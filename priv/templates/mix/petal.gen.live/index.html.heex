<.container class="py-16">
  <.page_header title="Listing <%= schema.human_plural %>">
    <.button link_type="live_patch" label="New <%= schema.human_singular %>" to={~p"<%= schema.route_prefix %>/new"} />
  </.page_header>

  <%%= if @live_action in [:new, :edit] do %>
    <.modal title={@page_title}>
      <.live_component
        module={<%= inspect context.web_module %>.<%= inspect Module.concat(schema.web_namespace, schema.alias) %>Live.FormComponent}
        id={@<%= schema.singular %>.id || :new}
        action={@live_action}
        <%= schema.singular %>={@<%= schema.singular %>}
        return_to={~p"<%= schema.route_prefix %>"}
      />
    </.modal>
  <%% end %>

  <.table id="<%= schema.plural %>" rows={@streams.<%= schema.collection %>}><%= for {col, _} <- schema.attrs do %>
    <:col :let={{_id, <%= schema.singular %>}} label="<%= Phoenix.Naming.humanize(Atom.to_string(col)) %>"><%%= <%= schema.singular %>.<%= col %> %></:col><% end %>
    <:col :let={{id, <%= schema.singular %>}} label="Actions" class="text-right whitespace-nowrap" row_class="text-right whitespace-nowrap">
      <.button
        color="primary"
        variant="outline"
        size="xs"
        link_type="live_redirect"
        label="Show"
        to={~p"<%= schema.route_prefix %>/#{<%= schema.singular %>}"}
      />

      <.button
        color="white"
        variant="outline"
        size="xs"
        link_type="live_patch"
        label="Edit"
        to={~p"<%= schema.route_prefix %>/#{<%= schema.singular %>}/edit"}
      />

      <.button
        color="danger"
        variant="outline"
        link_type="a"
        to="#"
        size="xs"
        label="Delete"
        phx-click={JS.push("delete", value: %{id: <%= schema.singular %>.id}) |> hide("##{id}")}
        data-confirm="Are you sure?"
      >
        Delete
      </.button>
    </:col>
  </.table>
</.container>
