# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     PetalPro.Repo.insert!(%PetalPro.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.
alias Boring.Google.Generator, as: Google
alias PetalPro.Accounts.User
alias PetalPro.Accounts.UserSeeder
alias PetalPro.Accounts.UserToken
alias PetalPro.Accounts.UserTOTP
alias PetalPro.Files.File
alias PetalPro.Files.FileSeeder
alias PetalPro.Logs.Log
alias PetalPro.Orgs.Invitation
alias PetalPro.Orgs.Membership
alias PetalPro.Orgs.Org
alias PetalPro.Posts.Post
alias PetalPro.Posts.PostSeeder

Boring.Repo.delete_all(Log)
Boring.Repo.delete_all(UserTOTP)
Boring.Repo.delete_all(Invitation)
Boring.Repo.delete_all(Membership)
Boring.Repo.delete_all(Org)
Boring.Repo.delete_all(Post)
Boring.Repo.delete_all(UserToken)
Boring.Repo.delete_all(User)
Boring.Repo.delete_all(File)

admin = UserSeeder.admin()

normal_user =
  UserSeeder.normal_user(%{
    email: "<EMAIL>",
    name: "Sarah Cunningham",
    password: "password",
    confirmed_at: Timex.to_naive_datetime(DateTime.utc_now())
  })

org =
  [actor: admin]
  |> Boring.Orgs.Generator.org()
  |> Ash.Generator.generate()

customer =
  [source: :org, org_id: org.id]
  |> Boring.Billing.Generator.customer()
  |> Ash.Generator.generate()

[billing_customer_id: customer.id, status: :active, plan_id: "pro-monthly"]
|> Boring.Billing.Generator.subscription()
|> Ash.Generator.generate()

PetalPro.Orgs.create_invitation(org, %{email: normal_user.email})

UserSeeder.random_users(20)

FileSeeder.create_files(admin)
PostSeeder.create_posts(admin)

# Pre-seed all State records
Boring.Locations.create_state!(Boring.Locations.Generator.all_states(),
  bulk_options: [return_errors?: true, stop_on_error?: false],
  authorize?: false
)

Ash.Generator.generate_many(Google.place_extract(), 10)

Boring.Google.Generator.place()
|> Ash.Generator.generate_many(150)
|> Ash.load!(state: [:name])
|> Enum.map(&[city: &1.city, state_name: &1.state.name])
|> Enum.uniq()
|> Enum.map(&Boring.Demographics.Generator.demographic/1)
|> Enum.each(&Ash.Generator.generate/1)

Ash.bulk_create(
  [%{name: :service_request_skiptrace, enabled?: true}],
  Boring.Features.Feature,
  :create,
  authorize?: false
)
