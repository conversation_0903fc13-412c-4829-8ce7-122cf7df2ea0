defmodule PetalPro.Repo.Migrations.OpportunityHasManyCloudFiles do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:cloud_files) do
      modify :orgs_opportunities_id, :uuid, null: true

      add :opportunity_id,
          references(:opportunities,
            column: :id,
            name: "cloud_files_opportunity_id_fkey",
            type: :uuid,
            prefix: "public"
          )
    end
  end

  def down do
    drop constraint(:cloud_files, "cloud_files_opportunity_id_fkey")

    alter table(:cloud_files) do
      remove :opportunity_id
      modify :orgs_opportunities_id, :uuid, null: false
    end
  end
end
