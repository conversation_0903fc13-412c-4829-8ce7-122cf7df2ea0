defmodule Boring.Repo.Migrations.AddRelationshipIndexBilling do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:billing_subscriptions, "billing_subscriptions_billing_customer_id_fkey")

    alter table(:billing_subscriptions) do
      modify :billing_customer_id,
             references(:billing_customers,
               column: :id,
               name: "billing_subscriptions_billing_customer_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:billing_subscriptions, [:billing_customer_id])

    drop constraint(:billing_meter_events, "billing_meter_events_billing_customer_id_fkey")

    alter table(:billing_meter_events) do
      modify :billing_customer_id,
             references(:billing_customers,
               column: :id,
               name: "billing_meter_events_billing_customer_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:billing_meter_events, [:billing_customer_id])

    drop constraint(:billing_customers, "billing_customers_org_id_fkey")

    drop constraint(:billing_customers, "billing_customers_user_id_fkey")

    alter table(:billing_customers) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "billing_customers_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "billing_customers_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:billing_customers, [:user_id])

    create index(:billing_customers, [:org_id])
  end

  def down do
    drop_if_exists index(:billing_customers, [:org_id])

    drop_if_exists index(:billing_customers, [:user_id])

    drop constraint(:billing_customers, "billing_customers_user_id_fkey")

    drop constraint(:billing_customers, "billing_customers_org_id_fkey")

    alter table(:billing_customers) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "billing_customers_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :user_id,
             references(:users,
               column: :id,
               name: "billing_customers_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    drop_if_exists index(:billing_meter_events, [:billing_customer_id])

    drop constraint(:billing_meter_events, "billing_meter_events_billing_customer_id_fkey")

    alter table(:billing_meter_events) do
      modify :billing_customer_id,
             references(:billing_customers,
               column: :id,
               name: "billing_meter_events_billing_customer_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    drop_if_exists index(:billing_subscriptions, [:billing_customer_id])

    drop constraint(:billing_subscriptions, "billing_subscriptions_billing_customer_id_fkey")

    alter table(:billing_subscriptions) do
      modify :billing_customer_id,
             references(:billing_customers,
               column: :id,
               name: "billing_subscriptions_billing_customer_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end
  end
end
