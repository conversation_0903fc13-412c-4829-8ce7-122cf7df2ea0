defmodule PetalPro.Repo.Migrations.UpdateContactUniqueIndexPartial do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop_if_exists unique_index(:contacts, [:org_id, :email],
                     name: "contacts_tenant_unique_email_index"
                   )

    drop_if_exists unique_index(:contacts, [:org_id, :phone_number],
                     name: "contacts_tenant_unique_phone_number_index"
                   )

    drop_if_exists unique_index(:contacts, [:org_id, :global_contact_id],
                     name: "contacts_tenant_unique_global_contact_index"
                   )

    create unique_index(:contacts, [:org_id, :email],
             name: "contacts_tenant_unique_email_index",
             where: "(\"skip_traced?\" = false) AND ((archived_at IS NULL))"
           )

    create unique_index(:contacts, [:org_id, :global_contact_id],
             where: "((archived_at IS NULL))",
             name: "contacts_tenant_unique_global_contact_index"
           )

    create unique_index(:contacts, [:org_id, :phone_number],
             name: "contacts_tenant_unique_phone_number_index",
             where: "(\"skip_traced?\" = false) AND ((archived_at IS NULL))"
           )
  end

  def down do
    drop_if_exists unique_index(:contacts, [:org_id, :phone_number],
                     name: "contacts_tenant_unique_phone_number_index"
                   )

    drop_if_exists unique_index(:contacts, [:org_id, :global_contact_id],
                     name: "contacts_tenant_unique_global_contact_index"
                   )

    drop_if_exists unique_index(:contacts, [:org_id, :email],
                     name: "contacts_tenant_unique_email_index"
                   )

    create unique_index(:contacts, [:org_id, :global_contact_id],
             name: "contacts_tenant_unique_global_contact_index"
           )

    create unique_index(:contacts, [:org_id, :phone_number],
             name: "contacts_tenant_unique_phone_number_index"
           )

    create unique_index(:contacts, [:org_id, :email], name: "contacts_tenant_unique_email_index")
  end
end
