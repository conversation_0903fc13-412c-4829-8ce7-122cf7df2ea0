defmodule PetalPro.Repo.Migrations.AddDemographicsGeoId do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:demographics, "demographics_state_id_fkey")

    alter table(:demographics) do
      add :geo_id, :text, null: true
    end

    drop_if_exists unique_index(:demographics, [:city, :state_id],
                     name: "demographics_unique_city_demographic_index"
                   )

    alter table(:demographics) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "demographics_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:demographics, [:state_id])

    create unique_index(:demographics, [:geo_id],
             name: "demographics_unique_city_demographic_index"
           )
  end

  def down do
    drop_if_exists unique_index(:demographics, [:geo_id],
                     name: "demographics_unique_city_demographic_index"
                   )

    drop_if_exists index(:demographics, [:state_id])

    drop constraint(:demographics, "demographics_state_id_fkey")

    alter table(:demographics) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "demographics_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create unique_index(:demographics, [:city, :state_id],
             name: "demographics_unique_city_demographic_index"
           )

    alter table(:demographics) do
      remove :geo_id
    end
  end
end
