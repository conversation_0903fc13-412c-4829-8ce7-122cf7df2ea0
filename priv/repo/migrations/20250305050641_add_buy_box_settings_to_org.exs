defmodule PetalPro.Repo.Migrations.AddBuyBoxSettingsToOrg do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:orgs) do
      add :buy_box_settings, :map
    end
  end

  def down do
    alter table(:orgs) do
      remove :buy_box_settings
    end
  end
end
