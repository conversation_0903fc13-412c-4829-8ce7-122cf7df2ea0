defmodule Boring.Repo.Migrations.AddRelationshipIndexFiles do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:files, "files_author_id_fkey")

    alter table(:files) do
      modify :author_id,
             references(:users,
               column: :id,
               name: "files_author_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:files, [:author_id])
  end

  def down do
    drop_if_exists index(:files, [:author_id])

    drop constraint(:files, "files_author_id_fkey")

    alter table(:files) do
      modify :author_id,
             references(:users,
               column: :id,
               name: "files_author_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end
  end
end
