defmodule Boring.Repo.Migrations.AddRawSkiptraceDataToOpportunities do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:opportunities) do
      add :raw_skiptrace_data, :map
    end
  end

  def down do
    alter table(:opportunities) do
      remove :raw_skiptrace_data
    end
  end
end
