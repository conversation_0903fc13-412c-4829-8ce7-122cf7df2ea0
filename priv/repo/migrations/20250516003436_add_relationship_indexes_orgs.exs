defmodule Boring.Repo.Migrations.AddRelationshipIndexesOrgs do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:orgs_memberships, "orgs_memberships_org_id_fkey")

    drop constraint(:orgs_memberships, "orgs_memberships_user_id_fkey")

    alter table(:orgs_memberships) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "orgs_memberships_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_memberships_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:orgs_memberships, [:org_id, :user_id])

    create index(:orgs_memberships, [:org_id])

    drop constraint(:orgs_invitations, "orgs_invitations_org_id_fkey")

    drop constraint(:orgs_invitations, "orgs_invitations_user_id_fkey")

    alter table(:orgs_invitations) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "orgs_invitations_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_invitations_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:orgs_invitations, [:org_id, :user_id])

    create index(:orgs_invitations, [:org_id])
  end

  def down do
    drop_if_exists index(:orgs_invitations, [:org_id])

    drop_if_exists index(:orgs_invitations, [:org_id, :user_id])

    drop constraint(:orgs_invitations, "orgs_invitations_user_id_fkey")

    drop constraint(:orgs_invitations, "orgs_invitations_org_id_fkey")

    alter table(:orgs_invitations) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_invitations_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :user_id,
             references(:users,
               column: :id,
               name: "orgs_invitations_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    drop_if_exists index(:orgs_memberships, [:org_id])

    drop_if_exists index(:orgs_memberships, [:org_id, :user_id])

    drop constraint(:orgs_memberships, "orgs_memberships_user_id_fkey")

    drop constraint(:orgs_memberships, "orgs_memberships_org_id_fkey")

    alter table(:orgs_memberships) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_memberships_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :user_id,
             references(:users,
               column: :id,
               name: "orgs_memberships_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end
  end
end
