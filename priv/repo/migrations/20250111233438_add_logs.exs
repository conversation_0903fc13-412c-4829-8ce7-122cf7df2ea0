defmodule PetalPro.Repo.Migrations.AddLogs do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:logs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :action, :text, null: false
      add :user_type, :text, null: false, default: "user"
      add :metadata, :map, default: %{}

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :user_id,
          references(:users,
            column: :id,
            name: "logs_user_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )

      add :target_user_id,
          references(:users,
            column: :id,
            name: "logs_target_user_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )
    end

    create index(:logs, [:user_type])

    create index(:logs, [:action])
  end

  def down do
    drop constraint(:logs, "logs_user_id_fkey")

    drop constraint(:logs, "logs_target_user_id_fkey")

    drop_if_exists index(:logs, [:action])

    drop_if_exists index(:logs, [:user_type])

    drop table(:logs)
  end
end
