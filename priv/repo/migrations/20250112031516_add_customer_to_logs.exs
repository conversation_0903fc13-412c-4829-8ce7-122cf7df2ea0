defmodule PetalPro.Repo.Migrations.AddCustomerToLogs do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:logs) do
      add :billing_customer_id,
          references(:billing_customers,
            column: :id,
            name: "logs_billing_customer_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )
    end
  end

  def down do
    drop constraint(:logs, "logs_billing_customer_id_fkey")

    alter table(:logs) do
      remove :billing_customer_id
    end
  end
end
