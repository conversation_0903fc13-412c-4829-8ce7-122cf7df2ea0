defmodule Boring.Repo.Migrations.AddRelationshipIndexesAcquisitions do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:orgs_opportunities, "orgs_opportunities_opportunity_id_fkey")

    drop constraint(:orgs_opportunities, "orgs_opportunities_org_id_fkey")

    alter table(:orgs_opportunities) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_opportunities_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "orgs_opportunities_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:orgs_opportunities, [:org_id])

    create index(:orgs_opportunities, [:org_id, :opportunity_id])

    drop constraint(:opportunities_contacts, "opportunities_contacts_opportunity_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_org_id_fkey")

    alter table(:opportunities_contacts) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "opportunities_contacts_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "opportunities_contacts_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:opportunities_contacts, [:org_id])

    create index(:opportunities_contacts, [:org_id, :contact_id])

    create index(:opportunities_contacts, [:org_id, :opportunity_id])

    drop constraint(:opportunities, "opportunities_state_id_fkey")

    alter table(:opportunities) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "opportunities_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:opportunities, [:state_id])

    drop constraint(:contacts, "contacts_global_contact_id_fkey")

    drop constraint(:contacts, "contacts_org_id_fkey")

    drop constraint(:contacts, "contacts_updated_by_id_fkey")

    drop constraint(:contacts, "contacts_created_by_id_fkey")

    alter table(:contacts) do
      modify :created_by_id,
             references(:users,
               column: :id,
               name: "contacts_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :updated_by_id,
             references(:users,
               column: :id,
               name: "contacts_updated_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "contacts_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :global_contact_id,
             references(:global_contacts,
               column: :id,
               name: "contacts_global_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :nilify_all
             )
    end

    create index(:contacts, [:org_id, :created_by_id])

    create index(:contacts, [:org_id, :updated_by_id])

    create index(:contacts, [:org_id])

    create index(:contacts, [:org_id, :global_contact_id])

    drop constraint(:cloud_files, "cloud_files_opportunity_id_fkey")

    drop constraint(:cloud_files, "cloud_files_org_id_fkey")

    alter table(:cloud_files) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "cloud_files_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "cloud_files_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:cloud_files, [:org_id])

    create index(:cloud_files, [:org_id, :opportunity_id])

    drop constraint(:buyboxes, "buyboxes_org_id_fkey")

    alter table(:buyboxes) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "buyboxes_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:buyboxes, [:org_id])
  end

  def down do
    drop_if_exists index(:buyboxes, [:org_id])

    drop constraint(:buyboxes, "buyboxes_org_id_fkey")

    alter table(:buyboxes) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "buyboxes_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    drop_if_exists index(:cloud_files, [:org_id, :opportunity_id])

    drop_if_exists index(:cloud_files, [:org_id])

    drop constraint(:cloud_files, "cloud_files_org_id_fkey")

    drop constraint(:cloud_files, "cloud_files_opportunity_id_fkey")

    alter table(:cloud_files) do
      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "cloud_files_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "cloud_files_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    drop_if_exists index(:contacts, [:org_id, :global_contact_id])

    drop_if_exists index(:contacts, [:org_id])

    drop_if_exists index(:contacts, [:org_id, :updated_by_id])

    drop_if_exists index(:contacts, [:org_id, :created_by_id])

    drop constraint(:contacts, "contacts_created_by_id_fkey")

    drop constraint(:contacts, "contacts_updated_by_id_fkey")

    drop constraint(:contacts, "contacts_org_id_fkey")

    drop constraint(:contacts, "contacts_global_contact_id_fkey")

    alter table(:contacts) do
      modify :global_contact_id,
             references(:global_contacts,
               column: :id,
               name: "contacts_global_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :nilify_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "contacts_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :updated_by_id,
             references(:users,
               column: :id,
               name: "contacts_updated_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :created_by_id,
             references(:users,
               column: :id,
               name: "contacts_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    drop_if_exists index(:opportunities, [:state_id])

    alter table(:opportunities) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "opportunities_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    drop_if_exists index(:opportunities_contacts, [:org_id, :opportunity_id])

    drop_if_exists index(:opportunities_contacts, [:org_id, :contact_id])

    drop_if_exists index(:opportunities_contacts, [:org_id])

    drop constraint(:opportunities_contacts, "opportunities_contacts_org_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_opportunity_id_fkey")

    alter table(:opportunities_contacts) do
      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "opportunities_contacts_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "opportunities_contacts_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    drop_if_exists index(:orgs_opportunities, [:org_id, :opportunity_id])

    drop_if_exists index(:orgs_opportunities, [:org_id])

    drop constraint(:orgs_opportunities, "orgs_opportunities_org_id_fkey")

    drop constraint(:orgs_opportunities, "orgs_opportunities_opportunity_id_fkey")

    alter table(:orgs_opportunities) do
      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "orgs_opportunities_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_opportunities_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end
  end
end
