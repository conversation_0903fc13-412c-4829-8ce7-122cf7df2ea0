defmodule PetalPro.Repo.Migrations.AddAerialImagePath do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:opportunities) do
      add :aerial_image_path, :text
    end
  end

  def down do
    alter table(:opportunities) do
      remove :aerial_image_path
    end
  end
end
