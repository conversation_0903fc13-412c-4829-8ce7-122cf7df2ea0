defmodule PetalPro.Repo.Migrations.AddPkToOrgOpportunities do
  use Ecto.Migration

  def up do
    # Drop existing primary key if there is one
    execute "ALTER TABLE orgs_opportunities DROP CONSTRAINT IF EXISTS orgs_opportunities_pkey"

    # Add primary key constraint to the id column
    execute "ALTER TABLE orgs_opportunities ADD PRIMARY KEY (id)"
  end

  def down do
    # Remove the primary key constraint
    execute "ALTER TABLE orgs_opportunities DROP CONSTRAINT IF EXISTS orgs_opportunities_pkey"

    # Create a composite primary key
    execute "ALTER TABLE orgs_opportunities ADD PRIMARY KEY (id, org_id, opportunity_id)"
  end
end
