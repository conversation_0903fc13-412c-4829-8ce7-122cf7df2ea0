defmodule PetalPro.Repo.Migrations.AddUserTokens do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:users_tokens, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :token, :binary, null: false
      add :context, :text, null: false
      add :sent_to, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :user_id,
          references(:users,
            column: :id,
            name: "users_tokens_user_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          null: false
    end

    create unique_index(:users_tokens, [:context, :token],
             name: "users_tokens_unique_token_index"
           )
  end

  def down do
    drop_if_exists unique_index(:users_tokens, [:context, :token],
                     name: "users_tokens_unique_token_index"
                   )

    drop constraint(:users_tokens, "users_tokens_user_id_fkey")

    drop table(:users_tokens)
  end
end
