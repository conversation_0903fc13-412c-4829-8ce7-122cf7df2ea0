defmodule PetalPro.Repo.Migrations.AddPosts do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:posts, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :category, :text
      add :title, :text, null: false
      add :slug, :text, null: false
      add :cover, :text
      add :cover_caption, :text
      add :summary, :text
      add :content, :text
      add :duration, :bigint
      add :published_category, :text
      add :published_title, :text
      add :published_slug, :text
      add :published_cover, :text
      add :published_cover_caption, :text
      add :published_summary, :text
      add :published_content, :text
      add :published_duration, :bigint
      add :last_published, :naive_datetime
      add :go_live, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :author_id,
          references(:users,
            column: :id,
            name: "posts_author_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false
    end
  end

  def down do
    drop constraint(:posts, "posts_author_id_fkey")

    drop table(:posts)
  end
end
