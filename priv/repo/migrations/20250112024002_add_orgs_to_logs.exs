defmodule PetalPro.Repo.Migrations.AddOrgsToLogs do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:logs) do
      add :org_id,
          references(:orgs,
            column: :id,
            name: "logs_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )
    end
  end

  def down do
    drop constraint(:logs, "logs_org_id_fkey")

    alter table(:logs) do
      remove :org_id
    end
  end
end
