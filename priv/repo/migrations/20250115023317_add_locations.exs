defmodule PetalPro.Repo.Migrations.AddLocations do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:states, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :name, :text, null: false
      add :code, :text, null: false
      add :country_code, :text, null: false, default: "US"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:states, [:code, :country_code],
             name: "states_unique_country_state_index"
           )
  end

  def down do
    drop_if_exists unique_index(:states, [:code, :country_code],
                     name: "states_unique_country_state_index"
                   )

    drop table(:states)
  end
end
