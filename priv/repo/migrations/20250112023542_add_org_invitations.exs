defmodule PetalPro.Repo.Migrations.AddOrgInvitations do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:orgs_invitations, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :email, :citext, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :user_id,
          references(:users,
            column: :id,
            name: "orgs_invitations_user_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )

      add :org_id,
          references(:orgs,
            column: :id,
            name: "orgs_invitations_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          null: false
    end

    create unique_index(:orgs_invitations, [:org_id, :org_id, :email],
             name: "orgs_invitations_unique_invitation_index"
           )
  end

  def down do
    drop_if_exists unique_index(:orgs_invitations, [:org_id, :org_id, :email],
                     name: "orgs_invitations_unique_invitation_index"
                   )

    drop constraint(:orgs_invitations, "orgs_invitations_user_id_fkey")

    drop constraint(:orgs_invitations, "orgs_invitations_org_id_fkey")

    drop table(:orgs_invitations)
  end
end
