defmodule Boring.Repo.Migrations.Wip do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:google_places) do
      modify :is_business_claimed, :boolean, default: true
    end
  end

  def down do
    alter table(:google_places) do
      modify :is_business_claimed, :boolean, default: false
    end
  end
end
