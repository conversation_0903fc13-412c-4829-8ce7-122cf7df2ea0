defmodule PetalPro.Repo.Migrations.BuyboxReferences do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:buyboxes_locations, "buyboxes_locations_buybox_id_fkey")

    alter table(:buyboxes_locations) do
      modify :buybox_id,
             references(:buyboxes,
               column: :id,
               name: "buyboxes_locations_buybox_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end
  end

  def down do
    drop constraint(:buyboxes_locations, "buyboxes_locations_buybox_id_fkey")

    alter table(:buyboxes_locations) do
      modify :buybox_id,
             references(:buyboxes,
               column: :id,
               name: "buyboxes_locations_buybox_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end
  end
end
