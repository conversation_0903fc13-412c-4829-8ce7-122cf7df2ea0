defmodule PetalPro.Repo.Migrations.AddBusinessClaimToPlace do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:google_places) do
      add :is_business_claimed, :boolean, default: false
    end

    drop constraint(:buyboxes_locations, "buyboxes_locations_buybox_id_fkey")

    alter table(:buyboxes_locations) do
      modify :buybox_id,
             references(:buyboxes,
               column: :id,
               name: "buyboxes_locations_buybox_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    alter table(:buyboxes) do
      modify :org_id, :uuid, null: true
    end

    drop_if_exists unique_index(:buyboxes, [:org_id], name: "buyboxes_unique_org_buybox_index")

    create unique_index(:buyboxes, [:org_id, :org_id], name: "buyboxes_unique_org_buybox_index")
  end

  def down do
    drop_if_exists unique_index(:buyboxes, [:org_id, :org_id],
                     name: "buyboxes_unique_org_buybox_index"
                   )

    create unique_index(:buyboxes, [:org_id, :org_id], name: "buyboxes_unique_org_buybox_index")

    alter table(:buyboxes) do
      modify :org_id, :uuid, null: false
    end

    drop constraint(:buyboxes_locations, "buyboxes_locations_buybox_id_fkey")

    alter table(:buyboxes_locations) do
      modify :buybox_id,
             references(:buyboxes,
               column: :id,
               name: "buyboxes_locations_buybox_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    alter table(:google_places) do
      remove :is_business_claimed
    end
  end
end
