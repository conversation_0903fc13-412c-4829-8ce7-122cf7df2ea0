defmodule PetalPro.Repo.Migrations.AddOpportunityAndOrgOpportunity do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:opportunities) do
      add :name, :text, null: false
      add :full_address, :text, null: false
      add :city, :text, null: false
      add :street, :text, null: false
      add :postal_code, :text, null: false
      add :latitude, :float, null: false
      add :longitude, :float, null: false
      add :website, :text
      add :map_url, :text, null: false
      add :phone_number, :text

      add :inserted_at, :utc_datetime,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime, null: false, default: fragment("(now() AT TIME ZONE 'utc')")

      add :google_place_id,
          references(:google_places,
            column: :id,
            name: "opportunities_google_place_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :state_id,
          references(:states,
            column: :id,
            name: "opportunities_state_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false
    end

    create unique_index(:opportunities, [:google_place_id],
             name: "opportunities_google_place_id_index"
           )

    create table(:orgs_opportunities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :status, :text, default: "new"

      add :org_id,
          references(:orgs,
            column: :id,
            name: "orgs_opportunities_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          null: false,
          primary_key: true

      add :opportunity_id,
          references(:opportunities,
            column: :id,
            name: "orgs_opportunities_opportunity_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :restrict
          ),
          null: false,
          primary_key: true

      add :notes, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end
  end

  def down do
    drop constraint(:orgs_opportunities, "orgs_opportunities_opportunity_id_fkey")

    drop constraint(:orgs_opportunities, "orgs_opportunities_org_id_fkey")

    drop table(:orgs_opportunities)

    drop_if_exists unique_index(:opportunities, [:google_place_id],
                     name: "opportunities_google_place_id_index"
                   )

    drop constraint(:opportunities, "opportunities_google_place_id_fkey")

    drop constraint(:opportunities, "opportunities_state_id_fkey")

    drop table(:opportunities)
  end
end
