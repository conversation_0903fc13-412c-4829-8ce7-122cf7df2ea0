defmodule PetalPro.Repo.Migrations.AddOpportunityContact do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:opportunities_contacts, primary_key: false) do
      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :contact_id,
          references(:contacts,
            column: :id,
            name: "opportunities_contacts_contact_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          primary_key: true,
          null: false

      add :opportunity_id,
          references(:opportunities,
            column: :id,
            name: "opportunities_contacts_opportunity_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          primary_key: true,
          null: false
    end
  end

  def down do
    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_opportunity_id_fkey")

    drop table(:opportunities_contacts)
  end
end
