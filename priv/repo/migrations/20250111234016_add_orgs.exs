defmodule PetalPro.Repo.Migrations.AddOrgs do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:orgs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :name, :text, null: false
      add :slug, :text, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:orgs, [:slug], name: "orgs_unique_slug_index")
  end

  def down do
    drop_if_exists unique_index(:orgs, [:slug], name: "orgs_unique_slug_index")

    drop table(:orgs)
  end
end
