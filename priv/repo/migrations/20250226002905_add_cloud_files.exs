defmodule PetalPro.Repo.Migrations.AddCloudFiles do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:cloud_files, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :remote_name, :text, null: false
      add :local_name, :text, null: false
      add :status, :text, null: false, default: "pending"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :orgs_opportunities_id,
          references(:orgs_opportunities,
            column: :id,
            name: "cloud_files_orgs_opportunities_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false
    end
  end

  def down do
    drop constraint(:cloud_files, "cloud_files_orgs_opportunities_id_fkey")

    drop table(:cloud_files)
  end
end
