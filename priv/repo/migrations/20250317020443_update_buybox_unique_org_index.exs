defmodule PetalPro.Repo.Migrations.UpdateBuyboxUniqueOrgIndex do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop_if_exists unique_index(:buyboxes, [:org_id, :org_id],
                     name: "buyboxes_unique_org_buybox_index"
                   )

    create unique_index(:buyboxes, [:org_id], name: "buyboxes_unique_org_buybox_index")
  end

  def down do
    drop_if_exists unique_index(:buyboxes, [:org_id, :org_id],
                     name: "buyboxes_unique_org_buybox_index"
                   )

    create unique_index(:buyboxes, [:org_id, :org_id], name: "buyboxes_unique_org_buybox_index")
  end
end
