defmodule PetalPro.Repo.Migrations.AddOrgOpportunityBaseFilterAshArchival do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop_if_exists unique_index(
                     :opportunities_contacts,
                     [:org_id, :contact_id, :opportunity_id, :role],
                     name: "opportunities_contacts_tenant_unique_opportunity_contact_index"
                   )

    create unique_index(:opportunities_contacts, [:org_id, :contact_id, :opportunity_id, :role],
             where: "((archived_at IS NULL))",
             name: "opportunities_contacts_tenant_unique_opportunity_contact_index"
           )
  end

  def down do
    drop_if_exists unique_index(
                     :opportunities_contacts,
                     [:org_id, :contact_id, :opportunity_id, :role],
                     name: "opportunities_contacts_tenant_unique_opportunity_contact_index"
                   )

    create unique_index(:opportunities_contacts, [:org_id, :contact_id, :opportunity_id, :role],
             name: "opportunities_contacts_tenant_unique_opportunity_contact_index"
           )
  end
end
