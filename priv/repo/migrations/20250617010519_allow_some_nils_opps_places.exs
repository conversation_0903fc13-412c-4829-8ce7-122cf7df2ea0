defmodule Boring.Repo.Migrations.AllowSomeNilsOppsPlaces do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:opportunities) do
      modify :map_url, :text, null: true
    end

    drop_if_exists index(:google_places, [:state_id])

    alter table(:google_places) do
      remove :state_id
      modify :customer_id, :text, null: true
    end
  end

  def down do
    alter table(:google_places) do
      modify :customer_id, :text, null: false

      add :state_id,
          references(:states,
            column: :id,
            name: "google_places_state_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :restrict
          ),
          null: false
    end

    create index(:google_places, [:state_id])

    alter table(:opportunities) do
      modify :map_url, :text, null: false
    end
  end
end
