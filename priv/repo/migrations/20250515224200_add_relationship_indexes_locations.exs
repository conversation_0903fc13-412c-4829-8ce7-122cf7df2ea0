defmodule Boring.Repo.Migrations.AddRelationshipIndexesLocations do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:orgs_locations, "orgs_locations_state_id_fkey")

    drop constraint(:orgs_locations, "orgs_locations_org_id_fkey")

    alter table(:orgs_locations) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_locations_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :state_id,
             references(:states,
               column: :id,
               name: "orgs_locations_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:orgs_locations, [:org_id])

    create index(:orgs_locations, [:state_id])

    drop constraint(:buyboxes_locations, "buyboxes_locations_state_id_fkey")

    drop constraint(:buyboxes_locations, "buyboxes_locations_buybox_id_fkey")

    alter table(:buyboxes_locations) do
      modify :buybox_id,
             references(:buyboxes,
               column: :id,
               name: "buyboxes_locations_buybox_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :state_id,
             references(:states,
               column: :id,
               name: "buyboxes_locations_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:buyboxes_locations, [:buybox_id])

    create index(:buyboxes_locations, [:state_id])
  end

  def down do
    drop_if_exists index(:buyboxes_locations, [:state_id])

    drop_if_exists index(:buyboxes_locations, [:buybox_id])

    drop constraint(:buyboxes_locations, "buyboxes_locations_buybox_id_fkey")

    drop constraint(:buyboxes_locations, "buyboxes_locations_state_id_fkey")

    alter table(:buyboxes_locations) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "buyboxes_locations_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :buybox_id,
             references(:buyboxes,
               column: :id,
               name: "buyboxes_locations_buybox_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    drop_if_exists index(:orgs_locations, [:state_id])

    drop_if_exists index(:orgs_locations, [:org_id])

    drop constraint(:orgs_locations, "orgs_locations_org_id_fkey")

    drop constraint(:orgs_locations, "orgs_locations_state_id_fkey")

    alter table(:orgs_locations) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "orgs_locations_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_locations_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end
  end
end
