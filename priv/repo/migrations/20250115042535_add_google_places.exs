defmodule PetalPro.Repo.Migrations.AddGooglePlaces do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:google_places, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :place_id, :text, null: false
      add :customer_id, :text, null: false
      add :maps_url, :text
      add :google_state, :text
      add :google_country, :text
      add :website, :text
      add :city, :text
      add :phone_number, :text
      add :business_name, :text
      add :street, :text
      add :review_score, :float, default: 0.0
      add :review_count, :bigint, default: 0
      add :postal_code, :text
      add :full_address, :text
      add :description, :text
      add :data_date, :utc_datetime
      add :longitude, :float
      add :latitude, :float
      add :permanently_closed, :boolean, default: false
      add :temporarily_closed, :boolean, default: false
      add :result_rank, :bigint
      add :categories, {:array, :text}, default: []
      add :additional_info, :map, default: %{}
      add :review_distribution, :map
      add :review_tags, {:array, :map}
      add :opening_hours, {:array, :map}

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :state_id,
          references(:states,
            column: :id,
            name: "google_places_state_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false
    end

    create unique_index(:google_places, [:place_id],
             name: "google_places_unique_google_place_index"
           )
  end

  def down do
    drop_if_exists unique_index(:google_places, [:place_id],
                     name: "google_places_unique_google_place_index"
                   )

    drop constraint(:google_places, "google_places_state_id_fkey")

    drop table(:google_places)
  end
end
