defmodule PetalPro.Repo.Migrations.AddFollowUpDatetime do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:orgs_opportunities) do
      add :follow_up, :utc_datetime
    end
  end

  def down do
    alter table(:orgs_opportunities) do
      remove :follow_up
    end
  end
end
