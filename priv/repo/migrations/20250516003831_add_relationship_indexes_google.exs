defmodule Boring.Repo.Migrations.AddRelationshipIndexesGoogle do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:google_places_extracts, "google_places_extracts_state_id_fkey")

    alter table(:google_places_extracts) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "google_places_extracts_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:google_places_extracts, [:state_id])

    drop constraint(:google_places, "google_places_state_id_fkey")

    alter table(:google_places) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "google_places_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:google_places, [:state_id])
  end

  def down do
    drop_if_exists index(:google_places, [:state_id])

    drop constraint(:google_places, "google_places_state_id_fkey")

    alter table(:google_places) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "google_places_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    drop_if_exists index(:google_places_extracts, [:state_id])

    drop constraint(:google_places_extracts, "google_places_extracts_state_id_fkey")

    alter table(:google_places_extracts) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "google_places_extracts_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end
  end
end
