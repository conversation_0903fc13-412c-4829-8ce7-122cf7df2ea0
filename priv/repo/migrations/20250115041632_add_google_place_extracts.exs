defmodule PetalPro.Repo.Migrations.AddGooglePlaceExtracts do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:google_places_extracts, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :interval, :bigint, null: false, default: 1
      add :interval_type, :text, null: false, default: "months"
      add :is_enabled, :boolean, default: true
      add :status, :text, null: false, default: "not_started"
      add :last_run_at, :utc_datetime
      add :last_actor_run_id, :text
      add :last_actor_run_started_at, :utc_datetime
      add :last_actor_run_finished_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :state_id,
          references(:states,
            column: :id,
            name: "google_places_extracts_state_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false
    end
  end

  def down do
    drop constraint(:google_places_extracts, "google_places_extracts_state_id_fkey")

    drop table(:google_places_extracts)
  end
end
