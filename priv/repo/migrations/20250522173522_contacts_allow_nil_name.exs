defmodule Boring.Repo.Migrations.ContactsAllowNilName do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:opportunities_contacts) do
      modify :role, :text, null: true
    end

    alter table(:global_contacts) do
      modify :name, :text, null: true
    end

    alter table(:contacts) do
      modify :name, :text, null: true
    end
  end

  def down do
    alter table(:contacts) do
      modify :name, :text, null: false
    end

    alter table(:global_contacts) do
      modify :name, :text, null: false
    end

    alter table(:opportunities_contacts) do
      modify :role, :text, null: false
    end
  end
end
