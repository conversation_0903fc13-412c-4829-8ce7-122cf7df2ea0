defmodule PetalPro.Repo.Migrations.RenameOrgOpportunityOnCloudFiles do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    rename table(:cloud_files), :orgs_opportunities_id, to: :org_opportunity_id

    drop constraint(:cloud_files, "cloud_files_orgs_opportunities_id_fkey")

    alter table(:cloud_files) do
      modify :org_opportunity_id,
             references(:orgs_opportunities,
               column: :id,
               name: "cloud_files_org_opportunity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end
  end

  def down do
    drop constraint(:cloud_files, "cloud_files_org_opportunity_id_fkey")

    alter table(:cloud_files) do
      modify :orgs_opportunities_id,
             references(:orgs_opportunities,
               column: :id,
               name: "cloud_files_orgs_opportunities_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    rename table(:cloud_files), :org_opportunity_id, to: :orgs_opportunities_id
  end
end
