defmodule PetalPro.Repo.Migrations.AddGooglePlaceReviews do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:google_place_reviews, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :review_id, :text, null: false
      add :name, :text
      add :text, :text
      add :published_at, :text
      add :published_at_date, :utc_datetime
      add :likes_count, :bigint
      add :review_url, :text
      add :reviewer_number_of_reviews, :bigint
      add :rating, :bigint
      add :response_from_owner_date, :utc_datetime
      add :response_from_owner_text, :text
      add :review_context, :map
      add :review_detailed_rating, :map
      add :visited_in, :text
      add :reviewer_photo_url, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :place_id,
          references(:google_places,
            column: :id,
            name: "google_place_reviews_place_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          null: false
    end

    create index(:google_place_reviews, [:published_at_date])

    create unique_index(:google_place_reviews, [:review_id],
             name: "google_place_reviews_unique_google_review_index"
           )
  end

  def down do
    drop_if_exists unique_index(:google_place_reviews, [:review_id],
                     name: "google_place_reviews_unique_google_review_index"
                   )

    drop constraint(:google_place_reviews, "google_place_reviews_place_id_fkey")

    drop_if_exists index(:google_place_reviews, [:published_at_date])

    drop table(:google_place_reviews)
  end
end
