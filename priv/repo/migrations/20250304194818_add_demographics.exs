defmodule PetalPro.Repo.Migrations.AddDemographics do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:demographics, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :city, :text, null: false
      add :population_estimate, :bigint, null: false
      add :median_household_income_estimate, :money_with_currency, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :state_id,
          references(:states,
            column: :id,
            name: "demographics_state_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :nilify_all
          ),
          null: false
    end

    create unique_index(:demographics, [:city, :state_id],
             name: "demographics_unique_city_demographic_index"
           )
  end

  def down do
    drop_if_exists unique_index(:demographics, [:city, :state_id],
                     name: "demographics_unique_city_demographic_index"
                   )

    drop constraint(:demographics, "demographics_state_id_fkey")

    drop table(:demographics)
  end
end
