defmodule PetalPro.Repo.Migrations.AddServiceRequests do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:service_requests, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :type, :text, null: false
      add :status, :text, null: false, default: "new"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :org_id,
          references(:orgs,
            column: :id,
            name: "service_requests_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :restrict
          )

      add :opportunity_id,
          references(:opportunities,
            column: :id,
            name: "service_requests_opportunity_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :restrict
          ),
          null: false

      add :created_by_id,
          references(:users,
            column: :id,
            name: "service_requests_created_by_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :restrict
          ),
          null: false
    end
  end

  def down do
    drop constraint(:service_requests, "service_requests_org_id_fkey")

    drop constraint(:service_requests, "service_requests_opportunity_id_fkey")

    drop constraint(:service_requests, "service_requests_created_by_id_fkey")

    drop table(:service_requests)
  end
end
