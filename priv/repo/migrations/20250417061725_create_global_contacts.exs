defmodule PetalPro.Repo.Migrations.CreateGlobalContacts do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    alter table(:opportunities_contacts) do
      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create table(:global_contacts, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :name, :text, null: false
      add :email, :citext, null: false
      add :phone_number, :text, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :opportunity_id,
          references(:opportunities,
            column: :id,
            name: "global_contacts_opportunity_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          null: false

      add :created_by_id,
          references(:users,
            column: :id,
            name: "global_contacts_created_by_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :restrict
          ),
          null: false

      add :updated_by_id,
          references(:users,
            column: :id,
            name: "global_contacts_updated_by_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :restrict
          ),
          null: false
    end

    rename table(:contacts), :source_contact_id, to: :global_contact_id

    drop constraint(:contacts, "contacts_source_contact_id_fkey")

    alter table(:contacts) do
      modify :global_contact_id,
             references(:global_contacts,
               column: :id,
               name: "contacts_global_contact_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end
  end

  def down do
    drop constraint(:contacts, "contacts_global_contact_id_fkey")

    alter table(:contacts) do
      modify :source_contact_id,
             references(:contacts,
               column: :id,
               name: "contacts_source_contact_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    rename table(:contacts), :global_contact_id, to: :source_contact_id

    drop constraint(:global_contacts, "global_contacts_opportunity_id_fkey")

    drop constraint(:global_contacts, "global_contacts_created_by_id_fkey")

    drop constraint(:global_contacts, "global_contacts_updated_by_id_fkey")

    drop table(:global_contacts)

    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    alter table(:opportunities_contacts) do
      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end
  end
end
