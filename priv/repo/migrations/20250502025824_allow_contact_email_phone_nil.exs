defmodule PetalPro.Repo.Migrations.AllowContactEmailPhoneNil do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:contacts) do
      modify :phone_number, :text, null: true
      modify :email, :citext, null: true
    end
  end

  def down do
    alter table(:contacts) do
      modify :email, :citext, null: false
      modify :phone_number, :text, null: false
    end
  end
end
