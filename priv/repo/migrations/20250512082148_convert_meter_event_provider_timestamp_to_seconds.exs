defmodule PetalPro.Repo.Migrations.ConvertMeterEventProviderTimestampToSeconds do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:billing_meter_events) do
      modify :provider_created_at, :utc_datetime
    end
  end

  def down do
    alter table(:billing_meter_events) do
      modify :provider_created_at, :utc_datetime_usec
    end
  end
end
