defmodule PetalPro.Repo.Migrations.AddOpportunityContactId do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    drop constraint("opportunities_contacts", "opportunities_contacts_pkey")

    alter table(:opportunities_contacts) do
      modify :opportunity_id, :uuid, primary_key: false

      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             ),
             primary_key: false
    end

    alter table(:opportunities_contacts) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
    end
  end

  def down do
    drop constraint("opportunities_contacts", "opportunities_contacts_pkey")

    alter table(:opportunities_contacts) do
      remove :id
    end

    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    alter table(:opportunities_contacts) do
      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             ),
             primary_key: true

      modify :opportunity_id, :uuid, primary_key: true
    end
  end
end
