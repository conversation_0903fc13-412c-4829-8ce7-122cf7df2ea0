defmodule PetalPro.Repo.Migrations.AddBuyboxes do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:buyboxes_locations, primary_key: false) do
      add :buybox_id, :uuid, null: false, primary_key: true
      add :state_id, :uuid, null: false, primary_key: true
    end

    create table(:buyboxes, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:buyboxes_locations) do
      modify :buybox_id,
             references(:buyboxes,
               column: :id,
               name: "buyboxes_locations_buybox_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :state_id,
             references(:states,
               column: :id,
               name: "buyboxes_locations_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    alter table(:buyboxes) do
      add :minimum_population, :bigint, null: false, default: 5000
      add :maximum_population, :bigint

      add :minimum_household_income, :money_with_currency,
        null: false,
        default: fragment("('USD', 30000)")

      add :maximum_household_income, :money_with_currency

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :org_id,
          references(:orgs,
            column: :id,
            name: "buyboxes_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          null: false
    end

    create unique_index(:buyboxes, [:org_id], name: "buyboxes_unique_org_buybox_index")
  end

  def down do
    drop_if_exists unique_index(:buyboxes, [:org_id], name: "buyboxes_unique_org_buybox_index")

    drop constraint(:buyboxes, "buyboxes_org_id_fkey")

    alter table(:buyboxes) do
      remove :org_id
      remove :updated_at
      remove :inserted_at
      remove :maximum_household_income
      remove :minimum_household_income
      remove :maximum_population
      remove :minimum_population
    end

    drop constraint(:buyboxes_locations, "buyboxes_locations_buybox_id_fkey")

    drop constraint(:buyboxes_locations, "buyboxes_locations_state_id_fkey")

    alter table(:buyboxes_locations) do
      modify :state_id, :uuid
      modify :buybox_id, :uuid
    end

    drop table(:buyboxes)

    drop table(:buyboxes_locations)
  end
end
