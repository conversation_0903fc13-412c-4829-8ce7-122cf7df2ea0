defmodule PetalPro.Repo.Migrations.AddUsers do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:users, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :name, :text
      add :email, :citext, null: false
      add :hashed_password, :text
      add :confirmed_at, :naive_datetime
      add :role, :text, null: false, default: "user"
      add :avatar, :text
      add :last_signed_in_ip, :text
      add :last_signed_in_datetime, :utc_datetime
      add :is_subscribed_to_marketing_notifications, :boolean, default: false
      add :is_suspended, :boolean, default: false
      add :is_deleted, :boolean, default: false
      add :is_onboarded, :boolean, default: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:users, [:email], name: "users_unique_email_index")
  end

  def down do
    drop_if_exists unique_index(:users, [:email], name: "users_unique_email_index")

    drop table(:users)
  end
end
