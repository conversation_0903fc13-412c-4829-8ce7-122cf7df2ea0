defmodule PetalPro.Repo.Migrations.MigrateToBuyboxes do
  @moduledoc """
  Migrates OrgLocation to Buybox and BuyboxLocation
  """

  use Ecto.Migration

  def up do
    execute """
      INSERT INTO buyboxes (org_id)
      SELECT DISTINCT id
      FROM orgs
    """

    execute """
      INSERT INTO buyboxes_locations (buybox_id, state_id)
      SELECT b.id, ol.state_id
      FROM orgs_locations ol
       JOIN buyboxes b ON ol.org_id = b.org_id;
    """
  end

  def down do
    execute """
      DELETE FROM buyboxes_locations;
      DELETE FROM buyboxes;
    """
  end
end
