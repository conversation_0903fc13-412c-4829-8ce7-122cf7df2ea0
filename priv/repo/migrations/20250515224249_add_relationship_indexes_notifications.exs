defmodule Boring.Repo.Migrations.AddRelationshipIndexesNotifications do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:user_notifications, "user_notifications_org_id_fkey")

    drop constraint(:user_notifications, "user_notifications_sender_id_fkey")

    drop constraint(:user_notifications, "user_notifications_recipient_id_fkey")

    alter table(:user_notifications) do
      modify :recipient_id,
             references(:users,
               column: :id,
               name: "user_notifications_recipient_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :sender_id,
             references(:users,
               column: :id,
               name: "user_notifications_sender_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "user_notifications_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:user_notifications, [:recipient_id])

    create index(:user_notifications, [:sender_id])

    create index(:user_notifications, [:org_id])
  end

  def down do
    drop_if_exists index(:user_notifications, [:org_id])

    drop_if_exists index(:user_notifications, [:sender_id])

    drop_if_exists index(:user_notifications, [:recipient_id])

    drop constraint(:user_notifications, "user_notifications_recipient_id_fkey")

    drop constraint(:user_notifications, "user_notifications_sender_id_fkey")

    drop constraint(:user_notifications, "user_notifications_org_id_fkey")

    alter table(:user_notifications) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "user_notifications_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :sender_id,
             references(:users,
               column: :id,
               name: "user_notifications_sender_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :recipient_id,
             references(:users,
               column: :id,
               name: "user_notifications_recipient_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end
  end
end
