defmodule PetalPro.Repo.Migrations.AddOrgMemberships do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:orgs_memberships, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true

      add :role, :text, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :user_id,
          references(:users,
            column: :id,
            name: "orgs_memberships_user_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          primary_key: true,
          null: false

      add :org_id,
          references(:orgs,
            column: :id,
            name: "orgs_memberships_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          primary_key: true,
          null: false
    end
  end

  def down do
    drop constraint(:orgs_memberships, "orgs_memberships_user_id_fkey")

    drop constraint(:orgs_memberships, "orgs_memberships_org_id_fkey")

    drop table(:orgs_memberships)
  end
end
