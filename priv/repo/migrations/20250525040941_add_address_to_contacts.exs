defmodule Boring.Repo.Migrations.AddAddressToContacts do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:global_contacts) do
      add :address, :map
    end

    alter table(:contacts) do
      add :address, :map
    end
  end

  def down do
    alter table(:contacts) do
      remove :address
    end

    alter table(:global_contacts) do
      remove :address
    end
  end
end
