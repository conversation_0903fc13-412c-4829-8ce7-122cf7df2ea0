defmodule PetalPro.Repo.Migrations.CleanupCloudFilesRelationships do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop_if_exists index(:orgs_opportunities, [:org_id, :opportunity_id])

    create unique_index(:orgs_opportunities, [:org_id, :opportunity_id],
             name: "orgs_opportunities_tenant_unique_opportunity_index"
           )

    alter table(:cloud_files) do
      remove :org_opportunity_id
    end

    drop constraint(:cloud_files, "cloud_files_opportunity_id_fkey")

    alter table(:cloud_files) do
      add :org_id,
          references(:orgs,
            column: :id,
            name: "cloud_files_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :restrict
          )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "cloud_files_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end
  end

  def down do
    drop constraint(:cloud_files, "cloud_files_org_id_fkey")

    drop constraint(:cloud_files, "cloud_files_opportunity_id_fkey")

    alter table(:cloud_files) do
      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "cloud_files_opportunity_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      remove :org_id
    end

    alter table(:cloud_files) do
      add :org_opportunity_id,
          references(:orgs_opportunities,
            column: :id,
            name: "cloud_files_org_opportunity_id_fkey",
            type: :uuid,
            prefix: "public"
          )
    end

    drop_if_exists unique_index(:orgs_opportunities, [:org_id, :opportunity_id],
                     name: "orgs_opportunities_tenant_unique_opportunity_index"
                   )

    create index(:orgs_opportunities, [:org_id, :opportunity_id], unique: true)
  end
end
