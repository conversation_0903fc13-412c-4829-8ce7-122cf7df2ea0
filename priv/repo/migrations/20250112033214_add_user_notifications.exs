defmodule PetalPro.Repo.Migrations.AddUserNotifications do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:user_notifications, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :type, :text, null: false
      add :message, :text
      add :read_at, :utc_datetime
      add :read_path, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :recipient_id,
          references(:users,
            column: :id,
            name: "user_notifications_recipient_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          null: false

      add :sender_id,
          references(:users,
            column: :id,
            name: "user_notifications_sender_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )

      add :org_id,
          references(:orgs,
            column: :id,
            name: "user_notifications_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )
    end

    create index(:user_notifications, [:recipient_id, :read_path, :read_at])

    create index(:user_notifications, [:recipient_id, :read_at])
  end

  def down do
    drop constraint(:user_notifications, "user_notifications_recipient_id_fkey")

    drop constraint(:user_notifications, "user_notifications_sender_id_fkey")

    drop constraint(:user_notifications, "user_notifications_org_id_fkey")

    drop_if_exists index(:user_notifications, [:recipient_id, :read_at])

    drop_if_exists index(:user_notifications, [:recipient_id, :read_path, :read_at])

    drop table(:user_notifications)
  end
end
