defmodule PetalPro.Repo.Migrations.FixOrgOpportunityMultiTenancy do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:orgs_opportunities) do
      modify :org_id, :uuid, null: true
    end
  end

  def down do
    alter table(:orgs_opportunities) do
      modify :org_id, :uuid, null: false
    end
  end
end
