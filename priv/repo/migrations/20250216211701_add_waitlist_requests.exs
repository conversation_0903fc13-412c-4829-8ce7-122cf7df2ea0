defmodule PetalPro.Repo.Migrations.Wip do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:waitlist_requests, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :email, :citext, null: false
      add :name, :text, null: false
      add :interest_reason, :text, null: false
      add :testimonial?, :boolean, null: false
      add :lead_contacts_per_week, :text, null: false
      add :report_feedback?, :boolean, null: false
      add :preferred_regions, {:array, :text}, null: false, default: []
      add :invited_at, :utc_datetime, default: nil

      add :inserted_at, :utc_datetime,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime, null: false, default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:waitlist_requests, [:email],
             name: "waitlist_requests_unique_email_index"
           )
  end

  def down do
    drop_if_exists unique_index(:waitlist_requests, [:email],
                     name: "waitlist_requests_unique_email_index"
                   )

    drop table(:waitlist_requests)
  end
end
