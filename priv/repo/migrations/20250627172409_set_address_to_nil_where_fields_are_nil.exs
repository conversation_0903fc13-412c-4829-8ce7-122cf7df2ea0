defmodule Boring.Repo.Migrations.SetAddressToNilWhereFieldsAreNil do
  use Ecto.Migration

  def up do
    # Set address to nil for global_contacts where any address field is nil
    execute """
    UPDATE global_contacts
    SET address = NULL
    WHERE address IS NOT NULL
    AND (
      address->>'street' IS NULL OR
      address->>'city' IS NULL OR
      address->>'state' IS NULL OR
      address->>'postal_code' IS NULL
    )
    """

    # Set address to nil for contacts where any address field is nil
    execute """
    UPDATE contacts
    SET address = NULL
    WHERE address IS NOT NULL
    AND (
      address->>'street' IS NULL OR
      address->>'city' IS NULL OR
      address->>'state' IS NULL OR
      address->>'postal_code' IS NULL
    )
    """
  end

  def down do
    # This migration cannot be reversed as we're removing data
    # The original nil values in address fields are lost
    :ok
  end
end
