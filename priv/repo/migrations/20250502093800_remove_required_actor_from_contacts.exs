defmodule PetalPro.Repo.Migrations.RemoveRequiredActorFromContacts do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:contacts) do
      modify :updated_by_id, :uuid, null: true
      modify :created_by_id, :uuid, null: true
    end
  end

  def down do
    alter table(:contacts) do
      modify :created_by_id, :uuid, null: false
      modify :updated_by_id, :uuid, null: false
    end
  end
end
