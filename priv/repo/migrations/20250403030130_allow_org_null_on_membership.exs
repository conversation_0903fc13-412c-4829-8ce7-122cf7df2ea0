defmodule PetalPro.Repo.Migrations.AllowOrgNullOnMembership do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint("orgs_memberships", "orgs_memberships_pkey")

    alter table(:orgs_memberships) do
      modify :org_id, :uuid, null: true
      modify :user_id, :uuid
    end

    execute("ALTER TABLE \"orgs_memberships\" ADD PRIMARY KEY (id)")
  end

  def down do
    alter table(:orgs_memberships) do
      modify :user_id, :uuid
      modify :org_id, :uuid, null: false
    end

    execute("ALTER TABLE \"orgs_memberships\" DROP constraint orgs_memberships_pkey")

    execute("ALTER TABLE \"orgs_memberships\" ADD PRIMARY KEY (id, user_id, org_id)")
  end
end
