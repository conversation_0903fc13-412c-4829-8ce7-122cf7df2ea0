defmodule PetalPro.Repo.Migrations.RestrictStateDeletionForDemographics do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:demographics, "demographics_state_id_fkey")

    alter table(:demographics) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "demographics_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end
  end

  def down do
    drop constraint(:demographics, "demographics_state_id_fkey")

    alter table(:demographics) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "demographics_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :nilify_all
             )
    end
  end
end
