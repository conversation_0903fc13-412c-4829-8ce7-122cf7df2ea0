defmodule PetalPro.Repo.Migrations.AddBillingTables do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:billing_subscriptions, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :status, :text, null: false
      add :plan_id, :text, null: false
      add :provider_subscription_id, :text, null: false
      add :provider_subscription_items, {:array, :map}, null: false
      add :cancel_at, :naive_datetime
      add :canceled_at, :naive_datetime
      add :current_period_end_at, :naive_datetime
      add :current_period_start, :naive_datetime, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :billing_customer_id, :uuid, null: false
    end

    create table(:billing_customers, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
    end

    alter table(:billing_subscriptions) do
      modify :billing_customer_id,
             references(:billing_customers,
               column: :id,
               name: "billing_subscriptions_billing_customer_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    alter table(:billing_customers) do
      add :email, :citext, null: false
      add :provider, :text, null: false
      add :provider_customer_id, :text, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :user_id,
          references(:users,
            column: :id,
            name: "billing_customers_user_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )

      add :org_id,
          references(:orgs,
            column: :id,
            name: "billing_customers_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )
    end

    create index(:billing_customers, [:provider])
  end

  def down do
    drop constraint(:billing_customers, "billing_customers_user_id_fkey")

    drop constraint(:billing_customers, "billing_customers_org_id_fkey")

    drop_if_exists index(:billing_customers, [:provider])

    alter table(:billing_customers) do
      remove :org_id
      remove :user_id
      remove :updated_at
      remove :inserted_at
      remove :provider_customer_id
      remove :provider
      remove :email
    end

    drop constraint(:billing_subscriptions, "billing_subscriptions_billing_customer_id_fkey")

    alter table(:billing_subscriptions) do
      modify :billing_customer_id, :uuid
    end

    drop table(:billing_customers)

    drop table(:billing_subscriptions)
  end
end
