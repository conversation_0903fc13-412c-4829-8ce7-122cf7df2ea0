defmodule Boring.Repo.Migrations.DropBuyboxTables do
  @moduledoc """
  Drop buybox-related tables as part of buybox feature removal.

  This migration removes the buyboxes and buyboxes_locations tables that were used 
  for automatic opportunity assignment based on investment criteria. The buybox
  feature has been removed in favor of manual opportunity assignment through
  the Opportunity Search interface.

  Tables dropped:
  1. buyboxes_locations (first, to remove foreign key constraint)
  2. buyboxes (second, after dependent table is removed)
  """
  use Ecto.Migration

  def up do
    # Drop foreign key constraints from buyboxes_locations first
    drop_if_exists constraint(:buyboxes_locations, "buyboxes_locations_buybox_id_fkey")
    drop_if_exists constraint(:buyboxes_locations, "buyboxes_locations_state_id_fkey")

    # Drop buyboxes_locations table (now has no foreign key constraints)
    drop table(:buyboxes_locations)

    # Drop buyboxes table (no longer has dependent objects)
    drop table(:buyboxes)
  end

  def down do
    # Recreate buyboxes table first (referenced by buyboxes_locations)
    create table(:buyboxes, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :minimum_population, :bigint, null: false, default: 5000
      add :maximum_population, :bigint

      add :minimum_household_income, :money_with_currency,
        null: false,
        default: fragment("('USD', 30000)")

      add :maximum_household_income, :money_with_currency

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :org_id,
          references(:orgs,
            column: :id,
            name: "buyboxes_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          null: false
    end

    # Recreate buyboxes indexes
    create unique_index(:buyboxes, [:org_id], name: "buyboxes_unique_org_buybox_index")

    # Recreate buyboxes_locations table second (references buyboxes)
    create table(:buyboxes_locations, primary_key: false) do
      add :buybox_id, :uuid, null: false, primary_key: true
      add :state_id, :uuid, null: false, primary_key: true
    end

    # Recreate buyboxes_locations foreign key constraints
    alter table(:buyboxes_locations) do
      modify :buybox_id,
             references(:buyboxes,
               column: :id,
               name: "buyboxes_locations_buybox_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :state_id,
             references(:states,
               column: :id,
               name: "buyboxes_locations_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end
  end
end
