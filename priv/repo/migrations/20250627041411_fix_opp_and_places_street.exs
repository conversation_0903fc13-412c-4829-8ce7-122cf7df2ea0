defmodule Boring.Repo.Migrations.FixOppAndPlacesStreet do
  use Ecto.Migration

  def change do
    execute """
    UPDATE google_places
    SET street = TRIM(SPLIT_PART(full_address, ',', 1))
    WHERE full_address IS NOT NULL
    AND full_address != '';
    """

    execute """
    UPDATE opportunities
    SET street = TRIM(SPLIT_PART(full_address, ',', 1))
    WHERE full_address IS NOT NULL
    AND full_address != '';
    """
  end
end
