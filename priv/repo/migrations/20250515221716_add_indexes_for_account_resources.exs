defmodule Boring.Repo.Migrations.AddIndexesForAccountResources do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:users_totps, "users_totps_user_id_fkey")

    alter table(:users_totps) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_totps_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:users_totps, [:user_id])

    drop constraint(:users_tokens, "users_tokens_user_id_fkey")

    alter table(:users_tokens) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_tokens_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:users_tokens, [:user_id])

    drop constraint(:users_pins, "users_pins_user_id_fkey")

    alter table(:users_pins) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_pins_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:users_pins, [:user_id])
  end

  def down do
    drop_if_exists index(:users_pins, [:user_id])

    drop constraint(:users_pins, "users_pins_user_id_fkey")

    alter table(:users_pins) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_pins_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    drop_if_exists index(:users_tokens, [:user_id])

    drop constraint(:users_tokens, "users_tokens_user_id_fkey")

    alter table(:users_tokens) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_tokens_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    drop_if_exists index(:users_totps, [:user_id])

    drop constraint(:users_totps, "users_totps_user_id_fkey")

    alter table(:users_totps) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_totps_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end
  end
end
