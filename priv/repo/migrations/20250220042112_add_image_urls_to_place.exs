defmodule PetalPro.Repo.Migrations.AddImageUrlsToPlace do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:google_places) do
      add :image_urls, {:array, :text}
    end
  end

  def down do
    alter table(:google_places) do
      remove :image_urls
    end
  end
end
