defmodule PetalPro.Repo.Migrations.AddSkipTracedToContacts do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:contacts, "contacts_global_contact_id_fkey")

    alter table(:contacts) do
      add :skip_traced?, :boolean, default: false

      modify :global_contact_id,
             references(:global_contacts,
               column: :id,
               name: "contacts_global_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :nilify_all
             )
    end

    create unique_index(:contacts, [:org_id, :global_contact_id],
             name: "contacts_tenant_unique_global_contact_index"
           )
  end

  def down do
    drop_if_exists unique_index(:contacts, [:org_id, :global_contact_id],
                     name: "contacts_tenant_unique_global_contact_index"
                   )

    drop constraint(:contacts, "contacts_global_contact_id_fkey")

    alter table(:contacts) do
      modify :global_contact_id,
             references(:global_contacts,
               column: :id,
               name: "contacts_global_contact_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      remove :skip_traced?
    end
  end
end
