defmodule PetalPro.Repo.Migrations.OpportunityContactReferences do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    alter table(:opportunities_contacts) do
      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end
  end

  def down do
    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    alter table(:opportunities_contacts) do
      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end
  end
end
