defmodule PetalPro.Repo.Migrations.AddStateNameCodeIdentities do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create unique_index(:states, [:code], name: "states_state_code_index")

    create unique_index(:states, [:name], name: "states_state_name_index")
  end

  def down do
    drop_if_exists unique_index(:states, [:name], name: "states_state_name_index")

    drop_if_exists unique_index(:states, [:code], name: "states_state_code_index")
  end
end
