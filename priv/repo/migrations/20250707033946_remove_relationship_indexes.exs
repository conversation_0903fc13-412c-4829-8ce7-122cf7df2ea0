defmodule Boring.Repo.Migrations.RemoveRelationshipIndexes do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop_if_exists index(:users_totps, [:user_id])

    drop constraint(:users_totps, "users_totps_user_id_fkey")

    alter table(:users_totps) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_totps_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:users_totps, [:user_id])

    drop_if_exists index(:users_tokens, [:user_id])

    drop constraint(:users_tokens, "users_tokens_user_id_fkey")

    alter table(:users_tokens) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_tokens_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:users_tokens, [:user_id])

    drop_if_exists index(:users_pins, [:user_id])

    drop constraint(:users_pins, "users_pins_user_id_fkey")

    alter table(:users_pins) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_pins_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:users_pins, [:user_id])

    drop_if_exists index(:user_notifications, [:org_id])

    drop_if_exists index(:user_notifications, [:sender_id])

    drop_if_exists index(:user_notifications, [:recipient_id])

    drop constraint(:user_notifications, "user_notifications_org_id_fkey")

    drop constraint(:user_notifications, "user_notifications_sender_id_fkey")

    drop constraint(:user_notifications, "user_notifications_recipient_id_fkey")

    alter table(:user_notifications) do
      modify :recipient_id,
             references(:users,
               column: :id,
               name: "user_notifications_recipient_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :sender_id,
             references(:users,
               column: :id,
               name: "user_notifications_sender_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "user_notifications_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:user_notifications, [:recipient_id])

    create index(:user_notifications, [:sender_id])

    create index(:user_notifications, [:org_id])

    drop_if_exists index(:service_requests, [:org_id, :created_by_id])

    drop_if_exists index(:service_requests, [:org_id, :opportunity_id])

    drop_if_exists index(:service_requests, [:org_id])

    drop constraint(:service_requests, "service_requests_created_by_id_fkey")

    drop constraint(:service_requests, "service_requests_opportunity_id_fkey")

    drop constraint(:service_requests, "service_requests_org_id_fkey")

    alter table(:service_requests) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "service_requests_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "service_requests_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :created_by_id,
             references(:users,
               column: :id,
               name: "service_requests_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:service_requests, [:org_id])

    create index(:service_requests, [:org_id, :opportunity_id])

    create index(:service_requests, [:org_id, :created_by_id])

    drop_if_exists index(:orgs_opportunities, [:org_id, :opportunity_id])

    drop_if_exists index(:orgs_opportunities, [:org_id])

    drop constraint(:orgs_opportunities, "orgs_opportunities_opportunity_id_fkey")

    drop constraint(:orgs_opportunities, "orgs_opportunities_org_id_fkey")

    alter table(:orgs_opportunities) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_opportunities_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "orgs_opportunities_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:orgs_opportunities, [:org_id])

    create index(:orgs_opportunities, [:org_id, :opportunity_id])

    drop_if_exists index(:orgs_memberships, [:org_id])

    drop_if_exists index(:orgs_memberships, [:org_id, :user_id])

    drop constraint(:orgs_memberships, "orgs_memberships_org_id_fkey")

    drop constraint(:orgs_memberships, "orgs_memberships_user_id_fkey")

    alter table(:orgs_memberships) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "orgs_memberships_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_memberships_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:orgs_memberships, [:org_id, :user_id])

    create index(:orgs_memberships, [:org_id])

    drop_if_exists index(:orgs_invitations, [:org_id])

    drop_if_exists index(:orgs_invitations, [:org_id, :user_id])

    drop constraint(:orgs_invitations, "orgs_invitations_org_id_fkey")

    drop constraint(:orgs_invitations, "orgs_invitations_user_id_fkey")

    alter table(:orgs_invitations) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "orgs_invitations_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_invitations_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:orgs_invitations, [:org_id, :user_id])

    create index(:orgs_invitations, [:org_id])

    drop_if_exists index(:opportunities_contacts, [:org_id, :opportunity_id])

    drop_if_exists index(:opportunities_contacts, [:org_id, :contact_id])

    drop_if_exists index(:opportunities_contacts, [:org_id])

    drop constraint(:opportunities_contacts, "opportunities_contacts_opportunity_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_org_id_fkey")

    alter table(:opportunities_contacts) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "opportunities_contacts_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "opportunities_contacts_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:opportunities_contacts, [:org_id])

    create index(:opportunities_contacts, [:org_id, :contact_id])

    create index(:opportunities_contacts, [:org_id, :opportunity_id])

    drop_if_exists index(:opportunities, [:state_id])

    drop constraint(:opportunities, "opportunities_state_id_fkey")

    alter table(:opportunities) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "opportunities_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:opportunities, [:state_id])

    drop_if_exists index(:google_places_extracts, [:state_id])

    drop constraint(:google_places_extracts, "google_places_extracts_state_id_fkey")

    alter table(:google_places_extracts) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "google_places_extracts_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:google_places_extracts, [:state_id])

    drop_if_exists index(:global_contacts, [:updated_by_id])

    drop_if_exists index(:global_contacts, [:created_by_id])

    drop_if_exists index(:global_contacts, [:opportunity_id])

    drop constraint(:global_contacts, "global_contacts_updated_by_id_fkey")

    drop constraint(:global_contacts, "global_contacts_created_by_id_fkey")

    drop constraint(:global_contacts, "global_contacts_opportunity_id_fkey")

    alter table(:global_contacts) do
      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "global_contacts_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :created_by_id,
             references(:users,
               column: :id,
               name: "global_contacts_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :updated_by_id,
             references(:users,
               column: :id,
               name: "global_contacts_updated_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:global_contacts, [:opportunity_id])

    create index(:global_contacts, [:created_by_id])

    create index(:global_contacts, [:updated_by_id])

    drop_if_exists index(:files, [:author_id])

    drop constraint(:files, "files_author_id_fkey")

    alter table(:files) do
      modify :author_id,
             references(:users,
               column: :id,
               name: "files_author_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:files, [:author_id])

    drop_if_exists index(:demographics, [:state_id])

    drop constraint(:demographics, "demographics_state_id_fkey")

    alter table(:demographics) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "demographics_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:demographics, [:state_id])

    drop_if_exists index(:contacts, [:org_id, :global_contact_id])

    drop_if_exists index(:contacts, [:org_id])

    drop_if_exists index(:contacts, [:org_id, :updated_by_id])

    drop_if_exists index(:contacts, [:org_id, :created_by_id])

    drop constraint(:contacts, "contacts_global_contact_id_fkey")

    drop constraint(:contacts, "contacts_org_id_fkey")

    drop constraint(:contacts, "contacts_updated_by_id_fkey")

    drop constraint(:contacts, "contacts_created_by_id_fkey")

    alter table(:contacts) do
      modify :created_by_id,
             references(:users,
               column: :id,
               name: "contacts_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :updated_by_id,
             references(:users,
               column: :id,
               name: "contacts_updated_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "contacts_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :global_contact_id,
             references(:global_contacts,
               column: :id,
               name: "contacts_global_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :nilify_all
             )
    end

    create index(:contacts, [:org_id, :created_by_id])

    create index(:contacts, [:org_id, :updated_by_id])

    create index(:contacts, [:org_id])

    create index(:contacts, [:org_id, :global_contact_id])

    drop_if_exists index(:cloud_files, [:org_id, :opportunity_id])

    drop_if_exists index(:cloud_files, [:org_id])

    drop constraint(:cloud_files, "cloud_files_opportunity_id_fkey")

    drop constraint(:cloud_files, "cloud_files_org_id_fkey")

    alter table(:cloud_files) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "cloud_files_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "cloud_files_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:cloud_files, [:org_id])

    create index(:cloud_files, [:org_id, :opportunity_id])

    drop_if_exists index(:billing_subscriptions, [:billing_customer_id])

    drop constraint(:billing_subscriptions, "billing_subscriptions_billing_customer_id_fkey")

    alter table(:billing_subscriptions) do
      modify :billing_customer_id,
             references(:billing_customers,
               column: :id,
               name: "billing_subscriptions_billing_customer_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:billing_subscriptions, [:billing_customer_id])

    drop_if_exists index(:billing_meter_events, [:billing_customer_id])

    drop constraint(:billing_meter_events, "billing_meter_events_billing_customer_id_fkey")

    alter table(:billing_meter_events) do
      modify :billing_customer_id,
             references(:billing_customers,
               column: :id,
               name: "billing_meter_events_billing_customer_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:billing_meter_events, [:billing_customer_id])

    drop_if_exists index(:billing_customers, [:org_id])

    drop_if_exists index(:billing_customers, [:user_id])

    drop constraint(:billing_customers, "billing_customers_org_id_fkey")

    drop constraint(:billing_customers, "billing_customers_user_id_fkey")

    alter table(:billing_customers) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "billing_customers_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "billing_customers_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:billing_customers, [:user_id])

    create index(:billing_customers, [:org_id])
  end

  def down do
    drop_if_exists index(:billing_customers, [:org_id])

    drop_if_exists index(:billing_customers, [:user_id])

    drop constraint(:billing_customers, "billing_customers_user_id_fkey")

    drop constraint(:billing_customers, "billing_customers_org_id_fkey")

    alter table(:billing_customers) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "billing_customers_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :user_id,
             references(:users,
               column: :id,
               name: "billing_customers_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:billing_customers, [:user_id])

    create index(:billing_customers, [:org_id])

    drop_if_exists index(:billing_meter_events, [:billing_customer_id])

    drop constraint(:billing_meter_events, "billing_meter_events_billing_customer_id_fkey")

    alter table(:billing_meter_events) do
      modify :billing_customer_id,
             references(:billing_customers,
               column: :id,
               name: "billing_meter_events_billing_customer_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:billing_meter_events, [:billing_customer_id])

    drop_if_exists index(:billing_subscriptions, [:billing_customer_id])

    drop constraint(:billing_subscriptions, "billing_subscriptions_billing_customer_id_fkey")

    alter table(:billing_subscriptions) do
      modify :billing_customer_id,
             references(:billing_customers,
               column: :id,
               name: "billing_subscriptions_billing_customer_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:billing_subscriptions, [:billing_customer_id])

    drop_if_exists index(:cloud_files, [:org_id, :opportunity_id])

    drop_if_exists index(:cloud_files, [:org_id])

    drop constraint(:cloud_files, "cloud_files_org_id_fkey")

    drop constraint(:cloud_files, "cloud_files_opportunity_id_fkey")

    alter table(:cloud_files) do
      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "cloud_files_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "cloud_files_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:cloud_files, [:org_id])

    create index(:cloud_files, [:org_id, :opportunity_id])

    drop_if_exists index(:contacts, [:org_id, :global_contact_id])

    drop_if_exists index(:contacts, [:org_id])

    drop_if_exists index(:contacts, [:org_id, :updated_by_id])

    drop_if_exists index(:contacts, [:org_id, :created_by_id])

    drop constraint(:contacts, "contacts_created_by_id_fkey")

    drop constraint(:contacts, "contacts_updated_by_id_fkey")

    drop constraint(:contacts, "contacts_org_id_fkey")

    drop constraint(:contacts, "contacts_global_contact_id_fkey")

    alter table(:contacts) do
      modify :global_contact_id,
             references(:global_contacts,
               column: :id,
               name: "contacts_global_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :nilify_all
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "contacts_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :updated_by_id,
             references(:users,
               column: :id,
               name: "contacts_updated_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :created_by_id,
             references(:users,
               column: :id,
               name: "contacts_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:contacts, [:org_id, :created_by_id])

    create index(:contacts, [:org_id, :updated_by_id])

    create index(:contacts, [:org_id])

    create index(:contacts, [:org_id, :global_contact_id])

    drop_if_exists index(:demographics, [:state_id])

    drop constraint(:demographics, "demographics_state_id_fkey")

    alter table(:demographics) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "demographics_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:demographics, [:state_id])

    drop_if_exists index(:files, [:author_id])

    drop constraint(:files, "files_author_id_fkey")

    alter table(:files) do
      modify :author_id,
             references(:users,
               column: :id,
               name: "files_author_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:files, [:author_id])

    drop_if_exists index(:global_contacts, [:updated_by_id])

    drop_if_exists index(:global_contacts, [:created_by_id])

    drop_if_exists index(:global_contacts, [:opportunity_id])

    drop constraint(:global_contacts, "global_contacts_opportunity_id_fkey")

    drop constraint(:global_contacts, "global_contacts_created_by_id_fkey")

    drop constraint(:global_contacts, "global_contacts_updated_by_id_fkey")

    alter table(:global_contacts) do
      modify :updated_by_id,
             references(:users,
               column: :id,
               name: "global_contacts_updated_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :created_by_id,
             references(:users,
               column: :id,
               name: "global_contacts_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "global_contacts_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:global_contacts, [:opportunity_id])

    create index(:global_contacts, [:created_by_id])

    create index(:global_contacts, [:updated_by_id])

    drop_if_exists index(:google_places_extracts, [:state_id])

    drop constraint(:google_places_extracts, "google_places_extracts_state_id_fkey")

    alter table(:google_places_extracts) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "google_places_extracts_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:google_places_extracts, [:state_id])

    drop_if_exists index(:opportunities, [:state_id])

    drop constraint(:opportunities, "opportunities_state_id_fkey")

    alter table(:opportunities) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "opportunities_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:opportunities, [:state_id])

    drop_if_exists index(:opportunities_contacts, [:org_id, :opportunity_id])

    drop_if_exists index(:opportunities_contacts, [:org_id, :contact_id])

    drop_if_exists index(:opportunities_contacts, [:org_id])

    drop constraint(:opportunities_contacts, "opportunities_contacts_org_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_opportunity_id_fkey")

    alter table(:opportunities_contacts) do
      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "opportunities_contacts_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "opportunities_contacts_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:opportunities_contacts, [:org_id])

    create index(:opportunities_contacts, [:org_id, :contact_id])

    create index(:opportunities_contacts, [:org_id, :opportunity_id])

    drop_if_exists index(:orgs_invitations, [:org_id])

    drop_if_exists index(:orgs_invitations, [:org_id, :user_id])

    drop constraint(:orgs_invitations, "orgs_invitations_user_id_fkey")

    drop constraint(:orgs_invitations, "orgs_invitations_org_id_fkey")

    alter table(:orgs_invitations) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_invitations_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :user_id,
             references(:users,
               column: :id,
               name: "orgs_invitations_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:orgs_invitations, [:org_id, :user_id])

    create index(:orgs_invitations, [:org_id])

    drop_if_exists index(:orgs_memberships, [:org_id])

    drop_if_exists index(:orgs_memberships, [:org_id, :user_id])

    drop constraint(:orgs_memberships, "orgs_memberships_user_id_fkey")

    drop constraint(:orgs_memberships, "orgs_memberships_org_id_fkey")

    alter table(:orgs_memberships) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_memberships_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :user_id,
             references(:users,
               column: :id,
               name: "orgs_memberships_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:orgs_memberships, [:org_id, :user_id])

    create index(:orgs_memberships, [:org_id])

    drop_if_exists index(:orgs_opportunities, [:org_id, :opportunity_id])

    drop_if_exists index(:orgs_opportunities, [:org_id])

    drop constraint(:orgs_opportunities, "orgs_opportunities_org_id_fkey")

    drop constraint(:orgs_opportunities, "orgs_opportunities_opportunity_id_fkey")

    alter table(:orgs_opportunities) do
      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "orgs_opportunities_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "orgs_opportunities_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:orgs_opportunities, [:org_id])

    create index(:orgs_opportunities, [:org_id, :opportunity_id])

    drop_if_exists index(:service_requests, [:org_id, :created_by_id])

    drop_if_exists index(:service_requests, [:org_id, :opportunity_id])

    drop_if_exists index(:service_requests, [:org_id])

    drop constraint(:service_requests, "service_requests_org_id_fkey")

    drop constraint(:service_requests, "service_requests_opportunity_id_fkey")

    drop constraint(:service_requests, "service_requests_created_by_id_fkey")

    alter table(:service_requests) do
      modify :created_by_id,
             references(:users,
               column: :id,
               name: "service_requests_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "service_requests_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "service_requests_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:service_requests, [:org_id])

    create index(:service_requests, [:org_id, :opportunity_id])

    create index(:service_requests, [:org_id, :created_by_id])

    drop_if_exists index(:user_notifications, [:org_id])

    drop_if_exists index(:user_notifications, [:sender_id])

    drop_if_exists index(:user_notifications, [:recipient_id])

    drop constraint(:user_notifications, "user_notifications_recipient_id_fkey")

    drop constraint(:user_notifications, "user_notifications_sender_id_fkey")

    drop constraint(:user_notifications, "user_notifications_org_id_fkey")

    alter table(:user_notifications) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "user_notifications_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :sender_id,
             references(:users,
               column: :id,
               name: "user_notifications_sender_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :recipient_id,
             references(:users,
               column: :id,
               name: "user_notifications_recipient_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:user_notifications, [:recipient_id])

    create index(:user_notifications, [:sender_id])

    create index(:user_notifications, [:org_id])

    drop_if_exists index(:users_pins, [:user_id])

    drop constraint(:users_pins, "users_pins_user_id_fkey")

    alter table(:users_pins) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_pins_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:users_pins, [:user_id])

    drop_if_exists index(:users_tokens, [:user_id])

    drop constraint(:users_tokens, "users_tokens_user_id_fkey")

    alter table(:users_tokens) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_tokens_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:users_tokens, [:user_id])

    drop_if_exists index(:users_totps, [:user_id])

    drop constraint(:users_totps, "users_totps_user_id_fkey")

    alter table(:users_totps) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "users_totps_user_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    create index(:users_totps, [:user_id])
  end
end
