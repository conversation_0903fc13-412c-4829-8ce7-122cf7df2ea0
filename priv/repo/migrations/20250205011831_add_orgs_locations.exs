defmodule PetalPro.Repo.Migrations.AddOrgLocation do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:orgs_locations, primary_key: false) do
      add :org_id,
          references(:orgs,
            column: :id,
            name: "orgs_locations_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          primary_key: true,
          null: false

      add :state_id,
          references(:states,
            column: :id,
            name: "orgs_locations_state_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          primary_key: true,
          null: false
    end
  end

  def down do
    drop constraint(:orgs_locations, "orgs_locations_org_id_fkey")

    drop constraint(:orgs_locations, "orgs_locations_state_id_fkey")

    drop table(:orgs_locations)
  end
end
