defmodule PetalPro.Repo.Migrations.HardenGooglePlaceSchema do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:google_places) do
      remove :data_date
      modify :image_urls, {:array, :text}, default: []
      modify :opening_hours, {:array, :map}, default: []
      modify :review_tags, {:array, :map}, default: []
      modify :latitude, :float, null: false
      modify :longitude, :float, null: false
      modify :full_address, :text, null: false
      modify :postal_code, :text, null: false
      modify :review_count, :bigint, null: false
      modify :review_score, :float, null: false
      modify :street, :text, null: false
      modify :business_name, :text, null: false
      modify :city, :text, null: false
      modify :google_country, :text, null: false
      modify :google_state, :text, null: false
    end
  end

  def down do
    alter table(:google_places) do
      modify :google_state, :text, null: true
      modify :google_country, :text, null: true
      modify :city, :text, null: true
      modify :business_name, :text, null: true
      modify :street, :text, null: true
      modify :review_score, :float, null: true
      modify :review_count, :bigint, null: true
      modify :postal_code, :text, null: true
      modify :full_address, :text, null: true
      modify :longitude, :float, null: true
      modify :latitude, :float, null: true
      modify :review_tags, {:array, :map}, default: nil
      modify :opening_hours, {:array, :map}, default: nil
      modify :image_urls, {:array, :text}, default: nil
      add :data_date, :utc_datetime
    end
  end
end
