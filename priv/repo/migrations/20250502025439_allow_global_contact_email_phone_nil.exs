defmodule PetalPro.Repo.Migrations.AllowGlobalContactEmailPhoneNil do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:global_contacts) do
      modify :phone_number, :text, null: true
      modify :email, :citext, null: true
    end
  end

  def down do
    alter table(:global_contacts) do
      modify :email, :citext, null: false
      modify :phone_number, :text, null: false
    end
  end
end
