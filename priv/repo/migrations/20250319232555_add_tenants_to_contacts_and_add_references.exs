defmodule PetalPro.Repo.Migrations.AddTenantsToContactsAndAddReferences do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:opportunities_contacts, "opportunities_contacts_opportunity_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    alter table(:opportunities_contacts) do
      add :role, :text, null: false

      add :org_id,
          references(:orgs,
            column: :id,
            name: "opportunities_contacts_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )

      add :archived_at, :utc_datetime_usec

      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "opportunities_contacts_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create unique_index(:opportunities_contacts, [:org_id, :contact_id, :opportunity_id, :role],
             name: "opportunities_contacts_tenant_unique_opportunity_contact_index"
           )

    drop constraint(:opportunities, "opportunities_state_id_fkey")

    drop constraint(:opportunities, "opportunities_google_place_id_fkey")

    alter table(:opportunities) do
      modify :google_place_id,
             references(:google_places,
               column: :id,
               name: "opportunities_google_place_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :state_id,
             references(:states,
               column: :id,
               name: "opportunities_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    drop constraint(:google_places_extracts, "google_places_extracts_state_id_fkey")

    alter table(:google_places_extracts) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "google_places_extracts_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    drop constraint(:google_places, "google_places_state_id_fkey")

    alter table(:google_places) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "google_places_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    alter table(:contacts) do
      remove :role
    end

    drop constraint(:contacts, "contacts_updated_by_id_fkey")

    drop constraint(:contacts, "contacts_created_by_id_fkey")

    alter table(:contacts) do
      add :org_id,
          references(:orgs,
            column: :id,
            name: "contacts_org_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          )
    end

    drop_if_exists unique_index(:contacts, [:email], name: "contacts_unique_email_index")

    alter table(:contacts) do
      modify :created_by_id,
             references(:users,
               column: :id,
               name: "contacts_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :updated_by_id,
             references(:users,
               column: :id,
               name: "contacts_updated_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create unique_index(:contacts, [:org_id, :email], name: "contacts_tenant_unique_email_index")

    create unique_index(:contacts, [:org_id, :phone_number],
             name: "contacts_tenant_unique_phone_number_index"
           )

    drop constraint(:buyboxes_locations, "buyboxes_locations_state_id_fkey")

    alter table(:buyboxes_locations) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "buyboxes_locations_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end
  end

  def down do
    drop constraint(:buyboxes_locations, "buyboxes_locations_state_id_fkey")

    alter table(:buyboxes_locations) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "buyboxes_locations_state_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    drop_if_exists unique_index(:contacts, [:org_id, :phone_number],
                     name: "contacts_tenant_unique_phone_number_index"
                   )

    drop_if_exists unique_index(:contacts, [:org_id, :email],
                     name: "contacts_tenant_unique_email_index"
                   )

    drop constraint(:contacts, "contacts_org_id_fkey")

    drop constraint(:contacts, "contacts_created_by_id_fkey")

    drop constraint(:contacts, "contacts_updated_by_id_fkey")

    alter table(:contacts) do
      modify :updated_by_id,
             references(:users,
               column: :id,
               name: "contacts_updated_by_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :created_by_id,
             references(:users,
               column: :id,
               name: "contacts_created_by_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:contacts, [:org_id, :email], name: "contacts_unique_email_index")

    alter table(:contacts) do
      remove :org_id
    end

    alter table(:contacts) do
      add :role, :text, null: false
    end

    drop constraint(:google_places, "google_places_state_id_fkey")

    alter table(:google_places) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "google_places_state_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    drop constraint(:google_places_extracts, "google_places_extracts_state_id_fkey")

    alter table(:google_places_extracts) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "google_places_extracts_state_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    drop constraint(:opportunities, "opportunities_google_place_id_fkey")

    drop constraint(:opportunities, "opportunities_state_id_fkey")

    alter table(:opportunities) do
      modify :state_id,
             references(:states,
               column: :id,
               name: "opportunities_state_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :google_place_id,
             references(:google_places,
               column: :id,
               name: "opportunities_google_place_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    drop_if_exists unique_index(
                     :opportunities_contacts,
                     [:org_id, :contact_id, :opportunity_id, :role],
                     name: "opportunities_contacts_tenant_unique_opportunity_contact_index"
                   )

    drop constraint(:opportunities_contacts, "opportunities_contacts_org_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_contact_id_fkey")

    drop constraint(:opportunities_contacts, "opportunities_contacts_opportunity_id_fkey")

    alter table(:opportunities_contacts) do
      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "opportunities_contacts_opportunity_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :contact_id,
             references(:contacts,
               column: :id,
               name: "opportunities_contacts_contact_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      remove :archived_at
      remove :org_id
      remove :role
    end
  end
end
