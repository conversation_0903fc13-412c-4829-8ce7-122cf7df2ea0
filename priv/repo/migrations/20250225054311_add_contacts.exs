defmodule PetalPro.Repo.Migrations.AddContacts do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:contacts, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :name, :text, null: false
      add :email, :citext, null: false
      add :phone_number, :text, null: false
      add :role, :text, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :created_by_id,
          references(:users,
            column: :id,
            name: "contacts_created_by_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :updated_by_id,
          references(:users,
            column: :id,
            name: "contacts_updated_by_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :archived_at, :utc_datetime_usec
    end

    create unique_index(:contacts, [:email], name: "contacts_unique_email_index")
  end

  def down do
    drop_if_exists unique_index(:contacts, [:email], name: "contacts_unique_email_index")

    drop constraint(:contacts, "contacts_created_by_id_fkey")

    drop constraint(:contacts, "contacts_updated_by_id_fkey")

    drop table(:contacts)
  end
end
