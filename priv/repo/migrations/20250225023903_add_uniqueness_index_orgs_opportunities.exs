defmodule PetalPro.Repo.Migrations.AddUniquenessIndexOrgOpportunity do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create index(:orgs_opportunities, [:org_id, :opportunity_id], unique: true)

    alter table(:orgs_opportunities) do
      modify :opportunity_id, :uuid, primary_key: false
      modify :org_id, :uuid, primary_key: false
    end
  end

  def down do
    # remove existing primary key
    drop constraint("orgs_opportunities", "orgs_opportunities_pkey")

    alter table(:orgs_opportunities) do
      modify :id, :uuid, primary_key: true
      modify :org_id, :uuid, primary_key: true
      modify :opportunity_id, :uuid, primary_key: true
    end

    drop index(:orgs_opportunities, [:org_id, :opportunity_id])
  end
end
