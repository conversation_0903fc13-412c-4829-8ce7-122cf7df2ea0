defmodule PetalPro.Repo.Migrations.CreateMeterEvents do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:billing_meter_events, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :event, :text, null: false
      add :value, :bigint, null: false
      add :provider_identifier, :text
      add :provider_created_at, :utc_datetime_usec

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :billing_customer_id,
          references(:billing_customers,
            column: :id,
            name: "billing_meter_events_billing_customer_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false
    end
  end

  def down do
    drop constraint(:billing_meter_events, "billing_meter_events_billing_customer_id_fkey")

    drop table(:billing_meter_events)
  end
end
