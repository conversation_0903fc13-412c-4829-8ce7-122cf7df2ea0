defmodule Boring.Repo.Migrations.AddRelationshipIndexesServices do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:service_requests, "service_requests_created_by_id_fkey")

    drop constraint(:service_requests, "service_requests_opportunity_id_fkey")

    drop constraint(:service_requests, "service_requests_org_id_fkey")

    alter table(:service_requests) do
      modify :org_id,
             references(:orgs,
               column: :id,
               name: "service_requests_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "service_requests_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :created_by_id,
             references(:users,
               column: :id,
               name: "service_requests_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:service_requests, [:org_id])

    create index(:service_requests, [:org_id, :opportunity_id])

    create index(:service_requests, [:org_id, :created_by_id])

    drop constraint(:global_contacts, "global_contacts_updated_by_id_fkey")

    drop constraint(:global_contacts, "global_contacts_created_by_id_fkey")

    drop constraint(:global_contacts, "global_contacts_opportunity_id_fkey")

    alter table(:global_contacts) do
      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "global_contacts_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )

      modify :created_by_id,
             references(:users,
               column: :id,
               name: "global_contacts_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :updated_by_id,
             references(:users,
               column: :id,
               name: "global_contacts_updated_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end

    create index(:global_contacts, [:opportunity_id])

    create index(:global_contacts, [:created_by_id])

    create index(:global_contacts, [:updated_by_id])
  end

  def down do
    drop_if_exists index(:global_contacts, [:updated_by_id])

    drop_if_exists index(:global_contacts, [:created_by_id])

    drop_if_exists index(:global_contacts, [:opportunity_id])

    drop constraint(:global_contacts, "global_contacts_opportunity_id_fkey")

    drop constraint(:global_contacts, "global_contacts_created_by_id_fkey")

    drop constraint(:global_contacts, "global_contacts_updated_by_id_fkey")

    alter table(:global_contacts) do
      modify :updated_by_id,
             references(:users,
               column: :id,
               name: "global_contacts_updated_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :created_by_id,
             references(:users,
               column: :id,
               name: "global_contacts_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "global_contacts_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    drop_if_exists index(:service_requests, [:org_id, :created_by_id])

    drop_if_exists index(:service_requests, [:org_id, :opportunity_id])

    drop_if_exists index(:service_requests, [:org_id])

    drop constraint(:service_requests, "service_requests_org_id_fkey")

    drop constraint(:service_requests, "service_requests_opportunity_id_fkey")

    drop constraint(:service_requests, "service_requests_created_by_id_fkey")

    alter table(:service_requests) do
      modify :created_by_id,
             references(:users,
               column: :id,
               name: "service_requests_created_by_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :opportunity_id,
             references(:opportunities,
               column: :id,
               name: "service_requests_opportunity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )

      modify :org_id,
             references(:orgs,
               column: :id,
               name: "service_requests_org_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :restrict
             )
    end
  end
end
