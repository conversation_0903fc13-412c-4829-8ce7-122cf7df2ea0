defmodule PetalPro.Repo.Migrations.AddOrgOpportunityFavorite do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:orgs_opportunities) do
      add :favorite?, :boolean, default: false
    end
  end

  def down do
    alter table(:orgs_opportunities) do
      remove :favorite?
    end
  end
end
