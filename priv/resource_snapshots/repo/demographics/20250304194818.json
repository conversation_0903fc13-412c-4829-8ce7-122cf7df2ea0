{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "city", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "population_estimate", "type": "bigint"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "median_household_income_estimate", "type": "money_with_currency"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "demographics_state_id_fkey", "on_delete": "nilify", "on_update": null, "primary_key?": true, "schema": "public", "table": "states"}, "size": null, "source": "state_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "5F2DF7F1AB9BDC41DE9F2332C957748B34DF41826D3EAA0EF46239344489CF2C", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "demographics_unique_city_demographic_index", "keys": [{"type": "atom", "value": "city"}, {"type": "atom", "value": "state_id"}], "name": "unique_city_demographic", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.PetalPro.Repo", "schema": null, "table": "demographics"}