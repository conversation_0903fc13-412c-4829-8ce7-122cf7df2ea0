{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "geo_id", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "city", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "population_estimate", "type": "bigint"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "median_household_income_estimate", "type": "money_with_currency"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": true, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "demographics_state_id_fkey", "on_delete": "restrict", "on_update": null, "primary_key?": true, "schema": "public", "table": "states"}, "size": null, "source": "state_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [{"all_tenants?": false, "concurrently": false, "error_fields": ["city", "state_id"], "fields": [{"type": "atom", "value": "city"}, {"type": "atom", "value": "state_id"}], "include": null, "message": null, "name": null, "nulls_distinct": true, "prefix": null, "table": null, "unique": false, "using": null, "where": null}], "custom_statements": [], "has_create_action": true, "hash": "000F36A5173CED7E73B23DAF5C8E30D1A1AEE4BC4ACA1C1E3E811900F3DF9516", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "demographics_unique_city_demographic_index", "keys": [{"type": "atom", "value": "geo_id"}], "name": "unique_city_demographic", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Boring.Repo", "schema": null, "table": "demographics"}