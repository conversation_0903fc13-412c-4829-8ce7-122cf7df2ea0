{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "place_id", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "customer_id", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "maps_url", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "google_state", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "google_country", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "website", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "city", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "phone_number", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "business_name", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "street", "type": "text"}, {"allow_nil?": false, "default": "0.0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "review_score", "type": "float"}, {"allow_nil?": false, "default": "0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "review_count", "type": "bigint"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "postal_code", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "full_address", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "longitude", "type": "float"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "latitude", "type": "float"}, {"allow_nil?": true, "default": "false", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "permanently_closed", "type": "boolean"}, {"allow_nil?": true, "default": "false", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "temporarily_closed", "type": "boolean"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "result_rank", "type": "bigint"}, {"allow_nil?": true, "default": "[]", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "categories", "type": ["array", "text"]}, {"allow_nil?": true, "default": "%{}", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "additional_info", "type": "map"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "review_distribution", "type": "map"}, {"allow_nil?": true, "default": "[]", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "review_tags", "type": ["array", "map"]}, {"allow_nil?": true, "default": "[]", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "opening_hours", "type": ["array", "map"]}, {"allow_nil?": true, "default": "[]", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "image_urls", "type": ["array", "text"]}, {"allow_nil?": true, "default": "false", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "is_business_claimed", "type": "boolean"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "84C637AC5D7068616C1D32583BA8B10F06DB96559832EE37018794DACCEA15E8", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "google_places_unique_google_place_index", "keys": [{"type": "atom", "value": "place_id"}], "name": "unique_google_place", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Boring.Repo", "schema": null, "table": "google_places"}