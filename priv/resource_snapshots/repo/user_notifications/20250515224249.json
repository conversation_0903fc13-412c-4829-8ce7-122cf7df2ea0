{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "type", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "message", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "read_at", "type": "utc_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "read_path", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": true, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "user_notifications_recipient_id_fkey", "on_delete": "delete", "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "size": null, "source": "recipient_id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": true, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "user_notifications_sender_id_fkey", "on_delete": "delete", "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "size": null, "source": "sender_id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": true, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "user_notifications_org_id_fkey", "on_delete": "delete", "on_update": null, "primary_key?": true, "schema": "public", "table": "orgs"}, "size": null, "source": "org_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [{"all_tenants?": false, "concurrently": false, "error_fields": ["recipient_id", "read_at"], "fields": [{"type": "atom", "value": "recipient_id"}, {"type": "atom", "value": "read_at"}], "include": null, "message": null, "name": null, "nulls_distinct": true, "prefix": null, "table": null, "unique": false, "using": null, "where": null}, {"all_tenants?": false, "concurrently": false, "error_fields": ["recipient_id", "read_path", "read_at"], "fields": [{"type": "atom", "value": "recipient_id"}, {"type": "atom", "value": "read_path"}, {"type": "atom", "value": "read_at"}], "include": null, "message": null, "name": null, "nulls_distinct": true, "prefix": null, "table": null, "unique": false, "using": null, "where": null}], "custom_statements": [], "has_create_action": true, "hash": "BBBC5276589C41F9856C40E76DDDD1F12A264AC5B61E3678B7D856B0EB0AD722", "identities": [], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Boring.Repo", "schema": null, "table": "user_notifications"}