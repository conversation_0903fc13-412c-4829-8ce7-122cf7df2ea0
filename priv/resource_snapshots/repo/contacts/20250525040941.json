{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "email", "type": "citext"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "phone_number", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "address", "type": "map"}, {"allow_nil?": true, "default": "false", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "skip_traced?", "type": "boolean"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": true, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "contacts_created_by_id_fkey", "on_delete": "restrict", "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "size": null, "source": "created_by_id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": true, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "contacts_updated_by_id_fkey", "on_delete": "restrict", "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "size": null, "source": "updated_by_id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": true, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "contacts_org_id_fkey", "on_delete": "delete", "on_update": null, "primary_key?": true, "schema": "public", "table": "orgs"}, "size": null, "source": "org_id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": true, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "contacts_global_contact_id_fkey", "on_delete": "nilify", "on_update": null, "primary_key?": true, "schema": "public", "table": "global_contacts"}, "size": null, "source": "global_contact_id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "archived_at", "type": "utc_datetime_usec"}], "base_filter": "(archived_at IS NULL)", "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "AC26FB73FCCD4EFC740D3D4DCCBFE1EA1F10AB33536396D896A7949B4D644726", "identities": [{"all_tenants?": false, "base_filter": "(archived_at IS NULL)", "index_name": "contacts_tenant_unique_email_index", "keys": [{"type": "atom", "value": "email"}], "name": "tenant_unique_email", "nils_distinct?": true, "where": "\"skip_traced?\" = false"}, {"all_tenants?": false, "base_filter": "(archived_at IS NULL)", "index_name": "contacts_tenant_unique_global_contact_index", "keys": [{"type": "atom", "value": "global_contact_id"}], "name": "tenant_unique_global_contact", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": "(archived_at IS NULL)", "index_name": "contacts_tenant_unique_phone_number_index", "keys": [{"type": "atom", "value": "phone_number"}], "name": "tenant_unique_phone_number", "nils_distinct?": true, "where": "\"skip_traced?\" = false"}], "multitenancy": {"attribute": "org_id", "global": false, "strategy": "attribute"}, "repo": "Elixir.Boring.Repo", "schema": null, "table": "contacts"}