{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "email", "type": "citext"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "interest_reason", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "testimonial?", "type": "boolean"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "lead_contacts_per_week", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "report_feedback?", "type": "boolean"}, {"allow_nil?": false, "default": "[]", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "preferred_regions", "type": ["array", "text"]}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "invited_at", "type": "utc_datetime"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "193F6B3202D64896FC45D6DF1ABAFE5AA8CEB5C0B97D33403137C9466A29DA6E", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "waitlist_requests_unique_email_index", "keys": [{"type": "atom", "value": "email"}], "name": "unique_email", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.PetalPro.Repo", "schema": null, "table": "waitlist_requests"}