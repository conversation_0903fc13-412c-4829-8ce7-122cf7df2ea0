{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "email", "type": "citext"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "hashed_password", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "confirmed_at", "type": "naive_datetime"}, {"allow_nil?": false, "default": "\"user\"", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "role", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "avatar", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "last_signed_in_ip", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "last_signed_in_datetime", "type": "utc_datetime"}, {"allow_nil?": true, "default": "false", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "is_subscribed_to_marketing_notifications", "type": "boolean"}, {"allow_nil?": true, "default": "false", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "is_suspended", "type": "boolean"}, {"allow_nil?": true, "default": "false", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "is_deleted", "type": "boolean"}, {"allow_nil?": true, "default": "false", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "is_onboarded", "type": "boolean"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "2737C0C25A282AA1ED6119D3DEA7A340EBDD86ABCE1D5638BDEAC76E78D85AE8", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "users_unique_email_index", "keys": [{"type": "atom", "value": "email"}], "name": "unique_email", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.PetalPro.Repo", "schema": null, "table": "users"}