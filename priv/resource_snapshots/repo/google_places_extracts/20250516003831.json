{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "1", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "interval", "type": "bigint"}, {"allow_nil?": false, "default": "\"months\"", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "interval_type", "type": "text"}, {"allow_nil?": true, "default": "true", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "is_enabled", "type": "boolean"}, {"allow_nil?": false, "default": "\"not_started\"", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "status", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "last_run_at", "type": "utc_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "last_actor_run_id", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "last_actor_run_started_at", "type": "utc_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "last_actor_run_finished_at", "type": "utc_datetime"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": true, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "google_places_extracts_state_id_fkey", "on_delete": "restrict", "on_update": null, "primary_key?": true, "schema": "public", "table": "states"}, "size": null, "source": "state_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "004EE04C1CBC52FBC4186C22145E4C26DD0A10E30CFC6D7CD37F0E31121F73BA", "identities": [], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Boring.Repo", "schema": null, "table": "google_places_extracts"}