{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "code", "type": "text"}, {"allow_nil?": false, "default": "\"US\"", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "country_code", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "B96CDD52D6494CC758FDDA53DEB7C51E4781C3B2BB08DBE9C7B6BA1266F25BA3", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "states_unique_country_state_index", "keys": [{"type": "atom", "value": "code"}, {"type": "atom", "value": "country_code"}], "name": "unique_country_state", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "states_state_code_index", "keys": [{"type": "atom", "value": "code"}], "name": "state_code", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "states_state_name_index", "keys": [{"type": "atom", "value": "name"}], "name": "state_name", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.PetalPro.Repo", "schema": null, "table": "states"}