{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "email", "type": "citext"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "provider", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "provider_customer_id", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "billing_customers_user_id_fkey", "on_delete": "delete", "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "size": null, "source": "user_id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "billing_customers_org_id_fkey", "on_delete": "delete", "on_update": null, "primary_key?": true, "schema": "public", "table": "orgs"}, "size": null, "source": "org_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [{"all_tenants?": false, "concurrently": false, "error_fields": ["provider"], "fields": [{"type": "atom", "value": "provider"}], "include": null, "message": null, "name": null, "nulls_distinct": true, "prefix": null, "table": null, "unique": false, "using": null, "where": null}], "custom_statements": [], "has_create_action": true, "hash": "42612E765BA6C5B6259288AD957A3B0BD963882DCBB2AE917D2C38B2BFF52BC1", "identities": [], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Boring.Repo", "schema": null, "table": "billing_customers"}