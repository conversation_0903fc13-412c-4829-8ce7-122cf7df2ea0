{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "status", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "plan_id", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "provider_subscription_id", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "provider_subscription_items", "type": ["array", "map"]}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "cancel_at", "type": "naive_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "canceled_at", "type": "naive_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "current_period_end_at", "type": "naive_datetime"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "current_period_start", "type": "naive_datetime"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "billing_subscriptions_billing_customer_id_fkey", "on_delete": "delete", "on_update": null, "primary_key?": true, "schema": "public", "table": "billing_customers"}, "size": null, "source": "billing_customer_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "EB13D390642D8E2D1AE61A9C891AEE1A6704A7544BA5720509069D72D9DE647C", "identities": [], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.PetalPro.Repo", "schema": null, "table": "billing_subscriptions"}