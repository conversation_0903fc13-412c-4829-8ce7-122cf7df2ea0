{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "primary_key?": true, "references": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "role", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "opportunities_contacts_org_id_fkey", "on_delete": "delete", "on_update": null, "primary_key?": true, "schema": "public", "table": "orgs"}, "size": null, "source": "org_id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": "org_id", "global": false, "strategy": "attribute"}, "name": "opportunities_contacts_contact_id_fkey", "on_delete": "restrict", "on_update": null, "primary_key?": true, "schema": "public", "table": "contacts"}, "size": null, "source": "contact_id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "opportunities_contacts_opportunity_id_fkey", "on_delete": "restrict", "on_update": null, "primary_key?": true, "schema": "public", "table": "opportunities"}, "size": null, "source": "opportunity_id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "archived_at", "type": "utc_datetime_usec"}], "base_filter": "(archived_at IS NULL)", "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "A78FDF0232EA5BE099ED7A913851FC982071180FD4D12BFF5BE7A58E068170F7", "identities": [{"all_tenants?": false, "base_filter": "(archived_at IS NULL)", "index_name": "opportunities_contacts_tenant_unique_opportunity_contact_index", "keys": [{"type": "atom", "value": "contact_id"}, {"type": "atom", "value": "opportunity_id"}], "name": "tenant_unique_opportunity_contact", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": "org_id", "global": false, "strategy": "attribute"}, "repo": "Elixir.Boring.Repo", "schema": null, "table": "opportunities_contacts"}