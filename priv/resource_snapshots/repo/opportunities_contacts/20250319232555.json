{"attributes": [{"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "role", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "opportunities_contacts_org_id_fkey", "on_delete": "delete", "on_update": null, "primary_key?": true, "schema": "public", "table": "orgs"}, "size": null, "source": "org_id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": true, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": "org_id", "global": false, "strategy": "attribute"}, "name": "opportunities_contacts_contact_id_fkey", "on_delete": "restrict", "on_update": null, "primary_key?": true, "schema": "public", "table": "contacts"}, "size": null, "source": "contact_id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "primary_key?": true, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "opportunities_contacts_opportunity_id_fkey", "on_delete": "restrict", "on_update": null, "primary_key?": true, "schema": "public", "table": "opportunities"}, "size": null, "source": "opportunity_id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "primary_key?": false, "references": null, "size": null, "source": "archived_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "C80E040D8019D9D00C2F241778B2A46E0FD2A4510C54122D294E8E08144C8A63", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "opportunities_contacts_tenant_unique_opportunity_contact_index", "keys": [{"type": "atom", "value": "contact_id"}, {"type": "atom", "value": "opportunity_id"}, {"type": "atom", "value": "role"}], "name": "tenant_unique_opportunity_contact", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": "org_id", "global": false, "strategy": "attribute"}, "repo": "Elixir.PetalPro.Repo", "schema": null, "table": "opportunities_contacts"}