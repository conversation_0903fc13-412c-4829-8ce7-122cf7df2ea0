# Changelog

<!-- changelog -->

## [v0.5.2](https://github.com/boring-business-leads/boring/compare/v0.5.1...v0.5.2) (2025-07-09)




### Bug Fixes:

* onboarding z index issue by <PERSON><PERSON>

## [v0.5.1](https://github.com/boring-business-leads/boring/compare/v0.5.0...v0.5.1) (2025-07-09)




### Improvements:

* Opp Search instructions and UX (#337) by <PERSON><PERSON>

* add instruction overlays to opp search map by <PERSON><PERSON>

* default opp search map to satellite by <PERSON><PERSON>

## [v0.5.0](https://github.com/boring-business-leads/boring/compare/v0.4.1...v0.5.0) (2025-07-09)




### Features:

* test/boring/scope_test.exs: Update test case by <PERSON><PERSON>

### Bug Fixes:

* subscribe success tests (#333) by <PERSON><PERSON>

* subscribe success tests by <PERSON><PERSON>

* map pan issue and upgrade deps (#330) by <PERSON><PERSON>

* Unable to pan when zoomed in on search by <PERSON><PERSON>

### Improvements:

* Add nicer subscribe success screen (#332) by <PERSON><PERSON>

* Add nicer subscribe success screen by <PERSON><PERSON>

## [v0.4.1](https://github.com/boring-business-leads/boring/compare/v0.4.0...v0.4.1) (2025-07-08)




### Bug Fixes:

* opp details contact flash messages (#329) by Chaz <PERSON>

* opp details contact flash messages by Chaz <PERSON>

## [v0.4.0](https://github.com/boring-business-leads/boring/compare/v0.3.0...v0.4.0) (2025-07-03)




### Features:

* Opportunity Details - Realtime contact syncing with pubsub (#326) by Chaz Watkins

* Opp Details - Realtime updates for contacts via pubsub by Chaz Watkins

* enformion-skiptrace (#305) by Chaz Watkins

### Bug Fixes:

* success flash for skiptrace by Chaz Watkins

* opp details contact actions for mobile by Chaz Watkins

* Skiptrace Experience (#324) by Chaz Watkins

* Skiptracer tests by Chaz Watkins

* Enformion Skiptracer by Chaz Watkins

* update http adapter for Slack, Apify, MailBluster by Chaz Watkins

* handle ZERO_RESULTS case in location geocoding (#317) by Chaz Watkins

* handle ZERO_RESULTS case in location geocoding by claude[bot]

* reduce Oban queue concurrency to align with pool size 35 (#316) by Chaz Watkins

* reduce Oban queue concurrency to align with pool size 35 by Chaz Watkins

* google text search address parsing by Chaz Watkins

* Enformion skiptracing by Chaz Watkins

* skiptrace: Update Enformion response parsing by Chaz Watkins

### Improvements:

* restrict skiptracing to pro and plus by Chaz Watkins

* landing page (#327) by Chaz Watkins

* Update landing page by Chaz Watkins

* pricing panel feature list by Chaz Watkins

## [v0.3.0](https://github.com/boring-business-leads/boring/compare/v0.2.8...v0.3.0) (2025-06-26)




### Features:

* added state filter back (#303) by Christopher-DeMarchi

### Bug Fixes:

* mobile_menu z index and disable endato (#302) by Chaz Watkins

* mobile menu z index by Chaz Watkins

## [v0.2.8](https://github.com/boring-business-leads/boring/compare/v0.2.7...v0.2.8) (2025-06-24)

### Chores

* Update zendesk widget to new url


## [v0.2.7](https://github.com/boring-business-leads/boring/compare/v0.2.6...v0.2.7) (2025-06-24)




### Bug Fixes:

* opportunity search error when clearing search field (#297) by Chaz Watkins

* opportunity search error when clearing search field by Chaz Watkins

## [v0.2.6](https://github.com/boring-business-leads/boring/compare/v0.2.5...v0.2.6) (2025-06-23)

- Add "not in buybox" status back

## [v0.2.5](https://github.com/boring-business-leads/boring/compare/v0.2.5-rc.1...v0.2.5) (2025-06-22)




## [v0.2.5-rc.1](https://github.com/boring-business-leads/boring/compare/v0.2.5-rc.0...v0.2.5-rc.1) (2025-06-22)




### Improvements:

* opp-search: improve off market sorting (#289) by Chaz Watkins

* opp-search: improve off market sorting by Chaz Watkins

## [v0.2.5-rc.0](https://github.com/boring-business-leads/boring/compare/v0.2.4...v0.2.5-rc.0) (2025-06-22)




### Bug Fixes:

* deal-flow: control bar showing on top of mobile menu by Chaz Watkins

## [v0.2.4](https://github.com/boring-business-leads/boring/compare/v0.2.4-rc.0...v0.2.4) (2025-06-22)




## [v0.2.4-rc.0](https://github.com/boring-business-leads/boring/compare/v0.2.3...v0.2.4-rc.0) (2025-06-22)




### Bug Fixes:

* opp-search: no places found and crash for unrelated pubsub by Chaz Watkins

## [v0.2.3](https://github.com/boring-business-leads/boring/compare/v0.2.2...v0.2.3) (2025-06-21)




### Improvements:

* update opportunity list when opportunity details are updated by Chaz Watkins

## [v0.2.2](https://github.com/boring-business-leads/boring/compare/v0.2.1...v0.2.2) (2025-06-21)




### Improvements:

* Make opportunity details slide-over on deal flow (#287) by Chaz Watkins

* move org_opportunity_live to slide-over by Chaz Watkins

## [v0.2.1](https://github.com/boring-business-leads/boring/compare/v0.2.0...v0.2.1) (2025-06-20)

### Improvements

* protect opp search route for subscribers only by Chaz Watkins

* disable search menu item for non-subscribers by Chaz Watkins

* add review count to OrgDashboard opp card by Chaz Watkins

* add review count to opp card by Chaz Watkins


## [v0.2.0](https://github.com/boring-business-leads/boring/compare/v0.1.8...v0.2.0) (2025-06-19)
### Breaking Changes:

* remove buybox and auto-assignment features by Chaz Watkins



### Features:

* add auto-skiptrace when opportunity is assigned by Chaz Watkins

* preset_filter: Change "favorites" to "trophies" by Chaz Watkins

* no_opportunities_screen: Replace link with button for opportunity search by Chaz Watkins

* Opportunity Search - minus assignment (#281) by Chaz Watkins

* api: implement Google Places and Geocoding API wrapper by Chaz Watkins

* google-map: add generic shape update system with in-place modifications by Chaz Watkins

* opportunity-search: Update default map type and add LiveView events by Chaz Watkins

* opportunity-search: Expand place data fields and create custom info window by Chaz Watkins

* opportunity_search: Improve page header and search placeholder by Chaz Watkins

* opportunity search add polygon search area by Chaz Watkins

* add radius search for opportunity search by Chaz Watkins

* add search to google_map component by Chaz Watkins

* implement async task infrastructure for parallel query execution by claude[bot]

### Bug Fixes:

* opportunity card image placeholder sizing by Chaz Watkins

* tests: Clean up tests after buybox removal by Chaz Watkins

* tests: Clean up tests after buybox removal by Chaz Watkins

* opportunity-search: place list disappearing on search by Chaz Watkins

* google-map: resolve state management issues by Chaz Watkins

* opportunity-search: markers by Chaz Watkins

* opp search location bias by Chaz Watkins

* contacts: Remove email and phone number validation from contact and global_contact by Chaz Watkins

* skiptrace apify age parsing and add git ops for release management (#250) by Chaz Watkins

### Improvements:

* opp-details: last skiptrace date by Chaz Watkins

* opp-search: list sorting to show least managed first by Chaz Watkins

* opp-search: handle params for search forms by Chaz Watkins

* debounce location search by 500 ms by Chaz Watkins

* opportunity-search: simplify assignment by Chaz Watkins

* google-map: add color options for markers by Chaz Watkins

* clean up opportunity search vibe coding shennanigans by Chaz Watkins

* opportunity-search: improve mobile styling by Chaz Watkins

* opportunity-search: Display when next opportunities are available by Chaz Watkins

* org: change opportunities_assigned_this_interval to billing period based by Chaz Watkins

* opportunity_search: Add quota exhausted screen by Chaz Watkins

* opportunity-search: Add entitlement progress bar by Chaz Watkins

* opportunity_search: Combine Google Places API and Apify data by Chaz Watkins

* google_map: Add control positioning by Chaz Watkins

* opportunity-search: Convert styling to petal components by Chaz Watkins

* org-opportunity: Clear skiptrace create flash when complete (#273) by Chaz Watkins

* org-opportunity: Clear skiptrace create flash when complete by Chaz Watkins

### Performance Improvements:

* optimize org_dashboard to use Task.await_many for better resource efficiency (#271) by Chaz Watkins

* optimize org_dashboard to use Task.await_many instead of multiple Task.await calls by claude[bot]

* implement async task infrastructure for parallel query execution (#266) by Chaz Watkins