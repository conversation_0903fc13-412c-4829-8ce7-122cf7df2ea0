# Chunk 5: Property Structure and Response Variations
**Priority**: Medium | **Estimated Tests**: 9 | **Impact**: Medium

## Scope
Test various property response structures and API response formats to ensure robust parsing.

## Test Cases

### 5.1 Missing currentOwners Array
**Scenario:** Property response has no `currentOwners` array in summary
**Implementation:**
- Valid property structure but `currentOwners` key missing
- Verify graceful handling of missing owner data
- Test `Map.get(summary, "currentOwners", [])` fallback

**Expected Results:**
- Empty results returned (no contacts)
- No errors thrown during processing
- Other property data can still be processed
- Consistent result structure maintained

### 5.2 Empty currentOwners Array
**Scenario:** Property has `currentOwners: []` (empty array)
**Implementation:**
- Valid property structure with empty owners array
- Verify empty array handling vs missing key
- Test processing continues normally

**Expected Results:**
- Empty results returned gracefully
- No errors from empty array processing
- Distinction between missing and empty handled correctly
- Function completes successfully

### 5.3 Missing property.summary Section
**Scenario:** Property object lacks `summary` section entirely
**Implementation:**
- Valid property object structure
- Missing `property.summary` key
- Test `extract_owners_from_single_property/1` fallback

**Expected Results:**
- No contacts extracted from property
- Function returns empty list for that property
- No errors thrown
- Other properties in response still processed

### 5.4 Multiple Properties in Response
**Scenario:** `propertyV2Records` array contains multiple property objects
**Implementation:**
- 3+ properties in response array
- Each property has different owners
- Mix of valid and invalid property structures
- Verify all valid properties processed

**Expected Results:**
- All owners from all valid properties included
- Results flattened into single contact list
- Invalid properties skipped gracefully
- Contact count reflects all valid owners

### 5.5 Invalid Property Objects in Array
**Scenario:** `propertyV2Records` contains mix of valid and invalid objects
**Implementation:**
- Some objects missing required structure
- Some objects have wrong data types
- Some objects are null/nil
- Test robust array processing

**Expected Results:**
- Valid property objects processed correctly
- Invalid objects skipped without errors
- Processing continues for remaining valid objects
- Partial results returned successfully

### 5.6 propertyV2Records Not an Array
**Scenario:** API response has `propertyV2Records` as wrong data type
**Implementation:**
- `propertyV2Records` is an object instead of array
- `propertyV2Records` is a string or primitive
- `propertyV2Records` is null
- Test type checking and error handling

**Expected Results:**
- Wrong data types handled gracefully
- Empty results returned for invalid types
- No exceptions thrown
- Function completes successfully

### 5.7 propertyV2Records Missing from Response
**Scenario:** API response body lacks `propertyV2Records` key entirely
**Implementation:**
- Valid HTTP response structure
- Response body missing expected key
- Test pattern matching and fallback

**Expected Results:**
- Missing key handled gracefully
- Empty results returned
- No errors from missing key
- Consistent error handling

### 5.8 Completely Empty Response Body
**Scenario:** API returns empty object `{}` as response body
**Implementation:**
- Valid HTTP 200 response
- Response body is empty object
- Test minimal response handling

**Expected Results:**
- Empty response handled gracefully
- Empty results returned
- No errors from minimal data
- Function completes successfully

### 5.9 Property with Only previousOwners
**Scenario:** Property has no current owners, only previous owners
**Implementation:**
- Valid property structure
- `currentOwners` array is empty
- `previousOwners` array has data
- Verify only current owners processed

**Expected Results:**
- No contacts returned (previous owners ignored)
- Empty results for that property
- Previous owners not included in results
- Processing logic focuses only on current owners

## Required Mock Data

```elixir
# Property missing currentOwners array
@property_response_no_current_owners [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        # No currentOwners key
        "previousOwners" => [
          %{
            "name" => %{
              "fullName" => "Previous Owner",
              "tahoeId" => "G9999999999"
            }
          }
        ]
      }
    }
  }
]

# Property with empty currentOwners array
@property_response_empty_current_owners [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        "currentOwners" => [],  # Empty array
        "previousOwners" => [
          %{
            "name" => %{
              "fullName" => "Previous Owner",
              "tahoeId" => "G9999999999"
            }
          }
        ]
      }
    }
  }
]

# Property missing summary section
@property_response_no_summary [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      # No summary section
      "address" => %{
        "street" => "123 Main St",
        "city" => "Springfield",
        "state" => "IL"
      }
    }
  }
]

# Multiple properties response
@property_response_multiple_properties [
  %{
    "poseidonId" => 111111111,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Owner One",
              "firstName" => "Owner",
              "lastName" => "One",
              "tahoeId" => "G1111111111"
            }
          }
        ]
      }
    }
  },
  %{
    "poseidonId" => 222222222,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Owner Two",
              "firstName" => "Owner", 
              "lastName" => "Two",
              "tahoeId" => "G2222222222"
            }
          },
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Owner Three",
              "firstName" => "Owner",
              "lastName" => "Three", 
              "tahoeId" => "G3333333333"
            }
          }
        ]
      }
    }
  }
]

# Invalid property objects mixed with valid
@property_response_invalid_structure [
  %{
    "poseidonId" => 111111111,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Valid Owner",
              "tahoeId" => "G1111111111"
            }
          }
        ]
      }
    }
  },
  %{
    "invalid" => "structure"  # Invalid property object
  },
  nil,  # Null property object
  %{
    "poseidonId" => 333333333
    # Missing property section entirely
  }
]

# Wrong data type responses
@property_response_wrong_type_object %{
  "propertyV2Records" => %{  # Object instead of array
    "poseidonId" => 123456789,
    "property" => %{}
  }
}

@property_response_wrong_type_string %{
  "propertyV2Records" => "invalid_string"  # String instead of array
}

@property_response_wrong_type_null %{
  "propertyV2Records" => nil  # Null instead of array
}

# Missing propertyV2Records key
@property_response_missing_key %{
  "someOtherData" => "value"
  # No propertyV2Records key
}

# Empty response body
@empty_response_body %{}

# Property with only previous owners
@property_response_only_previous_owners [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        "currentOwners" => [],
        "previousOwners" => [
          %{
            "name" => %{
              "fullName" => "Previous Owner One",
              "tahoeId" => "G8888888888"
            }
          },
          %{
            "name" => %{
              "fullName" => "Previous Owner Two", 
              "tahoeId" => "G9999999999"
            }
          }
        ]
      }
    }
  }
]
```

## Test Structure Template

```elixir
describe "Property Structure and Response Variations" do
  test "handles missing currentOwners array gracefully", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_response_no_current_owners}
      }}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
  end

  test "handles empty currentOwners array", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_response_empty_current_owners}
      }}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
  end

  test "handles missing property summary section", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_response_no_summary}
      }}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
  end

  test "processes multiple properties correctly", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_response_multiple_properties}
      }}
    end)

    # Mock contact enrichment for all owners
    stub(Endato, :contact_id, fn _tahoe_id ->
      {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
    end)

    assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should have 3 total owners from 2 properties
    assert length(results) == 3
    
    names = Enum.map(results, & &1.name)
    assert "Owner One" in names
    assert "Owner Two" in names
    assert "Owner Three" in names
  end

  test "skips invalid property objects gracefully", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_response_invalid_structure}
      }}
    end)

    stub(Endato, :contact_id, fn _tahoe_id ->
      {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
    end)

    assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should only have 1 valid owner (invalid objects skipped)
    assert length(results) == 1
    assert Enum.at(results, 0).name == "Valid Owner"
  end

  test "handles propertyV2Records wrong data type", %{service_request: service_request} do
    # Test object instead of array
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: @property_response_wrong_type_object
      }}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
  end

  # Additional test implementations...
end
```

## Implementation Notes

### Array Processing
- Test `Stream.flat_map/2` behavior with various input types
- Verify invalid objects are skipped without errors
- Ensure empty arrays are handled correctly

### Pattern Matching
- Test pattern matching in `extract_owners_from_single_property/1`
- Verify fallback clauses handle unexpected structures
- Ensure graceful degradation for malformed data

### Error Isolation
- Verify errors in one property don't affect others
- Test partial success scenarios
- Ensure consistent error handling across property variations

### Data Type Validation
- Test response body type checking
- Verify array vs object distinction
- Ensure null/nil values handled appropriately

## Success Criteria
- [ ] Missing sections result in empty results, not errors
- [ ] Multiple properties processed correctly with flattened results
- [ ] Invalid property objects skipped gracefully  
- [ ] Wrong data types handled without crashing
- [ ] Empty response bodies return empty results
- [ ] Pattern matching handles all expected variations
- [ ] Previous owners ignored (only current owners processed)
- [ ] Partial failures don't prevent other property processing
- [ ] Consistent result structure across all scenarios

## File Location
`test/boring/api/skiptrace_service/providers/endato_property_structure_test.exs`

## Context Dependencies
- Understanding of `extract_owners_from_single_property/1` pattern matching
- Knowledge of `Stream.flat_map/2` behavior with invalid inputs
- Familiarity with Elixir pattern matching fallback clauses
- Understanding of how the main processing loop handles arrays