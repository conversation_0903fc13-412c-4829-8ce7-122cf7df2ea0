# Chunk 8: API Error Handling and Edge Cases
**Priority**: Low | **Estimated Tests**: 6 | **Impact**: Low

## Scope
Test comprehensive API error handling, network issues, and unexpected response scenarios to ensure robust error recovery.

## Test Cases

### 8.1 Network Timeout Errors
**Scenario:** Infrastructure failures and network timeouts during API calls
**Implementation:**
- Mock `Endato.property_search` to raise timeout errors
- Mock `Endato.contact_id` to raise timeout errors  
- Test various timeout scenarios (connection, read, etc.)
- Verify graceful handling without crashing

**Expected Results:**
- Network errors result in empty results `{:ok, %Result{contacts: []}}`
- No exceptions propagated to caller
- Function completes gracefully
- Error logging for debugging purposes

### 8.2 Invalid API Credentials
**Scenario:** Authentication and authorization errors from API
**Implementation:**
- Mock 401 Unauthorized responses
- Mock 403 Forbidden responses
- Test both property search and contact enrichment credential failures
- Verify consistent error handling

**Expected Results:**
- Authentication errors return empty results
- No retries attempted for auth failures
- Appropriate error responses handled
- No credential exposure in logs

### 8.3 Rate Limiting Responses
**Scenario:** API quota exceeded and rate limiting scenarios
**Implementation:**
- Mock 429 Too Many Requests responses
- Mock various rate limit headers and messages
- Test both temporary and permanent rate limits
- Verify rate limit handling doesn't crash processing

**Expected Results:**
- Rate limits result in empty results (no automatic retry)
- 429 responses handled gracefully
- Function continues to completion
- No infinite retry loops on rate limits

### 8.4 Partial Response Corruption
**Scenario:** Incomplete or corrupted data transmission
**Implementation:**
- Mock responses with incomplete JSON
- Mock responses with invalid JSON syntax
- Mock responses with truncated data
- Test JSON parsing error handling

**Expected Results:**
- Malformed JSON handled gracefully
- Parsing errors don't crash function
- Empty results returned for corrupted data
- Consistent error handling across all corruption types

### 8.5 Unexpected HTTP Status Codes
**Scenario:** Non-standard or unexpected HTTP response codes
**Implementation:**
- Mock unusual status codes (418, 451, 502, 503, etc.)
- Test both property search and contact enrichment unusual responses
- Verify status code handling logic
- Test boundary conditions in status checking

**Expected Results:**
- All non-200 status codes result in empty results
- No assumptions about specific error codes
- Consistent handling regardless of status code value
- Function completes successfully for all status codes

### 8.6 API Response with Unexpected Content-Type
**Scenario:** API returns data in wrong format or encoding
**Implementation:**
- Mock responses with wrong Content-Type headers
- Mock responses with binary data instead of JSON
- Mock responses with HTML error pages
- Test content type validation and handling

**Expected Results:**
- Wrong content types handled gracefully
- No assumptions about response format
- Empty results for non-JSON responses
- No parsing errors from unexpected formats

## Required Mock Data

```elixir
# Network timeout errors
@network_timeout_error {:error, %{reason: :timeout}}
@connection_timeout_error {:error, %{reason: :connect_timeout}}
@read_timeout_error {:error, %{reason: :timeout}}

# Authentication error responses
@unauthorized_response %Req.Response{
  status: 401,
  body: %{
    "error" => "Unauthorized",
    "message" => "Invalid API credentials"
  }
}

@forbidden_response %Req.Response{
  status: 403,
  body: %{
    "error" => "Forbidden", 
    "message" => "Access denied"
  }
}

# Rate limiting responses
@rate_limit_response %Req.Response{
  status: 429,
  body: %{
    "error" => "Too Many Requests",
    "retry_after" => 60
  }
}

@quota_exceeded_response %Req.Response{
  status: 429,
  body: %{
    "error" => "Quota exceeded",
    "message" => "Daily API limit reached"
  }
}

# Corrupted response data
@partial_json_response %Req.Response{
  status: 200,
  body: "{\"propertyV2Records\": [{"  # Incomplete JSON
}

@invalid_json_response %Req.Response{
  status: 200,
  body: "{invalid json syntax}"
}

@malformed_json_response %Req.Response{
  status: 200,
  body: "{{{{ not valid json"
}

# Unexpected status codes
@teapot_response %Req.Response{
  status: 418,  # I'm a teapot
  body: %{"error" => "I'm a teapot"}
}

@unavailable_legal_response %Req.Response{
  status: 451,  # Unavailable for legal reasons
  body: %{"error" => "Unavailable for legal reasons"}
}

@bad_gateway_response %Req.Response{
  status: 502,
  body: %{"error" => "Bad Gateway"}
}

@service_unavailable_response %Req.Response{
  status: 503,
  body: %{"error" => "Service Temporarily Unavailable"}
}

# Wrong content type responses
@html_error_response %Req.Response{
  status: 200,
  headers: [{"content-type", "text/html"}],
  body: "<html><body>Error Page</body></html>"
}

@binary_response %Req.Response{
  status: 200,
  headers: [{"content-type", "application/octet-stream"}],
  body: <<0, 1, 2, 3, 4, 5, 6, 7, 8, 9>>  # Binary data
}

@xml_response %Req.Response{
  status: 200,
  headers: [{"content-type", "application/xml"}],
  body: "<?xml version=\"1.0\"?><error>XML Error</error>"
}

# Property for testing contact enrichment errors
@property_for_contact_errors [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "John Smith",
              "firstName" => "John",
              "lastName" => "Smith",
              "tahoeId" => "G1234567890"
            }
          }
        ]
      }
    }
  }
]
```

## Test Structure Template

```elixir
describe "API Error Handling and Edge Cases" do
  test "handles network timeout errors gracefully", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      @network_timeout_error
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
  end

  test "handles authentication errors gracefully", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, @unauthorized_response}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
  end

  test "handles rate limiting responses", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, @rate_limit_response}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
  end

  test "handles contact enrichment network errors", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_for_contact_errors}
      }}
    end)

    stub(Endato, :contact_id, fn "G1234567890" ->
      @connection_timeout_error
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should create contact with basic info despite enrichment error
    assert result.name == "John Smith"
    assert result.emails == []
    assert result.phone_numbers == []
    assert result.address == nil
  end

  test "handles partial JSON corruption", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, @partial_json_response}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
  end

  test "handles unexpected HTTP status codes", %{service_request: service_request} do
    # Test unusual status codes
    unusual_responses = [
      @teapot_response,
      @unavailable_legal_response,
      @bad_gateway_response,
      @service_unavailable_response
    ]

    for response <- unusual_responses do
      stub(Endato, :property_search, fn _params ->
        {:ok, response}
      end)

      assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
    end
  end

  test "handles wrong content type responses", %{service_request: service_request} do
    wrong_content_responses = [
      @html_error_response,
      @binary_response,
      @xml_response
    ]

    for response <- wrong_content_responses do
      stub(Endato, :property_search, fn _params ->
        {:ok, response}
      end)

      assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
    end
  end

  test "contact enrichment handles various error types", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_for_contact_errors}
      }}
    end)

    error_responses = [
      @unauthorized_response,
      @rate_limit_response,
      @teapot_response,
      @partial_json_response
    ]

    for error_response <- error_responses do
      stub(Endato, :contact_id, fn "G1234567890" ->
        {:ok, error_response}
      end)

      assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
      
      # Should still create contact with basic info
      assert result.name == "John Smith"
      assert result.emails == []
      assert result.phone_numbers == []
    end
  end

  # Additional error scenario tests...
end
```

## Implementation Notes

### Error Type Coverage
- Test both property search and contact enrichment API errors
- Verify different error types (network, HTTP, parsing)
- Ensure consistent error handling across all API calls

### Exception Handling
- Verify no exceptions propagate to caller
- Test that all error scenarios return valid Result structs
- Ensure error handling doesn't break processing flow

### Logging Verification
- Verify appropriate error logging occurs
- Check that sensitive data isn't exposed in logs
- Ensure logging helps with debugging

### Recovery Testing
- Test that partial failures don't prevent other processing
- Verify graceful degradation when some APIs fail
- Ensure consistent behavior across error types

## Success Criteria
- [ ] All error scenarios return empty results gracefully
- [ ] No exceptions propagated to caller
- [ ] Appropriate logging for different error types
- [ ] Consistent error response structure
- [ ] Timeout handling doesn't block indefinitely
- [ ] Authentication errors handled appropriately
- [ ] Rate limits don't cause infinite retries
- [ ] Malformed responses handled gracefully
- [ ] Contact enrichment errors don't prevent basic contact creation

## File Location
`test/boring/api/skiptrace_service/providers/endato_error_handling_test.exs`

## Context Dependencies
- Understanding of error handling patterns in `do_run_skiptrace/2`
- Knowledge of `enrich_owner_contact/1` error handling
- Familiarity with Logger usage for error reporting
- Understanding of how errors are propagated through the processing chain