# Chunk 3: Contact Enrichment and Address Fallback
**Priority**: High | **Estimated Tests**: 10 | **Impact**: High

## Scope
Test the complete address fallback chain: enrichment → mailing address → nil, plus contact enrichment edge cases.

## Test Cases

### 3.1 Enrichment Address Takes Precedence
**Scenario:** Both enrichment and mailing addresses available, enrichment used
**Implementation:**
- Owner with valid `tahoeId`
- Contact enrichment returns valid address
- Mailing address also available
- Verify enrichment address chosen

**Expected Results:**
- Contact enrichment address used
- Mailing address ignored
- Proper address structure returned
- All address fields populated correctly

### 3.2 Mailing Address Fallback When Enrichment Invalid
**Scenario:** Enrichment address invalid/incomplete, falls back to mailing
**Implementation:**
- Contact enrichment returns address missing required fields
- Valid mailing address available in metadata
- Address validation rejects enrichment address

**Expected Results:**
- Enrichment address rejected due to validation failure
- Mailing address used as fallback
- Proper address structure maintained
- No errors during fallback process

### 3.3 Nil Address When Both Sources Invalid
**Scenario:** Both enrichment and mailing addresses fail validation
**Implementation:**
- Contact enrichment returns incomplete address
- Mailing address also missing required fields
- Both addresses fail validation

**Expected Results:**
- Both addresses rejected by validation
- Final address field is `nil`
- Contact still created with other data
- No errors thrown during processing

### 3.4 Contact Enrichment with Non-200 Response
**Scenario:** Contact ID API returns error status codes
**Implementation:**
- Owner with valid `tahoeId`
- Contact ID API returns 404, 500, etc.
- Mailing address available as fallback

**Expected Results:**
- Contact enrichment skipped due to error
- Basic owner information used
- Mailing address used for location
- Debug logging for error status

### 3.5 Contact Enrichment with Malformed Person Data
**Scenario:** Contact ID API returns 200 but invalid person structure
**Implementation:**
- Valid API response (200 status)
- Missing `person` key or invalid structure
- Test various malformed data scenarios

**Expected Results:**
- Malformed data handled gracefully
- Falls back to basic owner info
- Mailing address used when available
- No exceptions thrown

### 3.6 Partial Contact Enrichment Data
**Scenario:** Contact enrichment returns incomplete but valid data
**Implementation:**
- Contact response missing some fields (emails, phones, addresses)
- Present fields have valid data
- Test each field absence independently

**Expected Results:**
- Available data used correctly
- Missing fields result in empty arrays/nil values
- Address fallback works when enrichment address missing
- Partial data doesn't cause errors

### 3.7 Contact Enrichment Timeout/Network Error
**Scenario:** Infrastructure failures during contact enrichment
**Implementation:**
- Mock network timeouts
- Mock connection failures
- Test various infrastructure error types

**Expected Results:**
- Network errors handled gracefully
- Falls back to basic owner info
- Mailing address used when available
- Error logging for debugging

### 3.8 Multiple Address Sources with Different Validity
**Scenario:** Matrix testing of address validity combinations
**Implementation:**
- Test all combinations:
  - Valid enrichment + valid mailing → use enrichment
  - Valid enrichment + invalid mailing → use enrichment  
  - Invalid enrichment + valid mailing → use mailing
  - Invalid enrichment + invalid mailing → nil address

**Expected Results:**
- Correct precedence order maintained
- Proper fallback logic in all cases
- Consistent address validation applied
- Expected address source used

### 3.9 Address Validation with Edge Case Data
**Scenario:** Boundary testing for address validation logic
**Implementation:**
- Empty string values in address fields
- Null/nil values in address fields
- Whitespace-only address fields
- Very long address field values

**Expected Results:**
- Invalid addresses properly rejected
- Validation logic consistent across sources
- Edge cases handled gracefully
- No validation errors thrown

### 3.10 Contact Enrichment with Empty Arrays
**Scenario:** Valid enrichment response with empty but present arrays
**Implementation:**
- Contact enrichment returns 200 with valid structure
- `emails`, `phones`, `addresses` arrays are empty `[]`
- Test that empty arrays are handled correctly

**Expected Results:**
- Empty arrays processed without errors
- Result fields have empty arrays (not nil)
- Address fallback triggered when addresses array empty
- Other enrichment data still processed

## Required Mock Data

```elixir
# Valid enrichment address
@contact_enrichment_valid_address %{
  "name" => %{
    "firstName" => "John",
    "lastName" => "Smith"
  },
  "emails" => [
    %{
      "email" => "<EMAIL>",
      "isValidated" => true,
      "isBusiness" => false
    }
  ],
  "phones" => [
    %{
      "number" => "************",
      "isConnected" => true
    }
  ],
  "addresses" => [
    %{
      "street" => "123 Enrichment St",
      "city" => "Springfield",
      "state" => "IL", 
      "zip" => "62701"
    }
  ]
}

# Invalid enrichment address (missing required fields)
@contact_enrichment_invalid_address %{
  "name" => %{
    "firstName" => "Jane",
    "lastName" => "Doe"
  },
  "emails" => [],
  "phones" => [],
  "addresses" => [
    %{
      "street" => "",  # Missing required field
      "city" => "Springfield",
      "state" => "IL",
      "zip" => "62701"
    }
  ]
}

# Partial enrichment data (missing some fields)
@contact_enrichment_partial_data %{
  "name" => %{
    "firstName" => "Bob",
    "lastName" => "Wilson"
  }
  # Missing emails, phones, addresses entirely
}

# Malformed enrichment responses
@contact_enrichment_malformed_no_person %{
  "error" => "Person not found"
  # Missing "person" key
}

@contact_enrichment_malformed_person_not_object %{
  "person" => "invalid_structure"  # Should be object
}

# Valid mailing address for fallback
@mailing_address_valid %{
  "addressLine1" => "456 Mailing Ave",
  "city" => "Chicago", 
  "state" => "IL",
  "zipCode" => "60601"
}

# Invalid mailing address
@mailing_address_invalid %{
  "addressLine1" => "",  # Missing required field
  "city" => "Chicago",
  "state" => "IL",
  "zipCode" => "60601"
}

# Property response with owner and mailing address
@property_with_individual_and_mailing [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "John Smith",
              "firstName" => "John",
              "lastName" => "Smith",
              "tahoeId" => "G1234567890"
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [@mailing_address_valid]
        }
      }
    }
  }
]

# Edge case address data
@address_with_edge_cases %{
  "street" => "   ",  # Whitespace only
  "city" => nil,      # Null value
  "state" => "",      # Empty string
  "zip" => "A very long zip code that exceeds normal length expectations for postal codes"
}
```

## Test Structure Template

```elixir
describe "Contact Enrichment and Address Fallback" do
  test "enrichment address takes precedence over mailing address", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_with_individual_and_mailing}
      }}
    end)

    stub(Endato, :contact_id, fn "G1234567890" ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"person" => @contact_enrichment_valid_address}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should use enrichment address, not mailing address
    assert result.address == %{
      street: "123 Enrichment St",
      city: "Springfield",
      state: "IL",
      postal_code: "62701"
    }
    
    # Should have enriched contact data
    assert result.emails == ["<EMAIL>"]
    assert result.phone_numbers == ["************"]
  end

  test "mailing address fallback when enrichment address invalid", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_with_individual_and_mailing}
      }}
    end)

    stub(Endato, :contact_id, fn "G1234567890" ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"person" => @contact_enrichment_invalid_address}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should use mailing address as fallback
    assert result.address == %{
      street: "456 Mailing Ave",
      city: "Chicago",
      state: "IL", 
      postal_code: "60601"
    }
    
    # Should still have enriched name but no emails/phones
    assert result.name == "Jane Doe"
    assert result.emails == []
    assert result.phone_numbers == []
  end

  test "nil address when both sources invalid", %{service_request: service_request} do
    property_with_invalid_mailing = put_in(
      @property_with_individual_and_mailing,
      [Access.at(0), "property", "summary", "currentOwnerMetaData", "mailingAddresses"],
      [@mailing_address_invalid]
    )

    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => property_with_invalid_mailing}
      }}
    end)

    stub(Endato, :contact_id, fn "G1234567890" ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"person" => @contact_enrichment_invalid_address}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Both address sources invalid, should be nil
    assert result.address == nil
    
    # Should still have other enriched data
    assert result.name == "Jane Doe"
  end

  # Additional test implementations...
end
```

## Implementation Notes

### Address Validation Testing
- Test `address_valid?/1` function with various edge cases
- Ensure consistent validation between enrichment and mailing addresses
- Verify validation rejects empty strings, nil values, whitespace-only fields

### Mock Response Patterns
- Use different tahoeId values to trigger different enrichment responses
- Mock various HTTP status codes for error testing
- Test malformed JSON and missing fields scenarios

### Fallback Chain Testing
- Verify exact order: enrichment → mailing → nil
- Test each step in the chain independently
- Ensure no steps are skipped inappropriately

### Error Handling
- Verify no exceptions thrown for any error scenario
- Check that errors are logged appropriately
- Ensure partial failures don't prevent other processing

## Success Criteria
- [ ] Address precedence: enrichment → mailing → nil
- [ ] Invalid addresses properly rejected by validation
- [ ] Contact enrichment failures don't crash processing  
- [ ] Partial enrichment data handled gracefully
- [ ] Owners without tahoeId skip enrichment entirely
- [ ] Network errors result in fallback to basic info
- [ ] Address validation consistent across all sources
- [ ] Empty arrays in enrichment data handled correctly
- [ ] Malformed enrichment responses handled gracefully
- [ ] All fallback scenarios work correctly

## File Location
`test/boring/api/skiptrace_service/providers/endato_address_enrichment_test.exs`

## Context Dependencies
- Understanding of `to_skiptrace_result/2` address fallback logic
- Knowledge of `extract_address_from_enrichment/1` implementation
- Familiarity with `extract_mailing_address/1` function
- Understanding of `address_valid?/1` validation rules