# Chunk 7: Name Processing and Owner Identification
**Priority**: Low | **Estimated Tests**: 8 | **Impact**: Low

## Scope
Test name field processing, owner identification, and edge cases in name data extraction and validation.

## Test Cases

### 7.1 Missing isCorporationOrBusiness Field
**Scenario:** Owner object lacks `isCorporationOrBusiness` field, test fallback logic
**Implementation:**
- Owner with only `name` object, no corporation flag
- Test various `tahoeId` scenarios (present/nil) 
- Verify fallback logic: `nil` tahoeId → treated as business, present tahoeId → individual

**Expected Results:**
- Missing corp flag triggers fallback to tahoeId presence check
- `tahoeId: nil` results in `is_corporation_or_business: true`
- Valid `tahoeId` results in `is_corporation_or_business: false`  
- Processing continues normally with inferred business status

### 7.2 Empty/Null Name Fields
**Scenario:** Name object has empty or null required fields
**Implementation:**
- `fullName` is null, empty string, or missing
- All name fields are null/empty
- Mix of some fields present, others empty
- Test name validation and filtering

**Expected Results:**
- Completely empty names result in `nil` owner (filtered out)
- Partial names still create valid owner objects
- Empty string fields treated as missing
- No contacts created with completely empty names

### 7.3 Only companyName Present for Business
**Scenario:** Business entity with only company name, no individual fields
**Implementation:**
- `isCorporationOrBusiness: true`
- Valid `companyName` field
- Missing or empty `firstName/lastName` fields
- Test business name extraction logic

**Expected Results:**
- Contact name uses `companyName` value
- Missing individual fields don't cause errors
- Business identification works correctly
- Name construction robust for business-only data

### 7.4 Individual with companyName Field
**Scenario:** Individual person record that also has company name field
**Implementation:**
- `isCorporationOrBusiness: false`
- Both individual name fields AND `companyName` present
- Test field priority for individuals vs businesses

**Expected Results:**
- Individual name fields used (not company name)
- Company name field ignored for individuals
- Proper individual identification maintained
- Name priority logic works correctly

### 7.5 Name with Special Characters
**Scenario:** Names containing Unicode, accents, and special characters
**Implementation:**
- Names with accented characters (José, Müller)
- Names with apostrophes (O'Connor, D'Angelo)
- Names with hyphens (Mary-Jane, Jean-Luc)
- Unicode characters from various languages

**Expected Results:**
- Special characters preserved correctly
- No encoding issues or character loss
- Proper string handling for international names
- Name construction works with all character types

### 7.6 Very Long Names
**Scenario:** Boundary testing with unusually long name fields
**Implementation:**
- Names exceeding typical length expectations
- Very long first, middle, last, and company names
- Test memory usage and string handling

**Expected Results:**
- Long names handled without truncation
- No memory issues with large strings
- Name construction completes successfully
- No arbitrary length limits imposed

### 7.7 Name Fields with Whitespace Only
**Scenario:** Name fields containing only whitespace characters
**Implementation:**
- Fields with spaces, tabs, newlines only
- Mix of whitespace-only and valid fields
- Test whitespace detection and trimming

**Expected Results:**
- Whitespace-only fields treated as empty/missing
- Trimming logic applied consistently
- Valid fields still processed correctly
- No contacts with whitespace-only names

### 7.8 Malformed Name Object Structure
**Scenario:** Name object with invalid or unexpected structure
**Implementation:**
- Name field is not an object (string, array, etc.)
- Name object with unexpected nested structure
- Name object with circular references
- Various malformed data scenarios

**Expected Results:**
- Malformed name objects handled gracefully
- Pattern matching fails safely with fallback
- No exceptions thrown for invalid structures
- Consistent error handling for all malformed cases

## Required Mock Data

```elixir
# Owner missing isCorporationOrBusiness field with tahoeId
@owner_missing_corp_flag_with_tahoe %{
  "name" => %{
    "fullName" => "John Smith",
    "firstName" => "John",
    "lastName" => "Smith",
    "tahoeId" => "G*********0"
  }
  # No isCorporationOrBusiness field
}

# Owner missing isCorporationOrBusiness field without tahoeId
@owner_missing_corp_flag_no_tahoe %{
  "name" => %{
    "fullName" => "Business Entity",
    "firstName" => "Business",
    "lastName" => "Entity",
    "tahoeId" => nil
  }
  # No isCorporationOrBusiness field
}

# Owner with empty/null name fields
@owner_empty_name_fields %{
  "isCorporationOrBusiness" => false,
  "name" => %{
    "fullName" => "",      # Empty string
    "firstName" => nil,    # Null value
    "lastName" => "",      # Empty string
    "tahoeId" => "G*********0"
  }
}

# Owner with all null name fields
@owner_all_null_names %{
  "isCorporationOrBusiness" => false,
  "name" => %{
    "fullName" => nil,
    "firstName" => nil,
    "lastName" => nil,
    "tahoeId" => "G*********0"
  }
}

# Business with only company name
@owner_company_name_only %{
  "isCorporationOrBusiness" => true,
  "name" => %{
    "companyName" => "Acme Corporation",
    "fullName" => "",      # Empty individual name
    "firstName" => nil,    # No individual fields
    "lastName" => nil,
    "tahoeId" => nil
  }
}

# Individual with company name field present
@owner_individual_with_company %{
  "isCorporationOrBusiness" => false,
  "name" => %{
    "fullName" => "John Smith",
    "firstName" => "John", 
    "lastName" => "Smith",
    "companyName" => "Smith Consulting",  # Should be ignored
    "tahoeId" => "G*********0"
  }
}

# Names with special characters
@owner_special_characters %{
  "isCorporationOrBusiness" => false,
  "name" => %{
    "fullName" => "José María Aznar-López",
    "firstName" => "José",
    "middleName" => "María",
    "lastName" => "Aznar-López",
    "tahoeId" => "G*********0"
  }
}

# Very long names
@owner_long_names %{
  "isCorporationOrBusiness" => false,
  "name" => %{
    "fullName" => String.duplicate("VeryLongName", 50),  # 600 characters
    "firstName" => String.duplicate("VeryLongFirst", 20), # 280 characters
    "lastName" => String.duplicate("VeryLongLast", 20),   # 260 characters
    "tahoeId" => "G*********0"
  }
}

# Names with whitespace only
@owner_whitespace_names %{
  "isCorporationOrBusiness" => false,
  "name" => %{
    "fullName" => "   ",       # Spaces only
    "firstName" => "\t\n",     # Tabs and newlines
    "lastName" => "Smith",     # Valid field
    "tahoeId" => "G*********0"
  }
}

# Malformed name object structures
@owner_malformed_name_string %{
  "isCorporationOrBusiness" => false,
  "name" => "not_an_object"  # String instead of object
}

@owner_malformed_name_array %{
  "isCorporationOrBusiness" => false, 
  "name" => ["array", "instead", "of", "object"]
}

@owner_malformed_name_nested %{
  "isCorporationOrBusiness" => false,
  "name" => %{
    "fullName" => %{           # Object instead of string
      "nested" => "structure"
    },
    "tahoeId" => "G*********0"
  }
}

# Property responses for testing
@property_with_owner_missing_corp_flag [
  %{
    "poseidonId" => *********,
    "property" => %{
      "summary" => %{
        "currentOwners" => [@owner_missing_corp_flag_with_tahoe]
      }
    }
  }
]

@property_with_empty_names [
  %{
    "poseidonId" => *********,
    "property" => %{
      "summary" => %{
        "currentOwners" => [@owner_empty_name_fields, @owner_all_null_names]
      }
    }
  }
]

@property_with_malformed_names [
  %{
    "poseidonId" => *********,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          @owner_malformed_name_string,
          @owner_malformed_name_array,
          @owner_malformed_name_nested
        ]
      }
    }
  }
]
```

## Test Structure Template

```elixir
describe "Name Processing and Owner Identification" do
  test "missing isCorporationOrBusiness field uses tahoeId fallback", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_with_owner_missing_corp_flag}
      }}
    end)

    stub(Endato, :contact_id, fn "G*********0" ->
      {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should be treated as individual due to presence of tahoeId
    assert result.is_corporation_or_business? == false
    assert result.name == "John Smith"
  end

  test "empty name fields result in filtered owner", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_with_empty_names}
      }}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
    
    # Both owners should be filtered out due to empty names
  end

  test "business uses company name over individual fields", %{service_request: service_request} do
    property_with_business = [
      %{
        "poseidonId" => *********,
        "property" => %{
          "summary" => %{
            "currentOwners" => [@owner_company_name_only]
          }
        }
      }
    ]

    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => property_with_business}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    assert result.name == "Acme Corporation"
    assert result.is_corporation_or_business? == true
  end

  test "individual ignores company name field", %{service_request: service_request} do
    property_with_individual = [
      %{
        "poseidonId" => *********,
        "property" => %{
          "summary" => %{
            "currentOwners" => [@owner_individual_with_company]
          }
        }
      }
    ]

    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => property_with_individual}
      }}
    end)

    stub(Endato, :contact_id, fn "G*********0" ->
      {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should use individual name, not company name
    assert result.name == "John Smith"
    assert result.is_corporation_or_business? == false
  end

  test "handles special characters in names correctly", %{service_request: service_request} do
    property_with_special_chars = [
      %{
        "poseidonId" => *********,
        "property" => %{
          "summary" => %{
            "currentOwners" => [@owner_special_characters]
          }
        }
      }
    ]

    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => property_with_special_chars}
      }}
    end)

    stub(Endato, :contact_id, fn "G*********0" ->
      {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should preserve special characters
    assert result.name == "José María Aznar-López"
  end

  test "handles malformed name objects gracefully", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_with_malformed_names}
      }}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
    
    # All malformed owners should be filtered out
  end

  # Additional test implementations...
end
```

## Implementation Notes

### Name Validation Testing
- Test `extract_owner_name_parts/2` with various input combinations
- Verify proper filtering of invalid name data
- Test name construction logic for different scenarios

### Fallback Logic Testing
- Verify `tahoeId` presence check when corp flag missing
- Test boolean inference logic consistency
- Ensure fallback doesn't break other processing

### String Handling
- Test UTF-8 encoding preservation
- Verify trimming and whitespace handling
- Test string concatenation for name construction

### Pattern Matching
- Test fallback clauses in name extraction functions
- Verify graceful handling of unexpected structures
- Ensure consistent behavior across all name scenarios

## Success Criteria
- [ ] Missing corporation flag uses tahoeId presence fallback
- [ ] Empty names result in `nil` owner (filtered out)
- [ ] CompanyName takes precedence for businesses
- [ ] Special characters preserved correctly
- [ ] Long names handled without truncation
- [ ] Whitespace-only fields treated as empty
- [ ] Malformed name objects handled gracefully
- [ ] Individual vs business name priority works correctly

## File Location
`test/boring/api/skiptrace_service/providers/endato_name_processing_test.exs`

## Context Dependencies
- Understanding of `extract_owner_name_parts/2` pattern matching
- Knowledge of name field priority logic for businesses vs individuals
- Familiarity with Elixir string handling and Unicode support
- Understanding of how owner filtering works in the main processing flow