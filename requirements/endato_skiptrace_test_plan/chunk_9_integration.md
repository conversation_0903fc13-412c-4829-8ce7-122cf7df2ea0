# Chunk 9: Integration and Performance Scenarios
**Priority**: Low | **Estimated Tests**: 5 | **Impact**: Low

## Scope
Test integration scenarios, performance edge cases, and end-to-end workflows to ensure comprehensive system validation.

## Test Cases

### 9.1 Large Response with Many Properties and Owners
**Scenario:** Scale testing with realistic large-scale API responses
**Implementation:**
- Mock response with 10+ properties
- Each property has 5+ owners
- Mix of individuals and businesses
- Various enrichment success/failure scenarios
- Test memory usage and processing time

**Expected Results:**
- All valid contacts processed efficiently
- Performance remains acceptable for large datasets
- Memory usage stays reasonable
- No degradation in result quality
- Processing completes successfully

### 9.2 State Code Variations and Normalization
**Scenario:** Different state/province formats and geographic data handling
**Implementation:**
- Test two-letter state codes (IL, CA, TX)
- Test full state names (Illinois, California, Texas)
- Test invalid/unknown state codes
- Test international province codes
- Verify consistent state handling

**Expected Results:**
- Consistent state field in output addresses
- Proper handling of different state formats
- Invalid states handled gracefully
- No errors from unexpected geographic data
- Address structure remains consistent

### 9.3 Mixed Success/Failure Scenarios
**Scenario:** Partial processing with some successes and some failures
**Implementation:**
- Multiple properties, some valid, some invalid
- Multiple owners, some with successful enrichment, others failing
- Mix of network errors, API errors, and data corruption
- Test that partial failures don't prevent successful processing

**Expected Results:**
- Valid data processed correctly despite some failures
- Failures isolated and don't affect other processing
- Results include all successfully processed contacts
- No cascading failures
- Consistent result structure

### 9.4 End-to-End Workflow with All Features
**Scenario:** Complete integration test covering all major functionality
**Implementation:**
- Property search with retry logic
- Multiple owners (individuals and businesses)  
- Contact enrichment with various outcomes
- Address fallback chain (enrichment → mailing → nil)
- Email/phone filtering
- Full feature coverage in single test

**Expected Results:**
- Complete workflow works end-to-end
- All features integrate correctly
- Results demonstrate full functionality
- No feature interactions cause issues
- Realistic scenario processes successfully

### 9.5 Memory Usage with Large Datasets
**Scenario:** Resource management and memory efficiency testing
**Implementation:**
- Very large mock responses (50+ properties, 200+ owners)
- Test Stream vs Enum memory usage patterns
- Monitor memory consumption during processing
- Test garbage collection behavior
- Verify no memory leaks

**Expected Results:**
- Memory usage remains bounded and reasonable
- No memory leaks during processing
- Stream processing provides memory efficiency
- Large datasets don't cause memory issues
- Performance scales appropriately

## Required Mock Data

```elixir
# Large-scale property response
@large_property_response Enum.map(1..12, fn property_index ->
  %{
    "poseidonId" => 100_000_000 + property_index,
    "property" => %{
      "summary" => %{
        "currentOwners" => Enum.map(1..6, fn owner_index ->
          is_business = rem(owner_index, 3) == 0  # Every 3rd owner is business
          tahoe_id = if is_business, do: nil, else: "G#{property_index}#{owner_index}0000000"
          
          %{
            "isCorporationOrBusiness" => is_business,
            "name" => if is_business do
              %{
                "companyName" => "Property #{property_index} Business #{owner_index}",
                "fullName" => "Property #{property_index} Business #{owner_index}",
                "tahoeId" => tahoe_id
              }
            else
              %{
                "fullName" => "Owner #{property_index}-#{owner_index}",
                "firstName" => "Owner#{property_index}",
                "lastName" => "Person#{owner_index}",
                "tahoeId" => tahoe_id
              }
            end
          }
        end),
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [
            %{
              "addressLine1" => "#{property_index}00 Property St",
              "city" => "City#{property_index}",
              "state" => "IL",
              "zipCode" => "6270#{property_index}"
            }
          ]
        }
      }
    }
  }
end)

# State code variations
@property_with_state_variations [
  %{
    "poseidonId" => *********,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Illinois Owner",
              "tahoeId" => "G*********0"
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [
            %{
              "addressLine1" => "123 State Test St",
              "city" => "Springfield",
              "state" => "IL",  # Two-letter code
              "zipCode" => "62701"
            }
          ]
        }
      }
    }
  },
  %{
    "poseidonId" => 202020202,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "California Owner",
              "tahoeId" => "G2020202020"
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [
            %{
              "addressLine1" => "456 State Test Ave",
              "city" => "Los Angeles",
              "state" => "California",  # Full state name
              "zipCode" => "90210"
            }
          ]
        }
      }
    }
  },
  %{
    "poseidonId" => 203030303,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Invalid State Owner",
              "tahoeId" => "G2030303030"
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [
            %{
              "addressLine1" => "789 State Test Blvd",
              "city" => "Unknown City",
              "state" => "XX",  # Invalid state code
              "zipCode" => "00000"
            }
          ]
        }
      }
    }
  }
]

# Mixed success/failure scenario
@mixed_success_failure_response [
  # Valid property with successful processing
  %{
    "poseidonId" => 301010101,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Success Owner",
              "firstName" => "Success",
              "lastName" => "Owner",
              "tahoeId" => "G3010101010"
            }
          }
        ]
      }
    }
  },
  # Invalid property structure
  %{
    "invalid" => "property structure"
  },
  # Valid property with contact enrichment that will fail
  %{
    "poseidonId" => 302020202,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Enrichment Fail Owner",
              "firstName" => "Enrichment",
              "lastName" => "Fail",
              "tahoeId" => "G3020202020"
            }
          }
        ]
      }
    }
  },
  # Business owner (no enrichment attempted)
  %{
    "poseidonId" => *********,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => true,
            "name" => %{
              "companyName" => "Business Success Corp",
              "fullName" => "Business Success Corp",
              "tahoeId" => nil
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [
            %{
              "addressLine1" => "100 Business Success Dr",
              "city" => "Business City",
              "state" => "IL",
              "zipCode" => "60601"
            }
          ]
        }
      }
    }
  }
]

# Complete workflow test data
@complete_workflow_response [
  %{
    "poseidonId" => *********,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          # Individual with successful enrichment
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "John Complete",
              "firstName" => "John",
              "lastName" => "Complete",
              "tahoeId" => "G*********0"
            }
          },
          # Individual with failed enrichment (will use mailing address)
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Jane Fallback",
              "firstName" => "Jane",
              "lastName" => "Fallback",
              "tahoeId" => "G*********1"
            }
          },
          # Business owner (uses mailing address)
          %{
            "isCorporationOrBusiness" => true,
            "name" => %{
              "companyName" => "Complete Workflow LLC",
              "fullName" => "Complete Workflow LLC",
              "tahoeId" => nil
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [
            %{
              "addressLine1" => "123 Workflow Lane",
              "city" => "Integration City",
              "state" => "IL",
              "zipCode" => "62701"
            }
          ]
        }
      }
    }
  }
]

# Contact enrichment responses for various scenarios
@enrichment_success_complete %{
  "name" => %{
    "firstName" => "John",
    "lastName" => "Complete"
  },
  "emails" => [
    %{
      "email" => "<EMAIL>",
      "isValidated" => true,
      "isBusiness" => false
    },
    %{
      "email" => "<EMAIL>",
      "isValidated" => false,
      "isBusiness" => false
    }
  ],
  "phones" => [
    %{
      "number" => "************",
      "isConnected" => true
    },
    %{
      "number" => "************",
      "isConnected" => false
    }
  ],
  "addresses" => [
    %{
      "street" => "456 Enriched St",
      "city" => "Enriched City",
      "state" => "IL",
      "zip" => "62702"
    }
  ]
}
```

## Test Structure Template

```elixir
describe "Integration and Performance Scenarios" do
  test "processes large response with many properties and owners", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @large_property_response}
      }}
    end)

    # Mock contact enrichment with varied responses
    stub(Endato, :contact_id, fn tahoe_id ->
      case rem(String.to_integer(String.slice(tahoe_id, -1, 1)), 3) do
        0 -> {:ok, %Req.Response{status: 200, body: %{"person" => @enrichment_success_complete}}}
        1 -> {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
        2 -> {:error, "Network timeout"}
      end
    end)

    assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should have many contacts (12 properties × 6 owners = 72 total)
    # Businesses (every 3rd) = 24, Individuals = 48
    # Expected: 24 businesses + 48 individuals = 72 contacts
    assert length(results) == 72
    
    # Verify mix of business and individual contacts
    business_count = Enum.count(results, & &1.is_corporation_or_business?)
    individual_count = Enum.count(results, &(not &1.is_corporation_or_business?))
    assert business_count == 24
    assert individual_count == 48
  end

  test "handles state code variations consistently", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_with_state_variations}
      }}
    end)

    stub(Endato, :contact_id, fn _tahoe_id ->
      {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
    end)

    assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
    
    assert length(results) == 3
    
    # All should have consistent address structure with state field
    for result <- results do
      assert result.address != nil
      assert is_binary(result.address.state)
    end
    
    # Verify state values are preserved as-is from source
    states = Enum.map(results, & &1.address.state)
    assert "IL" in states
    assert "California" in states  
    assert "XX" in states
  end

  test "handles mixed success and failure scenarios", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @mixed_success_failure_response}
      }}
    end)

    stub(Endato, :contact_id, fn tahoe_id ->
      case tahoe_id do
        "G3010101010" -> 
          {:ok, %Req.Response{status: 200, body: %{"person" => @enrichment_success_complete}}}
        "G3020202020" -> 
          {:error, "Network timeout"}
        _ -> 
          {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
      end
    end)

    assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should have 3 valid contacts (invalid property skipped)
    assert length(results) == 3
    
    names = Enum.map(results, & &1.name)
    assert "Success Owner" in names
    assert "Enrichment Fail Owner" in names  
    assert "Business Success Corp" in names
    
    # Verify different processing outcomes
    success_owner = Enum.find(results, & &1.name == "Success Owner")
    assert length(success_owner.emails) > 0  # Has enriched data
    
    fail_owner = Enum.find(results, & &1.name == "Enrichment Fail Owner")
    assert fail_owner.emails == []  # No enriched data due to failure
    
    business_owner = Enum.find(results, & &1.name == "Business Success Corp")
    assert business_owner.is_corporation_or_business? == true
    assert business_owner.address != nil  # Has mailing address
  end

  test "complete end-to-end workflow with all features", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @complete_workflow_response}
      }}
    end)

    stub(Endato, :contact_id, fn tahoe_id ->
      case tahoe_id do
        "G*********0" -> 
          # Successful enrichment with address that takes precedence
          {:ok, %Req.Response{status: 200, body: %{"person" => @enrichment_success_complete}}}
        "G*********1" -> 
          # Failed enrichment, will fall back to mailing address
          {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
      end
    end)

    assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
    
    assert length(results) == 3
    
    # Test John Complete (successful enrichment)
    john = Enum.find(results, & &1.name == "John Complete")
    assert john.emails == ["<EMAIL>"]  # Filtered to validated only
    assert john.phone_numbers == ["************"]        # Filtered to connected only
    assert john.address.street == "456 Enriched St"      # Enrichment address precedence
    assert john.is_corporation_or_business? == false
    
    # Test Jane Fallback (failed enrichment, mailing address fallback)
    jane = Enum.find(results, & &1.name == "Jane Fallback")
    assert jane.emails == []                             # No enriched data
    assert jane.phone_numbers == []                      # No enriched data
    assert jane.address.street == "123 Workflow Lane"    # Mailing address fallback
    assert jane.is_corporation_or_business? == false
    
    # Test Complete Workflow LLC (business, mailing address)
    business = Enum.find(results, & &1.name == "Complete Workflow LLC")
    assert business.emails == []                         # No enrichment attempted
    assert business.phone_numbers == []                  # No enrichment attempted
    assert business.address.street == "123 Workflow Lane" # Mailing address
    assert business.is_corporation_or_business? == true
  end

  test "memory usage remains reasonable with large datasets", %{service_request: service_request} do
    # Create very large dataset
    very_large_response = Enum.map(1..50, fn property_index ->
      %{
        "poseidonId" => 500_000_000 + property_index,
        "property" => %{
          "summary" => %{
            "currentOwners" => Enum.map(1..10, fn owner_index ->
              %{
                "isCorporationOrBusiness" => false,
                "name" => %{
                  "fullName" => "Large Dataset Owner #{property_index}-#{owner_index}",
                  "firstName" => "Owner#{property_index}",
                  "lastName" => "Person#{owner_index}",
                  "tahoeId" => "G#{property_index}#{owner_index}000000"
                }
              }
            end)
          }
        }
      }
    end)

    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => very_large_response}
      }}
    end)

    stub(Endato, :contact_id, fn _tahoe_id ->
      {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
    end)

    # Measure memory before and after (basic check)
    memory_before = :erlang.memory(:total)
    
    assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
    
    memory_after = :erlang.memory(:total)
    memory_used = memory_after - memory_before
    
    # Should process all contacts (50 properties × 10 owners = 500)
    assert length(results) == 500
    
    # Memory usage should be reasonable (less than 50MB for this test)
    assert memory_used < 50_000_000
  end
end
```

## Implementation Notes

### Performance Testing
- Use realistic data sizes for performance testing
- Monitor memory usage patterns during processing
- Verify Stream processing provides memory efficiency
- Test with various data complexity levels

### Integration Testing
- Test all major features working together
- Verify no feature interactions cause conflicts
- Test realistic end-to-end scenarios
- Ensure consistent behavior across feature combinations

### State Handling
- Test various geographic data formats
- Verify consistent output regardless of input format
- Test invalid geographic data handling
- Ensure no assumptions about state format

### Error Isolation
- Verify partial failures don't cascade
- Test that processing continues despite some failures
- Ensure failed items don't affect successful processing
- Test various failure combinations

## Success Criteria
- [ ] Large responses processed efficiently without memory issues
- [ ] State codes handled consistently regardless of format
- [ ] Partial failures don't prevent successful processing
- [ ] Complete workflows work end-to-end with all features
- [ ] Memory usage scales appropriately with data size
- [ ] Performance remains acceptable for realistic data volumes
- [ ] All features integrate correctly without conflicts
- [ ] Geographic data variations handled gracefully
- [ ] Error isolation prevents cascading failures

## File Location
`test/boring/api/skiptrace_service/providers/endato_integration_test.exs`

## Context Dependencies
- Understanding of complete processing flow from property search to final results
- Knowledge of memory usage patterns in Elixir Stream processing
- Familiarity with integration testing patterns for multi-step workflows
- Understanding of performance characteristics for the full processing pipeline