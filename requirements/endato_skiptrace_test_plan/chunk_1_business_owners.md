# Chunk 1: Business Owner Core Functionality
**Priority**: High | **Estimated Tests**: 8 | **Impact**: Critical

## Scope
Test business owner handling, the primary new functionality that enables corporation/business contact returns with mailing addresses.

## Test Cases

### 1.1 Pure Business Owners
**Scenario:** Property owned entirely by corporations/businesses
**Implementation:**
- All `currentOwners` have `isCorporationOrBusiness: true`
- Include `companyName` in name object
- No `tahoeId` (businesses typically don't have person IDs)
- Include valid mailing address in `currentOwnerMetaData`

**Expected Results:**
- Business contacts included in results
- `is_corporation_or_business: true` flag set
- Names use `companyName` field
- Mailing address populated from metadata
- No contact enrichment API calls attempted

### 1.2 Mixed Individual and Business Owners
**Scenario:** Property with both individual and business current owners
**Implementation:**
- Mix of `isCorporationOrBusiness: true/false` owners
- Individuals with valid `tahoeId`, businesses without
- Mock contact enrichment success for individuals only
- Include mailing address for fallback

**Expected Results:**
- Both individual and business contacts returned
- Individuals have enriched data when available
- Businesses have mailing addresses only
- Proper `is_corporation_or_business` flags for each

### 1.3 Business with Company Name Precedence
**Scenario:** Business entity with both company and individual name fields
**Implementation:**
- `isCorporationOrBusiness: true`
- Name has `firstName/lastName` AND `companyName`
- Test that `companyName` takes precedence

**Expected Results:**
- Contact name uses `companyName` value
- Individual name fields ignored when `companyName` present
- Proper business identification

### 1.4 Business Without tahoeId
**Scenario:** Typical business scenario with no person identifier
**Implementation:**
- `isCorporationOrBusiness: true`
- `tahoeId: nil` or missing
- Valid `companyName`
- Mailing address available

**Expected Results:**
- Business contact created successfully
- No contact enrichment attempted
- Mailing address used for location data
- Proper business flags set

### 1.5 Business with Invalid Mailing Address
**Scenario:** Business owner with incomplete mailing address data
**Implementation:**
- Valid business owner data
- Mailing address missing required fields (street, city, state, zipCode)
- Test graceful degradation

**Expected Results:**
- Business contact still created
- Address field is `nil` due to validation failure
- No errors thrown during processing
- Business identification preserved

### 1.6 Business with Missing currentOwnerMetaData
**Scenario:** Business owner but no metadata section in response
**Implementation:**
- Valid business owner in `currentOwners`
- No `currentOwnerMetaData` section in property summary
- Test handling of missing mailing address source

**Expected Results:**
- Business contact created with basic info
- Address field is `nil`
- No errors from missing metadata
- Business processing continues normally

### 1.7 Multiple Businesses with Different Structures
**Scenario:** Multiple business owners with varying data completeness
**Implementation:**
- 3+ business owners in single property
- Mix of complete/incomplete business data
- Different company name structures
- Various mailing address qualities

**Expected Results:**
- All valid business contacts included
- Invalid business data filtered out gracefully
- Each business processed independently
- Consistent result structure

### 1.8 Business Name Construction Fallback
**Scenario:** Business without `companyName` but with individual name fields
**Implementation:**
- `isCorporationOrBusiness: true`
- Missing or empty `companyName`
- Valid `firstName/lastName` fields
- Test name construction fallback

**Expected Results:**
- Name constructed from individual fields as fallback
- Business flags still set correctly
- Graceful handling of missing company name
- Consistent name format

## Required Mock Data

```elixir
# Pure business owners response
@property_search_response_pure_business [
  %{
    "poseidonId" => *********,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => true,
            "name" => %{
              "companyName" => "Acme Storage LLC",
              "fullName" => "Acme Storage LLC",
              "firstName" => "Acme",
              "lastName" => "Storage LLC",
              "tahoeId" => nil
            }
          },
          %{
            "isCorporationOrBusiness" => true,
            "name" => %{
              "companyName" => "Investment Properties Inc",
              "fullName" => "Investment Properties Inc",
              "firstName" => "Investment",
              "lastName" => "Properties Inc",
              "tahoeId" => nil
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [
            %{
              "addressLine1" => "100 Business Park Dr",
              "city" => "Springfield",
              "state" => "IL",
              "zipCode" => "62701"
            }
          ]
        }
      }
    }
  }
]

# Mixed individual and business owners
@property_search_response_mixed_owners [
  %{
    "poseidonId" => *********,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "John Smith",
              "firstName" => "John",
              "lastName" => "Smith",
              "tahoeId" => "G*********0"
            }
          },
          %{
            "isCorporationOrBusiness" => true,
            "name" => %{
              "companyName" => "Smith Holdings LLC",
              "fullName" => "Smith Holdings LLC",
              "tahoeId" => nil
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [
            %{
              "addressLine1" => "200 Corporate Blvd",
              "city" => "Chicago",
              "state" => "IL",
              "zipCode" => "60601"
            }
          ]
        }
      }
    }
  }
]

# Business with company name precedence
@property_search_response_business_name_precedence [
  %{
    "poseidonId" => *********,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => true,
            "name" => %{
              "companyName" => "TechCorp Industries",
              "fullName" => "John Doe",  # Should be ignored
              "firstName" => "John",     # Should be ignored
              "lastName" => "Doe",       # Should be ignored
              "tahoeId" => nil
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [
            %{
              "addressLine1" => "300 Tech Lane",
              "city" => "Austin",
              "state" => "TX",
              "zipCode" => "73301"
            }
          ]
        }
      }
    }
  }
]

# Business with missing metadata
@property_search_response_business_no_meta [
  %{
    "poseidonId" => *********,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => true,
            "name" => %{
              "companyName" => "No Metadata Corp",
              "fullName" => "No Metadata Corp",
              "tahoeId" => nil
            }
          }
        ]
        # Note: No currentOwnerMetaData section
      }
    }
  }
]

# Mailing address variations
@mailing_address_complete %{
  "addressLine1" => "123 Complete St",
  "city" => "Complete City",
  "state" => "IL",
  "zipCode" => "62701"
}

@mailing_address_incomplete %{
  "addressLine1" => "",  # Missing required field
  "city" => "Incomplete City",
  "state" => "IL",
  "zipCode" => "62701"
}

# Contact enrichment for mixed scenario
@contact_enrichment_individual %{
  "name" => %{
    "firstName" => "John",
    "lastName" => "Smith"
  },
  "emails" => [
    %{
      "email" => "<EMAIL>",
      "isValidated" => true,
      "isBusiness" => false
    }
  ],
  "phones" => [
    %{
      "number" => "************",
      "isConnected" => true
    }
  ]
}
```

## Test Structure Template

```elixir
describe "Business Owner Core Functionality" do
  test "pure business owners are included with mailing addresses", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_search_response_pure_business}
      }}
    end)

    # No contact enrichment calls should be made for businesses
    # (verify by not stubbing Endato.contact_id)

    assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
    assert length(results) == 2

    # Check first business
    acme = Enum.find(results, &(&1.name == "Acme Storage LLC"))
    assert acme.is_corporation_or_business? == true
    assert acme.emails == []
    assert acme.phone_numbers == []
    assert acme.age == nil
    assert acme.address == %{
      street: "100 Business Park Dr",
      city: "Springfield", 
      state: "IL",
      postal_code: "62701"
    }

    # Check second business
    investment = Enum.find(results, &(&1.name == "Investment Properties Inc"))
    assert investment.is_corporation_or_business? == true
    assert investment.address == %{
      street: "100 Business Park Dr",
      city: "Springfield",
      state: "IL", 
      postal_code: "62701"
    }
  end

  # Additional test implementations...
end
```

## Success Criteria
- [ ] Business contacts included in results with `is_corporation_or_business: true`
- [ ] Mailing addresses properly extracted and validated
- [ ] Company names take precedence over individual name fields for businesses
- [ ] No contact enrichment attempted for businesses (no tahoeId calls)
- [ ] Graceful handling when mailing address data is invalid/missing
- [ ] Mixed individual/business scenarios work correctly
- [ ] Name construction fallback works when companyName missing
- [ ] Multiple businesses processed independently

## File Location
`test/boring/api/skiptrace_service/providers/endato_business_owners_test.exs`

## Implementation Notes
- Use `refute_called(Endato, :contact_id, :_, _)` to verify no enrichment calls for businesses
- Test both positive cases (valid data) and negative cases (invalid/missing data)
- Ensure consistent address structure between enrichment and mailing address fallbacks
- Verify business flag is set correctly in all scenarios