# Chunk 6: Mailing Address Processing and Validation
**Priority**: Medium | **Estimated Tests**: 7 | **Impact**: Medium

## Scope
Test comprehensive mailing address extraction, validation, and edge case handling from `currentOwnerMetaData`.

## Test Cases

### 6.1 Valid Mailing Address Extraction
**Scenario:** Complete and valid mailing address data extraction
**Implementation:**
- Valid `currentOwnerMetaData` with complete mailing address
- All required fields present: `addressLine1`, `city`, `state`, `zipCode`
- Test successful extraction and structure conversion

**Expected Results:**
- Mailing address extracted correctly
- Proper field mapping to standard address structure
- Address validation passes
- Consistent structure with enrichment addresses

### 6.2 Missing addressLine1
**Scenario:** Mailing address missing required street address
**Implementation:**
- Mailing address with empty or missing `addressLine1`
- Other fields (city, state, zipCode) are valid
- Test validation failure for missing street

**Expected Results:**
- Address validation fails due to missing street
- Function returns `nil` for address
- No errors thrown during validation
- Contact still created with other available data

### 6.3 Missing city/state/zipCode
**Scenario:** Mailing address missing other required fields
**Implementation:**
- Test each required field missing independently:
  - Missing `city`
  - Missing `state`  
  - Missing `zipCode`
- Valid `addressLine1` present

**Expected Results:**
- Address validation fails for each missing field
- Function returns `nil` for incomplete addresses
- Validation consistent across all required fields
- No partial address structures returned

### 6.4 Empty String Field Values
**Scenario:** Mailing address fields present but contain empty strings
**Implementation:**
- Fields exist in response but have empty string values `""`
- Test distinction between missing fields and empty values
- Verify validation treats empty strings as invalid

**Expected Results:**
- Empty string fields treated as missing/invalid
- Address validation fails for empty values
- No distinction between `nil` and `""` in validation
- Consistent validation logic

### 6.5 Multiple Mailing Addresses
**Scenario:** `mailingAddresses` array contains multiple address objects
**Implementation:**
- Array with 3+ mailing addresses
- First address valid, subsequent addresses may be invalid
- Test that only first address is used

**Expected Results:**
- Only first mailing address processed
- Other addresses in array ignored
- No iteration through full array
- Consistent behavior regardless of array length

### 6.6 Mailing Address with Extra Fields
**Scenario:** Mailing address contains additional unexpected fields
**Implementation:**
- Standard required fields plus extra data
- Test that extra fields don't interfere with processing
- Verify robust field extraction

**Expected Results:**
- Required fields extracted correctly
- Extra fields ignored gracefully  
- No errors from unexpected data
- Standard address structure maintained

### 6.7 currentOwnerMetaData Structure Variations
**Scenario:** Different metadata section structures and edge cases
**Implementation:**
- Missing `currentOwnerMetaData` entirely
- `currentOwnerMetaData` present but missing `mailingAddresses`
- `mailingAddresses` is not an array (wrong type)
- `mailingAddresses` is empty array `[]`

**Expected Results:**
- Missing metadata handled gracefully
- Missing mailing addresses result in `nil`
- Wrong data types handled without errors
- Empty arrays result in `nil` address

## Required Mock Data

```elixir
# Complete valid mailing address
@mailing_address_complete %{
  "addressLine1" => "123 Complete St",
  "city" => "Springfield", 
  "state" => "IL",
  "zipCode" => "62701"
}

# Missing addressLine1
@mailing_address_missing_street %{
  "addressLine1" => "",  # Empty string
  "city" => "Springfield",
  "state" => "IL", 
  "zipCode" => "62701"
}

# Missing city
@mailing_address_missing_city %{
  "addressLine1" => "123 Main St",
  # No city field
  "state" => "IL",
  "zipCode" => "62701"
}

# Missing state
@mailing_address_missing_state %{
  "addressLine1" => "123 Main St",
  "city" => "Springfield",
  # No state field
  "zipCode" => "62701"
}

# Missing zipCode
@mailing_address_missing_zip %{
  "addressLine1" => "123 Main St",
  "city" => "Springfield",
  "state" => "IL"
  # No zipCode field
}

# Empty string field values
@mailing_address_empty_fields %{
  "addressLine1" => "123 Main St",
  "city" => "",      # Empty string
  "state" => "",     # Empty string  
  "zipCode" => ""    # Empty string
}

# Multiple mailing addresses array
@multiple_mailing_addresses [
  %{
    "addressLine1" => "123 First St",
    "city" => "Springfield",
    "state" => "IL",
    "zipCode" => "62701"
  },
  %{
    "addressLine1" => "456 Second Ave", 
    "city" => "Chicago",
    "state" => "IL",
    "zipCode" => "60601"
  },
  %{
    "addressLine1" => "789 Third Blvd",
    "city" => "Peoria", 
    "state" => "IL",
    "zipCode" => "61601"
  }
]

# Mailing address with extra fields
@mailing_address_extra_fields %{
  "addressLine1" => "123 Main St",
  "city" => "Springfield",
  "state" => "IL",
  "zipCode" => "62701",
  "extraField1" => "unexpected data",
  "extraField2" => 12345,
  "nested" => %{
    "unexpected" => "structure"
  }
}

# Property responses with different metadata structures
@property_with_complete_mailing [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "John Smith",
              "tahoeId" => nil  # No enrichment, will use mailing
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => [@mailing_address_complete]
        }
      }
    }
  }
]

@property_missing_metadata [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Jane Doe", 
              "tahoeId" => nil
            }
          }
        ]
        # No currentOwnerMetaData section
      }
    }
  }
]

@property_missing_mailing_addresses [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Bob Wilson",
              "tahoeId" => nil
            }
          }
        ],
        "currentOwnerMetaData" => %{
          # No mailingAddresses field
          "otherData" => "value"
        }
      }
    }
  }
]

@property_wrong_mailing_type [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Alice Johnson",
              "tahoeId" => nil
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => "not_an_array"  # Wrong type
        }
      }
    }
  }
]

@property_empty_mailing_array [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "Charlie Brown",
              "tahoeId" => nil
            }
          }
        ],
        "currentOwnerMetaData" => %{
          "mailingAddresses" => []  # Empty array
        }
      }
    }
  }
]
```

## Test Structure Template

```elixir
describe "Mailing Address Processing and Validation" do
  test "extracts valid mailing address correctly", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_with_complete_mailing}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    assert result.address == %{
      street: "123 Complete St",
      city: "Springfield",
      state: "IL", 
      postal_code: "62701"
    }
  end

  test "rejects mailing address missing required fields", %{service_request: service_request} do
    # Test missing addressLine1
    property_missing_street = put_in(
      @property_with_complete_mailing,
      [Access.at(0), "property", "summary", "currentOwnerMetaData", "mailingAddresses"],
      [@mailing_address_missing_street]
    )

    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => property_missing_street}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Address should be nil due to validation failure
    assert result.address == nil
    assert result.name == "John Smith"  # Other data still present
  end

  test "uses only first mailing address from array", %{service_request: service_request} do
    property_multiple_addresses = put_in(
      @property_with_complete_mailing,
      [Access.at(0), "property", "summary", "currentOwnerMetaData", "mailingAddresses"],
      @multiple_mailing_addresses
    )

    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => property_multiple_addresses}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should use first address only
    assert result.address == %{
      street: "123 First St",
      city: "Springfield",
      state: "IL",
      postal_code: "62701"
    }
  end

  test "handles missing currentOwnerMetaData gracefully", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_missing_metadata}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    assert result.address == nil
    assert result.name == "Jane Doe"
  end

  test "handles wrong mailingAddresses data type", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200, 
        body: %{"propertyV2Records" => @property_wrong_mailing_type}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    assert result.address == nil
    assert result.name == "Alice Johnson"
  end

  # Additional test implementations...
end
```

## Implementation Notes

### Address Validation Testing
- Test `extract_mailing_address/1` function directly
- Verify `address_valid?/1` function behavior with edge cases
- Ensure consistent validation between mailing and enrichment addresses

### Field Mapping
- Verify proper mapping: `addressLine1` → `street`, `zipCode` → `postal_code`
- Test field case sensitivity and exact key matching
- Ensure consistent structure output

### Array Processing
- Test `List.first/1` behavior with various array types
- Verify proper handling of empty arrays vs nil arrays
- Test that array length doesn't affect performance

### Edge Case Handling
- Test various combinations of missing/empty fields
- Verify metadata structure variations handled gracefully
- Ensure no exceptions thrown for any input type

## Success Criteria
- [ ] Valid addresses extracted correctly with proper field mapping
- [ ] Invalid addresses result in `nil` (not errors)
- [ ] Only first mailing address used when multiple present
- [ ] Address validation consistent with enrichment validation
- [ ] Missing metadata sections handled gracefully
- [ ] Empty string fields treated as invalid
- [ ] Wrong data types handled without errors
- [ ] Extra fields in address data ignored gracefully

## File Location
`test/boring/api/skiptrace_service/providers/endato_mailing_address_test.exs`

## Context Dependencies
- Understanding of `extract_mailing_address/1` implementation
- Knowledge of `address_valid?/1` validation rules
- Familiarity with `get_in/2` usage for nested data extraction
- Understanding of `List.first/1` behavior with various inputs