# Chunk 4: Email and Phone Filtering Logic
**Priority**: Medium | **Estimated Tests**: 8 | **Impact**: Medium

## Scope
Test email validation and phone connection filtering logic to ensure only quality contact data is returned.

## Test Cases

### 4.1 Only Validated Emails Included
**Scenario:** Contact enrichment returns mix of validated and unvalidated emails
**Implementation:**
- Contact response with multiple emails
- Some emails have `isValidated: true`, others `isValidated: false`
- Verify only validated emails included in results

**Expected Results:**
- Only emails with `isValidated: true` in result
- Unvalidated emails filtered out
- Email order preserved from API response
- No errors during filtering

### 4.2 Only Connected Phones Included
**Scenario:** Contact enrichment returns mix of connected and disconnected phones
**Implementation:**
- Contact response with multiple phone numbers
- Some phones have `isConnected: true`, others `isConnected: false`
- Verify only connected phones included

**Expected Results:**
- Only phones with `isConnected: true` in result
- Disconnected phones filtered out
- Phone order preserved from API response
- No errors during filtering

### 4.3 Mixed Email Validation States
**Scenario:** Various email validation combinations and edge cases
**Implementation:**
- Test emails with `isValidated: null/undefined`
- Test emails missing `isValidated` field entirely
- Test mix of business and personal emails with validation
- Verify robust filtering logic

**Expected Results:**
- Only explicitly validated emails (`isValidated: true`) included
- Null/undefined validation treated as invalid
- Missing validation field treated as invalid
- Business vs personal distinction preserved

### 4.4 Mixed Phone Connection States
**Scenario:** Various phone connection combinations and edge cases
**Implementation:**
- Test phones with `isConnected: null/undefined`
- Test phones missing `isConnected` field entirely
- Test different phone types (mobile, landline) with connection status
- Verify robust filtering logic

**Expected Results:**
- Only explicitly connected phones (`isConnected: true`) included
- Null/undefined connection treated as disconnected
- Missing connection field treated as disconnected
- Phone type information preserved

### 4.5 Empty Email/Phone Arrays
**Scenario:** Contact enrichment with no valid emails or phones
**Implementation:**
- All emails have `isValidated: false`
- All phones have `isConnected: false`
- Verify empty arrays returned (not nil)

**Expected Results:**
- Result has empty arrays `[]` for emails and phones
- No nil values for contact fields
- Contact still created with other available data
- No errors from empty filtering results

### 4.6 Missing Email/Phone Fields Entirely
**Scenario:** Contact enrichment response lacks email/phone fields
**Implementation:**
- Contact response missing `emails` key entirely
- Contact response missing `phones` key entirely
- Test each missing field independently

**Expected Results:**
- Missing fields result in empty arrays `[]`
- No errors from missing fields
- Other contact data still processed correctly
- Consistent result structure maintained

### 4.7 Multiple Valid Emails and Phones
**Scenario:** Contact with multiple valid emails and phones
**Implementation:**
- Multiple emails all with `isValidated: true`
- Multiple phones all with `isConnected: true`
- Verify all valid items included
- Test ordering preservation

**Expected Results:**
- All valid emails included in result array
- All valid phones included in result array
- Order preserved from API response
- No arbitrary limits on valid items

### 4.8 Email/Phone with Invalid Validation Flags
**Scenario:** Contact data with malformed validation flags
**Implementation:**
- `isValidated` field has non-boolean values (strings, numbers)
- `isConnected` field has non-boolean values
- Test various invalid flag types

**Expected Results:**
- Non-boolean validation flags treated as invalid
- String "true"/"false" not treated as boolean true/false
- Only actual boolean `true` considered valid
- Robust type checking in filtering logic

## Required Mock Data

```elixir
# Mixed email validation states
@contact_enrichment_mixed_emails %{
  "name" => %{
    "firstName" => "John",
    "lastName" => "Smith"
  },
  "emails" => [
    %{
      "email" => "<EMAIL>",
      "isValidated" => true,
      "isBusiness" => false
    },
    %{
      "email" => "<EMAIL>", 
      "isValidated" => false,
      "isBusiness" => false
    },
    %{
      "email" => "<EMAIL>",
      "isValidated" => true,
      "isBusiness" => true
    },
    %{
      "email" => "<EMAIL>",
      "isBusiness" => false
      # Missing isValidated field
    }
  ],
  "phones" => [],
  "addresses" => []
}

# Mixed phone connection states
@contact_enrichment_mixed_phones %{
  "name" => %{
    "firstName" => "Jane",
    "lastName" => "Doe"
  },
  "emails" => [],
  "phones" => [
    %{
      "number" => "************",
      "type" => "mobile",
      "isConnected" => true
    },
    %{
      "number" => "************",
      "type" => "landline", 
      "isConnected" => false
    },
    %{
      "number" => "************",
      "type" => "mobile",
      "isConnected" => true
    },
    %{
      "number" => "************",
      "type" => "landline"
      # Missing isConnected field
    }
  ],
  "addresses" => []
}

# No valid emails or phones (all invalid)
@contact_enrichment_no_valid_contacts %{
  "name" => %{
    "firstName" => "Bob",
    "lastName" => "Wilson"
  },
  "emails" => [
    %{
      "email" => "<EMAIL>",
      "isValidated" => false,
      "isBusiness" => false
    }
  ],
  "phones" => [
    %{
      "number" => "************",
      "type" => "mobile",
      "isConnected" => false
    }
  ],
  "addresses" => []
}

# Missing email/phone fields entirely
@contact_enrichment_missing_fields %{
  "name" => %{
    "firstName" => "Alice",
    "lastName" => "Johnson"
  },
  "addresses" => []
  # Missing emails and phones fields entirely
}

# Multiple valid items
@contact_enrichment_multiple_valid %{
  "name" => %{
    "firstName" => "Charlie",
    "lastName" => "Brown"
  },
  "emails" => [
    %{
      "email" => "<EMAIL>",
      "isValidated" => true,
      "isBusiness" => false
    },
    %{
      "email" => "<EMAIL>",
      "isValidated" => true,
      "isBusiness" => false
    },
    %{
      "email" => "<EMAIL>",
      "isValidated" => true,
      "isBusiness" => false
    }
  ],
  "phones" => [
    %{
      "number" => "************",
      "type" => "mobile",
      "isConnected" => true
    },
    %{
      "number" => "************", 
      "type" => "landline",
      "isConnected" => true
    }
  ],
  "addresses" => []
}

# Invalid validation flags
@contact_enrichment_invalid_flags %{
  "name" => %{
    "firstName" => "David",
    "lastName" => "Miller"
  },
  "emails" => [
    %{
      "email" => "<EMAIL>",
      "isValidated" => "true",  # String, not boolean
      "isBusiness" => false
    },
    %{
      "email" => "<EMAIL>",
      "isValidated" => 1,       # Number, not boolean
      "isBusiness" => false
    },
    %{
      "email" => "<EMAIL>", 
      "isValidated" => nil,     # Null value
      "isBusiness" => false
    }
  ],
  "phones" => [
    %{
      "number" => "************",
      "type" => "mobile",
      "isConnected" => "true"   # String, not boolean
    },
    %{
      "number" => "************",
      "type" => "mobile", 
      "isConnected" => 1        # Number, not boolean
    }
  ],
  "addresses" => []
}
```

## Test Structure Template

```elixir
describe "Email and Phone Filtering Logic" do
  test "only validated emails included in results", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_with_individual}
      }}
    end)

    stub(Endato, :contact_id, fn "G1234567890" ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"person" => @contact_enrichment_mixed_emails}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should only include validated emails
    assert length(result.emails) == 2
    assert "<EMAIL>" in result.emails
    assert "<EMAIL>" in result.emails
    refute "<EMAIL>" in result.emails
    refute "<EMAIL>" in result.emails
  end

  test "only connected phones included in results", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_with_individual}
      }}
    end)

    stub(Endato, :contact_id, fn "G1234567890" ->
      {:ok, %Req.Response{
        status: 200, 
        body: %{"person" => @contact_enrichment_mixed_phones}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should only include connected phones
    assert length(result.phone_numbers) == 2
    assert "************" in result.phone_numbers
    assert "************" in result.phone_numbers
    refute "************" in result.phone_numbers
    refute "************" in result.phone_numbers
  end

  test "empty arrays when no valid contacts", %{service_request: service_request} do
    stub(Endato, :property_search, fn _params ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"propertyV2Records" => @property_with_individual}
      }}
    end)

    stub(Endato, :contact_id, fn "G1234567890" ->
      {:ok, %Req.Response{
        status: 200,
        body: %{"person" => @contact_enrichment_no_valid_contacts}
      }}
    end)

    assert {:ok, %Result{contacts: [result]}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should have empty arrays, not nil
    assert result.emails == []
    assert result.phone_numbers == []
    assert result.name == "Bob Wilson"  # Other data still present
  end

  # Additional test implementations...
end
```

## Implementation Notes

### Filtering Logic Testing
- Test `extract_emails_from_enrichment/1` function directly
- Test `extract_phone_numbers_from_enrichment/1` function directly
- Verify boolean type checking is strict (only `true` is valid)

### Edge Case Handling
- Test null, undefined, and missing field scenarios
- Verify non-boolean values are treated as invalid
- Test empty arrays vs missing fields

### Order Preservation
- Verify API response order is maintained in results
- Test with multiple valid items to confirm ordering
- Ensure filtering doesn't change relative order

### Type Safety
- Test various data types for validation flags
- Ensure robust type checking prevents errors
- Verify consistent behavior across different invalid types

## Success Criteria
- [ ] Only emails with `isValidated: true` included
- [ ] Only phones with `isConnected: true` included  
- [ ] Empty arrays returned when no valid items (not nil)
- [ ] Missing fields result in empty arrays, not errors
- [ ] Invalid validation flags handled gracefully
- [ ] Multiple valid items all included in results
- [ ] Order preserved from API response
- [ ] Strict boolean type checking for validation flags

## File Location
`test/boring/api/skiptrace_service/providers/endato_contact_filtering_test.exs`

## Context Dependencies
- Understanding of `extract_emails_from_enrichment/1` implementation
- Knowledge of `extract_phone_numbers_from_enrichment/1` implementation
- Familiarity with Elixir boolean type checking behavior
- Understanding of Stream vs Enum usage in filtering logic