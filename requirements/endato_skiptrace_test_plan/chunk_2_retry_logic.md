# Chunk 2: Retry Logic and Parameter Handling
**Priority**: High | **Estimated Tests**: 6 | **Impact**: Critical

## Scope  
Test the retry mechanism with business_name parameter, ensuring proper fallback behavior when initial searches fail.

## Test Cases

### 2.1 Successful Retry with Business Name Removal
**Scenario:** Initial search with business_name returns empty, retry without it succeeds
**Implementation:**
- Mock first call with `business_name` parameter to return empty `propertyV2Records`
- Mock second call without `business_name` to return valid data
- Verify retry_count increments correctly
- Track parameter changes between calls

**Expected Results:**
- First search fails with empty results
- Second search triggered automatically
- business_name parameter removed on retry
- Final results contain expected contacts
- Exactly 2 API calls made

### 2.2 Retry Limit Exceeded
**Scenario:** Multiple retries still return empty results, respect retry limit
**Implementation:**
- Mock all calls to return empty `propertyV2Records`
- Verify retry stops after retry_count > 1
- Ensure no infinite retry loops

**Expected Results:**
- Maximum of 2 total API calls made
- Function returns empty results after retry limit
- No additional API calls after limit reached
- Graceful termination

### 2.3 No Retry Without Business Name
**Scenario:** Initial search without business_name returns empty, no retry attempted
**Implementation:**
- Mock search params without `business_name` to return empty
- Verify no retry is attempted
- Single API call should be made

**Expected Results:**
- Single API call made
- Empty results returned immediately
- No retry logic triggered
- Parameters unchanged

### 2.4 Retry with Different Response Structures
**Scenario:** Test retry behavior with various failure response types
**Implementation:**
- Test retry with HTTP errors on first call
- Test retry with malformed responses
- Test retry with non-200 status codes
- Verify retry only triggers on empty propertyV2Records

**Expected Results:**
- Retry only triggered by empty propertyV2Records array
- Other error types don't trigger retry
- Proper error handling for each response type
- Consistent retry logic

### 2.5 Retry Count Tracking
**Scenario:** Verify internal retry count state management
**Implementation:**
- Mock multiple scenarios requiring different retry counts
- Verify count increments correctly
- Test boundary conditions (exactly at limit)

**Expected Results:**
- Retry count starts at 0
- Increments by 1 on each retry attempt
- Stops when count > 1 (max 2 total calls)
- Count resets for new skiptrace calls

### 2.6 Retry with API Errors
**Scenario:** Test retry behavior when API calls result in errors
**Implementation:**
- Mock first call to raise network error
- Mock second call to succeed
- Verify retry logic handles API failures

**Expected Results:**
- API errors don't trigger retry (only empty results do)
- Error responses handled gracefully
- Function returns empty results for API errors
- No infinite retry on network failures

## Required Mock Data

```elixir
# Empty response that triggers retry
@property_search_empty_response %{
  "propertyV2Records" => []
}

# Successful response for retry scenario
@property_search_retry_success [
  %{
    "poseidonId" => 123456789,
    "property" => %{
      "summary" => %{
        "currentOwners" => [
          %{
            "isCorporationOrBusiness" => false,
            "name" => %{
              "fullName" => "John Doe",
              "firstName" => "John",
              "lastName" => "Doe",
              "tahoeId" => "G1234567890"
            }
          }
        ]
      }
    }
  }
]

# Search parameters with business_name
@search_params_with_business_name [
  business_name: "Test Business",
  address_line1: "123 Main St",
  address_line2: "Springfield, IL 62701"
]

# Search parameters without business_name (for retry)
@search_params_without_business_name [
  address_line1: "123 Main St", 
  address_line2: "Springfield, IL 62701"
]

# Error responses for testing
@api_error_response {:error, "Network timeout"}

@http_error_response %Req.Response{
  status: 500,
  body: %{"error" => "Internal server error"}
}

@malformed_response %Req.Response{
  status: 200,
  body: %{"invalid" => "structure"}
}
```

## Test Structure Template

```elixir
describe "Retry Logic and Parameter Handling" do
  test "successful retry with business_name removal", %{service_request: service_request} do
    # Track API calls and parameters
    call_count = Agent.start_link(fn -> 0 end)
    call_params = Agent.start_link(fn -> [] end)

    stub(Endato, :property_search, fn params ->
      count = Agent.get_and_update(call_count, &{&1, &1 + 1})
      Agent.update(call_params, &[params | &1])

      case count do
        0 -> 
          # First call with business_name returns empty
          assert Keyword.has_key?(params, :business_name)
          {:ok, %Req.Response{
            status: 200,
            body: @property_search_empty_response
          }}
        1 ->
          # Second call without business_name returns results
          refute Keyword.has_key?(params, :business_name)
          {:ok, %Req.Response{
            status: 200,
            body: %{"propertyV2Records" => @property_search_retry_success}
          }}
      end
    end)

    assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
    
    # Verify results
    assert length(results) == 1
    assert Enum.at(results, 0).name == "John Doe"
    
    # Verify exactly 2 calls made
    final_count = Agent.get(call_count, & &1)
    assert final_count == 2
    
    # Verify parameter changes
    params_list = Agent.get(call_params, & &1) |> Enum.reverse()
    assert Keyword.has_key?(Enum.at(params_list, 0), :business_name)
    refute Keyword.has_key?(Enum.at(params_list, 1), :business_name)
  end

  test "retry limit exceeded returns empty results", %{service_request: service_request} do
    call_count = Agent.start_link(fn -> 0 end)

    stub(Endato, :property_search, fn _params ->
      Agent.update(call_count, &(&1 + 1))
      {:ok, %Req.Response{
        status: 200,
        body: @property_search_empty_response
      }}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should make maximum 2 calls (initial + 1 retry)
    final_count = Agent.get(call_count, & &1)
    assert final_count == 2
  end

  test "no retry without business_name parameter", %{service_request: service_request} do
    call_count = Agent.start_link(fn -> 0 end)

    stub(Endato, :property_search, fn params ->
      Agent.update(call_count, &(&1 + 1))
      # Verify no business_name in parameters
      refute Keyword.has_key?(params, :business_name)
      {:ok, %Req.Response{
        status: 200,
        body: @property_search_empty_response
      }}
    end)

    assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
    
    # Should make only 1 call (no retry)
    final_count = Agent.get(call_count, & &1)
    assert final_count == 1
  end

  # Additional test implementations...
end
```

## Implementation Notes

### Mock Strategy
- Use `Agent` to track call count and parameters across multiple API calls
- Implement different responses based on call count to simulate retry scenarios
- Verify exact parameters passed to API on each call

### Parameter Verification
- Check for presence/absence of `business_name` parameter
- Ensure other parameters remain unchanged during retry
- Verify parameter list structure consistency

### Call Count Tracking  
- Use `Agent.start_link` in test setup to track state
- Increment counter on each API call
- Assert exact call counts after test execution

### Edge Case Testing
- Test exactly at retry limit boundary
- Test various empty response scenarios
- Verify no retry on non-empty responses

## Success Criteria
- [ ] Retry triggered when business_name present and results empty
- [ ] Retry removes business_name parameter from search params
- [ ] Retry limit of 1 respected (max 2 total calls)
- [ ] No retry when business_name not in original parameters
- [ ] Successful results halt retry process immediately
- [ ] API errors don't trigger retry (only empty results do)
- [ ] Retry count tracking works correctly
- [ ] Parameter modifications work as expected

## File Location
`test/boring/api/skiptrace_service/providers/endato_retry_logic_test.exs`

## Context Dependencies
- Understanding of `do_run_skiptrace/2` retry logic implementation
- Knowledge of Endato.property_search parameter structure
- Familiarity with Mimic stubbing for stateful mock responses