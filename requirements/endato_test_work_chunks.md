# Endato Skiptrace Test Implementation - Work Chunks

This document breaks down the comprehensive test plan into manageable chunks that can be executed by multiple Claude agents without overloading context windows.

## Chunk Distribution Strategy

### Context Window Optimization
- Each chunk contains 8-12 specific test cases
- Complete mock data provided for each chunk
- Clear success criteria for validation
- Independent execution (no cross-chunk dependencies)

### Agent Assignment
Chunks are prioritized by business impact and can be assigned to agents as:
- **High Priority** (Chunks 1-3): Core business functionality
- **Medium Priority** (Chunks 4-6): Error handling and edge cases  
- **Low Priority** (Chunks 7-9): Comprehensive coverage and integration

---

## Chunk 1: Business Owner Core Functionality
**Priority**: High | **Estimated Tests**: 8 | **Impact**: Critical

### Scope
Test business owner handling, the primary new functionality that enables corporation/business contact returns with mailing addresses.

### Test Cases
1. **Pure business owners** - All current owners are corporations
2. **Mixed individual and business owners** - Combination scenario
3. **Business with company name precedence** - Name field priority testing
4. **Business without tahoeId** - Typical business scenario
5. **Business with invalid mailing address** - Graceful degradation
6. **Business with missing currentOwnerMetaData** - Edge case handling
7. **Multiple businesses with different structures** - Variety testing
8. **Business name construction fallback** - When companyName missing

### Required Mock Data
```elixir
@property_search_response_pure_business    # All business owners
@property_search_response_mixed_owners     # Mix of individual/business
@property_search_response_business_no_meta # Missing metadata
@mailing_address_complete                  # Valid mailing address
@mailing_address_incomplete                # Invalid mailing address
```

### Success Criteria
- [ ] Business contacts included in results with `is_corporation_or_business: true`
- [ ] Mailing addresses properly extracted and validated
- [ ] Company names take precedence over individual name fields
- [ ] No contact enrichment attempted for businesses (no tahoeId calls)
- [ ] Graceful handling when mailing address data is invalid/missing

### File Location
`test/boring/api/skiptrace_service/providers/endato_business_owners_test.exs`

---

## Chunk 2: Retry Logic and Parameter Handling
**Priority**: High | **Estimated Tests**: 6 | **Impact**: Critical

### Scope  
Test the retry mechanism with business_name parameter, ensuring proper fallback behavior when initial searches fail.

### Test Cases
1. **Successful retry with business_name removal** - Core retry scenario
2. **Retry limit exceeded** - Prevents infinite loops
3. **No retry when business_name not present** - Conditional retry logic
4. **Retry with different response structures** - Various failure modes
5. **Retry count tracking** - Internal state management
6. **Retry with API errors** - Error scenario retries

### Required Mock Data
```elixir
@property_search_empty_response            # Empty propertyV2Records
@property_search_retry_success             # Success on retry
@search_params_with_business_name          # Initial params
@search_params_without_business_name       # Retry params
```

### Implementation Notes
- Mock Endato.property_search to return different responses based on call count
- Use process state or mocking sequence to track retry attempts
- Verify exact parameters passed to API on retry

### Success Criteria
- [ ] Retry triggered when business_name present and results empty
- [ ] Retry removes business_name parameter 
- [ ] Retry limit of 1 respected (max 2 total calls)
- [ ] No retry when business_name not in original parameters
- [ ] Successful results halt retry process immediately

### File Location
`test/boring/api/skiptrace_service/providers/endato_retry_logic_test.exs`

---

## Chunk 3: Contact Enrichment and Address Fallback
**Priority**: High | **Estimated Tests**: 10 | **Impact**: High

### Scope
Test the complete address fallback chain: enrichment → mailing address → nil, plus contact enrichment edge cases.

### Test Cases
1. **Enrichment address takes precedence** - Happy path
2. **Mailing address fallback when enrichment invalid** - Primary fallback
3. **Nil address when both sources invalid** - Final fallback
4. **Contact enrichment with non-200 response** - API error handling
5. **Contact enrichment with malformed person data** - Data structure errors
6. **Partial contact enrichment data** - Missing fields in response
7. **Contact enrichment timeout/network error** - Infrastructure failures
8. **Multiple address sources with different validity** - Precedence matrix
9. **Address validation with edge case data** - Boundary testing
10. **Contact enrichment with empty arrays** - Empty but valid responses

### Required Mock Data
```elixir
@contact_enrichment_valid_address          # Complete enrichment address
@contact_enrichment_invalid_address        # Incomplete enrichment address
@contact_enrichment_partial_data           # Missing some fields
@contact_enrichment_malformed              # Invalid structure
@mailing_address_valid                     # Valid fallback address
@mailing_address_invalid                   # Invalid fallback address
```

### Success Criteria
- [ ] Address precedence: enrichment → mailing → nil
- [ ] Invalid addresses properly rejected by validation
- [ ] Contact enrichment failures don't crash processing
- [ ] Partial enrichment data handled gracefully
- [ ] Owners without tahoeId skip enrichment entirely

### File Location
`test/boring/api/skiptrace_service/providers/endato_address_enrichment_test.exs`

---

## Chunk 4: Email and Phone Filtering Logic
**Priority**: Medium | **Estimated Tests**: 8 | **Impact**: Medium

### Scope
Test email validation and phone connection filtering logic to ensure only quality contact data is returned.

### Test Cases
1. **Only validated emails included** - Core email filtering
2. **Only connected phones included** - Core phone filtering  
3. **Mixed email validation states** - Partial filtering
4. **Mixed phone connection states** - Partial filtering
5. **Empty email/phone arrays** - No valid data scenarios
6. **Missing email/phone fields entirely** - Field absence handling
7. **Multiple valid emails and phones** - Multiple item handling
8. **Email/phone with null/invalid validation flags** - Data quality issues

### Required Mock Data
```elixir
@contact_enrichment_mixed_emails           # Validated and unvalidated emails
@contact_enrichment_mixed_phones           # Connected and disconnected phones
@contact_enrichment_no_emails              # Missing emails array
@contact_enrichment_no_phones              # Missing phones array
@contact_enrichment_invalid_validation     # Null/invalid validation flags
```

### Success Criteria
- [ ] Only emails with `isValidated: true` included
- [ ] Only phones with `isConnected: true` included  
- [ ] Empty arrays returned when no valid items
- [ ] Missing fields result in empty arrays, not errors
- [ ] Invalid validation flags handled gracefully

### File Location
`test/boring/api/skiptrace_service/providers/endato_contact_filtering_test.exs`

---

## Chunk 5: Property Structure and Response Variations
**Priority**: Medium | **Estimated Tests**: 9 | **Impact**: Medium

### Scope
Test various property response structures and API response formats to ensure robust parsing.

### Test Cases
1. **Missing currentOwners array** - Property without owners
2. **Empty currentOwners array** - Property with no current owners
3. **Missing property.summary section** - Incomplete property structure
4. **Multiple properties in response** - Array processing
5. **Invalid property objects in array** - Mixed valid/invalid items
6. **propertyV2Records not an array** - Wrong data type
7. **propertyV2Records missing from response** - Missing key
8. **Completely empty response body** - Empty object scenario
9. **Property with only previousOwners** - No current owners scenario

### Required Mock Data
```elixir
@property_response_no_current_owners       # Missing currentOwners
@property_response_empty_current_owners    # Empty currentOwners array
@property_response_no_summary              # Missing summary section
@property_response_multiple_properties     # Multiple property records
@property_response_invalid_structure       # Malformed property objects
@property_response_wrong_type              # propertyV2Records not array
@empty_response_body                       # {}
```

### Success Criteria
- [ ] Missing sections result in empty results, not errors
- [ ] Multiple properties processed correctly
- [ ] Invalid property objects skipped gracefully  
- [ ] Wrong data types handled without crashing
- [ ] Empty response bodies return empty results

### File Location
`test/boring/api/skiptrace_service/providers/endato_property_structure_test.exs`

---

## Chunk 6: Mailing Address Processing and Validation
**Priority**: Medium | **Estimated Tests**: 7 | **Impact**: Medium

### Scope
Test comprehensive mailing address extraction, validation, and edge case handling.

### Test Cases
1. **Valid mailing address extraction** - Happy path
2. **Missing addressLine1** - Required field missing
3. **Missing city/state/zipCode** - Other required fields missing
4. **Empty string field values** - Empty but present fields
5. **Multiple mailing addresses** - Array with multiple items
6. **Mailing address with extra fields** - Unexpected data presence
7. **currentOwnerMetaData structure variations** - Different metadata formats

### Required Mock Data
```elixir
@mailing_address_complete                  # All required fields present
@mailing_address_missing_street           # Missing addressLine1
@mailing_address_missing_city             # Missing city
@mailing_address_empty_fields             # Empty string values
@multiple_mailing_addresses               # Array with 3+ addresses
@metadata_structure_variations            # Different metadata formats
```

### Success Criteria
- [ ] Valid addresses extracted correctly
- [ ] Invalid addresses result in nil (not errors)
- [ ] Only first mailing address used when multiple present
- [ ] Address validation consistent with enrichment validation
- [ ] Missing metadata sections handled gracefully

### File Location
`test/boring/api/skiptrace_service/providers/endato_mailing_address_test.exs`

---

## Chunk 7: Name Processing and Owner Identification
**Priority**: Low | **Estimated Tests**: 8 | **Impact**: Low

### Scope
Test name field processing, owner identification, and edge cases in name data.

### Test Cases
1. **Missing isCorporationOrBusiness field** - Fallback logic testing
2. **Empty/null name fields** - Data quality issues
3. **Only companyName present for business** - Business name handling
4. **Individual with companyName field** - Field priority testing
5. **Name with special characters** - Unicode and encoding
6. **Very long names** - Boundary testing
7. **Name fields with whitespace only** - Empty content detection
8. **Malformed name object structure** - Invalid nested data

### Required Mock Data
```elixir
@owner_missing_corp_flag                   # No isCorporationOrBusiness field
@owner_empty_name_fields                   # Null/empty name values
@owner_company_name_only                   # Only companyName present
@owner_special_characters                  # Unicode in names
@owner_long_names                          # Boundary length names
@owner_whitespace_names                    # Whitespace-only fields
@owner_malformed_name                      # Invalid name structure
```

### Success Criteria
- [ ] Missing corporation flag uses tahoeId presence fallback
- [ ] Empty names result in nil owner (filtered out)
- [ ] CompanyName takes precedence for businesses
- [ ] Special characters preserved correctly
- [ ] Long names handled without truncation
- [ ] Whitespace-only fields treated as empty

### File Location
`test/boring/api/skiptrace_service/providers/endato_name_processing_test.exs`

---

## Chunk 8: API Error Handling and Edge Cases
**Priority**: Low | **Estimated Tests**: 6 | **Impact**: Low

### Scope
Test comprehensive API error handling, network issues, and unexpected response scenarios.

### Test Cases
1. **Network timeout errors** - Infrastructure failures
2. **Invalid API credentials** - Authentication errors
3. **Rate limiting responses** - API quota issues  
4. **Partial response corruption** - Incomplete data transmission
5. **Unexpected HTTP status codes** - Non-standard responses
6. **API response with unexpected content-type** - Wrong response format

### Required Mock Data
```elixir
@network_timeout_error                     # Timeout simulation
@invalid_credentials_response              # 401/403 responses
@rate_limit_response                       # 429 responses
@partial_response_corruption               # Incomplete JSON
@unexpected_status_codes                   # Various HTTP statuses
```

### Success Criteria
- [ ] All error scenarios return empty results gracefully
- [ ] No exceptions propagated to caller
- [ ] Appropriate logging for different error types
- [ ] Consistent error response structure
- [ ] Timeout handling doesn't block indefinitely

### File Location
`test/boring/api/skiptrace_service/providers/endato_error_handling_test.exs`

---

## Chunk 9: Integration and Performance Scenarios
**Priority**: Low | **Estimated Tests**: 5 | **Impact**: Low

### Scope
Test integration scenarios, performance edge cases, and end-to-end workflows.

### Test Cases
1. **Large response with many properties and owners** - Scale testing
2. **State code variations and normalization** - Geographic data handling  
3. **Mixed success/failure scenarios** - Partial processing
4. **End-to-end workflow with all features** - Complete integration
5. **Memory usage with large datasets** - Resource management

### Required Mock Data
```elixir
@large_property_response                   # 10+ properties, 50+ owners
@state_code_variations                     # Different state formats
@mixed_success_failure_response           # Some valid, some invalid data
@complete_workflow_response               # Full feature coverage
```

### Success Criteria
- [ ] Large responses processed efficiently
- [ ] State codes normalized consistently
- [ ] Partial failures don't prevent successful processing
- [ ] Complete workflows work end-to-end
- [ ] Memory usage remains reasonable

### File Location
`test/boring/api/skiptrace_service/providers/endato_integration_test.exs`

---

## Agent Execution Instructions

### For Each Chunk:
1. **Read the comprehensive test plan** - Understand overall context
2. **Focus on assigned chunk only** - Don't implement other chunks  
3. **Create specified test file** - Use exact filename provided
4. **Follow existing test patterns** - Match current code style
5. **Include all required mock data** - Create complete test fixtures
6. **Verify success criteria** - Ensure all criteria met
7. **Run tests to confirm** - Validate implementation works

### Code Standards:
- Use existing module attribute pattern for mock data (`@property_search_response_*`)
- Follow current test structure (setup, stub, execute, assert)
- Include descriptive test names and documentation comments
- Use comprehensive assertions that verify all expected behavior
- Handle both positive and negative test cases

### Completion Validation:
Each chunk is complete when:
- ✅ All test cases implemented and passing
- ✅ Mock data covers all scenarios  
- ✅ Success criteria fully verified
- ✅ Code follows project conventions
- ✅ No existing tests broken

### Context Management:
- Each chunk is self-contained
- No dependencies between chunks
- Can be implemented in parallel
- Clear boundaries prevent overlap
- Agent can focus on single chunk without full codebase context