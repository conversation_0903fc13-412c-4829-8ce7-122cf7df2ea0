# Endato Skiptrace Service - Comprehensive Test Plan

## Overview
This document outlines a complete testing strategy for the Endato Skiptrace Service implementation (`Boring.Api.SkiptraceService.Providers.Endato`). The plan is designed to be executed by Claude agents with clear, actionable test specifications.

## Current Test Coverage Analysis
**Existing Tests:**
- ✅ Individual owners with successful contact enrichment
- ✅ Mixed individual owners (some with/without enrichment)
- ✅ API errors and non-200 HTTP responses
- ✅ Empty response handling
- ✅ Malformed response structure
- ✅ Contact enrichment failures
- ✅ Partial contact enrichment failures
- ✅ Basic mailing address fallback

**Coverage Gaps Identified:**
- Business/corporation owner handling
- Retry logic with business_name parameter
- Comprehensive mailing address scenarios
- Address validation edge cases
- Email/phone filtering edge cases
- Name processing variations
- State/province handling variations
- Multiple property responses

## Test Strategy

### Test Data Organization
All test data should be organized as module attributes following the existing pattern:
```elixir
@property_search_response_[scenario_name] [...]
@contact_id_response_[scenario_name] %{...}
```

### Test Structure
Each test should follow this pattern:
1. **Setup** - Mock Endato API responses
2. **Execute** - Call `EndatoProvider.run_skiptrace(service_request)`
3. **Assert** - Verify results match expected behavior
4. **Document** - Clear test description explaining the scenario

### Error Handling Philosophy
All error scenarios should result in `{:ok, %Result{contacts: [], raw_result: body}}` rather than raising exceptions, maintaining the graceful degradation principle.

## Detailed Test Specifications

### Phase 1: Business Owner and Corporation Handling

#### P1.1 - Pure Business Owners
**Scenario:** Property owned entirely by corporations/businesses
**Mock Data:**
- `currentOwners` with `isCorporationOrBusiness: true`
- Include `companyName` in name object
- No `tahoeId` (businesses typically don't have person IDs)
- Include mailing address in `currentOwnerMetaData`

**Expected Results:**
- Business contacts included in results
- Names use `companyName` when available
- `is_corporation_or_business: true`
- Mailing address populated from metadata
- No email/phone enrichment attempted

#### P1.2 - Mixed Individual and Business Owners
**Scenario:** Property with both individual and business current owners
**Mock Data:**
- Mix of `isCorporationOrBusiness: true/false` owners
- Individuals with `tahoeId`, businesses without
- Contact enrichment success for individuals only

**Expected Results:**
- Both individual and business contacts returned
- Individuals have enriched data when available
- Businesses have mailing addresses only
- Proper `is_corporation_or_business` flags

#### P1.3 - Business with Individual Name Structure
**Scenario:** Business entity with individual-style name fields
**Mock Data:**
- `isCorporationOrBusiness: true`
- Name has `firstName/lastName` but also `companyName`
- Test precedence of `companyName` over individual names

**Expected Results:**
- Contact name uses `companyName` when available
- Falls back to constructed name from individual fields if `companyName` missing

### Phase 2: Retry Logic and Business Name Parameter

#### P2.1 - Retry with Business Name Parameter
**Scenario:** Initial search with business_name returns empty, retry without it
**Implementation:**
- Mock first call with `business_name` parameter to return empty `propertyV2Records`
- Mock second call without `business_name` to return valid data
- Verify retry_count increments correctly

**Expected Results:**
- First search fails, triggers retry
- Second search succeeds with results
- Final results contain expected contacts

#### P2.2 - Retry Limit Exceeded
**Scenario:** Multiple retries still return empty results
**Implementation:**
- Mock all calls to return empty `propertyV2Records`
- Verify retry stops after retry_count > 1

**Expected Results:**
- Function returns empty results after retry limit
- No infinite retry loops

#### P2.3 - No Retry Without Business Name
**Scenario:** Initial search without business_name returns empty
**Implementation:**
- Mock search params without `business_name` to return empty
- Verify no retry is attempted

**Expected Results:**
- Single API call made
- Empty results returned immediately

### Phase 3: Comprehensive Mailing Address Scenarios

#### P3.1 - Missing currentOwnerMetaData
**Scenario:** Property response has no `currentOwnerMetaData` section
**Mock Data:**
- Valid `currentOwners` with `tahoeId: nil`
- No `currentOwnerMetaData` key in summary

**Expected Results:**
- Contacts created with names only
- All addresses are `nil`

#### P3.2 - Empty Mailing Addresses Array
**Scenario:** `currentOwnerMetaData.mailingAddresses` is empty array
**Mock Data:**
- `currentOwnerMetaData: %{"mailingAddresses" => []}`

**Expected Results:**
- Contacts have `nil` addresses
- No errors during processing

#### P3.3 - Invalid Mailing Address Data
**Scenario:** Mailing address missing required fields
**Test Cases:**
- Missing `addressLine1`
- Missing `city`
- Missing `state`
- Missing `zipCode`
- Empty string values

**Expected Results:**
- Invalid addresses result in `nil` address field
- Processing continues normally

#### P3.4 - Multiple Mailing Addresses
**Scenario:** Multiple addresses in `mailingAddresses` array
**Mock Data:**
- Array with 3+ mailing addresses
- First address valid, others may be invalid

**Expected Results:**
- Only first address is used
- Other addresses ignored

### Phase 4: Contact Enrichment Edge Cases

#### P4.1 - tahoeId with Non-200 Contact Response
**Scenario:** Owner has `tahoeId` but Contact ID API returns error status
**Mock Data:**
- Owner with valid `tahoeId`
- Contact ID API returns 404, 500, etc.

**Expected Results:**
- Contact included with basic info only
- Mailing address used as fallback
- No enriched email/phone/age data

#### P4.2 - tahoeId with Malformed Contact Data
**Scenario:** Contact ID API returns 200 but with invalid person structure
**Test Cases:**
- Missing `person` key in response
- `person` is not an object
- `person` has unexpected structure

**Expected Results:**
- Contact uses basic owner info
- Graceful handling of malformed data
- Mailing address fallback

#### P4.3 - Partial Contact Data
**Scenario:** Contact enrichment returns incomplete data
**Test Cases:**
- Missing `emails` array
- Missing `phones` array
- Missing `addresses` array
- Missing `name` object

**Expected Results:**
- Missing data results in empty arrays/nil values
- Available data still used
- No errors thrown

### Phase 5: Address Validation and Fallback Chain

#### P5.1 - Invalid Enrichment Address
**Scenario:** Contact enrichment address fails validation
**Mock Data:**
- Contact response with incomplete address data
- Valid mailing address available

**Expected Results:**
- Enrichment address rejected
- Mailing address used as fallback
- Address validation logic applied consistently

#### P5.2 - Address Precedence Testing
**Scenario:** Multiple address sources available
**Test Matrix:**
- Valid enrichment + valid mailing → use enrichment
- Invalid enrichment + valid mailing → use mailing
- Valid enrichment + invalid mailing → use enrichment
- Invalid enrichment + invalid mailing → nil address

**Expected Results:**
- Correct precedence order maintained
- Proper fallback logic

#### P5.3 - Address Field Variations
**Scenario:** Different address field formats
**Test Cases:**
- ZIP vs zipCode variations
- State abbreviations vs full names
- Unit/apartment number handling
- International address formats

**Expected Results:**
- Consistent address structure output
- Proper field mapping

### Phase 6: Name Processing Edge Cases

#### P6.1 - Missing isCorporationOrBusiness Field
**Scenario:** Owner object lacks `isCorporationOrBusiness` field entirely
**Mock Data:**
- Owner with only `name` object
- Various `tahoeId` scenarios (present/nil)

**Expected Results:**
- Fallback logic uses `tahoeId` presence to determine business status
- `nil` tahoeId → treated as business
- Present tahoeId → treated as individual

#### P6.2 - Empty Name Fields
**Scenario:** Name object has empty/null required fields
**Test Cases:**
- `fullName` is null/empty
- All name fields are null/empty
- Only some fields present

**Expected Results:**
- Graceful handling of missing names
- Fallback to available name components
- No contacts with completely empty names

#### P6.3 - Company Name Variations
**Scenario:** Different company name field combinations
**Test Cases:**
- Only `companyName` present
- Both `companyName` and individual fields
- `companyName` empty but individual fields present

**Expected Results:**
- Proper name precedence for businesses
- Fallback to individual name construction

### Phase 7: Email and Phone Filtering

#### P7.1 - Email Validation Edge Cases
**Scenario:** Various email validation states
**Test Cases:**
- All emails `isValidated: false`
- Mix of validated/unvalidated emails
- `emails` array is empty
- `emails` field missing entirely

**Expected Results:**
- Only validated emails included
- Empty array when no validated emails
- Proper filtering logic

#### P7.2 - Phone Connection Edge Cases
**Scenario:** Various phone connection states
**Test Cases:**
- All phones `isConnected: false`
- Mix of connected/disconnected phones
- `phones` array is empty
- `phones` field missing entirely

**Expected Results:**
- Only connected phones included
- Empty array when no connected phones
- Proper filtering logic

#### P7.3 - Email and Phone Priority
**Scenario:** Multiple valid emails/phones
**Mock Data:**
- Multiple validated emails
- Multiple connected phones
- Different priorities/types

**Expected Results:**
- All valid items included in arrays
- Proper ordering if API provides priority

### Phase 8: Property Structure Variations

#### P8.1 - Missing Property Sections
**Scenario:** Property response missing expected sections
**Test Cases:**
- No `currentOwners` array
- No `summary` section
- No `property` section

**Expected Results:**
- Empty results for missing sections
- No errors thrown
- Graceful degradation

#### P8.2 - Multiple Properties
**Scenario:** Response contains multiple property records
**Mock Data:**
- Array with 2+ property records
- Different owners in each property
- Mix of valid/invalid property structures

**Expected Results:**
- All valid owners from all properties included
- Invalid properties skipped
- Flat list of all contacts

#### P8.3 - Property Array Edge Cases
**Scenario:** Various property array structures
**Test Cases:**
- `propertyV2Records` is not an array
- `propertyV2Records` contains non-object items
- Mixed valid/invalid property objects

**Expected Results:**
- Invalid items skipped
- Processing continues for valid items
- No exceptions thrown

### Phase 9: Integration and Performance Tests

#### P9.1 - Large Response Handling
**Scenario:** Response with many owners and properties
**Mock Data:**
- 10+ properties with 5+ owners each
- Mix of individuals and businesses
- Various enrichment scenarios

**Expected Results:**
- All contacts processed correctly
- Performance remains acceptable
- Memory usage reasonable

#### P9.2 - State Code Variations
**Scenario:** Different state/province formats
**Test Cases:**
- Two-letter state codes (IL, CA, TX)
- Full state names (Illinois, California, Texas)
- Invalid state codes
- International province codes

**Expected Results:**
- Consistent state field in output
- Proper handling of variations
- Invalid states handled gracefully

#### P9.3 - Character Encoding and Special Characters
**Scenario:** Names and addresses with special characters
**Test Cases:**
- Unicode characters in names
- Accented characters
- Special punctuation
- Very long names/addresses

**Expected Results:**
- Proper character encoding preservation
- No truncation of valid data
- Graceful handling of edge cases

## Implementation Guidelines for Claude Agents

### Test File Organization
Each phase should be implemented in separate test files or clearly separated describe blocks:
```elixir
describe "Phase 1: Business Owner Handling" do
  # P1.1, P1.2, P1.3 tests
end

describe "Phase 2: Retry Logic" do
  # P2.1, P2.2, P2.3 tests
end
```

### Mock Data Patterns
Follow consistent naming for mock data:
```elixir
@property_search_response_business_owners [...]
@property_search_response_retry_scenario [...]
@contact_id_response_partial_data %{...}
```

### Assertion Patterns
Use comprehensive assertions that verify:
1. Result structure (`{:ok, %Result{}}`)
2. Contact count
3. Individual contact properties
4. Specific field values
5. Nil/empty handling

### Error Scenario Testing
For each error scenario, verify:
1. No exceptions raised
2. Empty contacts list returned
3. Appropriate raw_result preservation
4. Proper logging (where applicable)

## Success Criteria

### Coverage Metrics
- [ ] All code paths in `run_skiptrace/1` tested
- [ ] All branches in `extract_owner_name_parts/2` tested
- [ ] All address fallback scenarios covered
- [ ] All email/phone filtering logic tested

### Quality Metrics
- [ ] Each test has clear documentation
- [ ] Mock data represents realistic API responses
- [ ] Assertions are comprehensive and specific
- [ ] Error cases are thoroughly covered

### Maintainability
- [ ] Test data is reusable across similar scenarios
- [ ] Tests are independent and can run in any order
- [ ] Clear separation between setup, execution, and assertion
- [ ] Consistent naming and organization

## Execution Priority
1. **Phase 1** - Business owner handling (highest impact)
2. **Phase 2** - Retry logic (core functionality)
3. **Phase 3** - Mailing address scenarios (new feature)
4. **Phase 4** - Contact enrichment edge cases (reliability)
5. **Phase 5** - Address validation (data quality)
6. **Phase 6** - Name processing (edge cases)
7. **Phase 7** - Email/phone filtering (data quality)
8. **Phase 8** - Property variations (robustness)
9. **Phase 9** - Integration tests (comprehensive validation)

This test plan ensures comprehensive coverage of the Endato Skiptrace implementation while providing clear, actionable specifications for Claude agents to implement.