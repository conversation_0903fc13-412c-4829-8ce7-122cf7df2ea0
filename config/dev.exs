import Config

# Used in Util.email_valid?
# In dev mode we don't bother with MX record check - just the string format.
# In prod.ex MX checking is enabled
secret_key_base = "47TFd8fpLTZROcN4Lxz/OQ5fz4hVFMNCsSxHKwSrRGZGxDcWKyGH+1uxAtGYn1/Q"

config :ash,
  policies: [show_policy_breakdowns?: true],
  pub_sub: [debug?: true]

config :boring, Boring.Repo,
  username: "postgres",
  password: "postgres",
  database: "boring_dev",
  # For development, we disable any cache and enable
  #   access_key: System.get_env("AWS_ACCESS_KEY"),

  #
  #   secret: System.get_env("AWS_SECRET")

  #     mix phx.gen.cert
  #
  # Note that this task requires Erlang/OTP 20 or later.
  # Run `mix help phx.gen.cert` for more information.
  hostname: "localhost",
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10

config :boring, PetalProWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: String.to_integer(System.get_env("PORT") || "4000")],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: secret_key_base,
  watchers: [
    node: ["esbuild.js", "--watch", cd: Path.expand("../assets", __DIR__)],
    tailwind: {Tailwind, :install_and_run, [:default, ~w(--watch)]}
  ]

# Watch static and templates for browser reloading.
# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Note that this task requires Erlang/OTP 20 or later.
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: String.to_integer(System.get_env("HTTPS_PORT") || "4001"),
config :boring, PetalProWeb.Endpoint,
  live_reload: [
    patterns: [
      ~r"priv/static/.*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/petal_pro_web/.*(ex|heex)$",
      ~r"lib/boring_web/.*(ex|heex)$",
      ~r"lib/petal_pro_web/(controllers|live|views|components|templates)/.*(ex|heex)$",
      ~r"lib/boring_web/(controllers|live|views|components|templates)/.*(ex|heex)$"
    ]
  ]

config :boring, :env, :dev

config :email_checker,
  default_dns: :system,
  also_dns: [],
  validations: [EmailChecker.Check.Format],
  smtp_retries: 2,
  # Add a port on which you want LiveDebugger to run
  timeout_milliseconds: :infinity

# Git Ops configuration (only available in dev environment)
config :git_ops,
  mix_project: Mix.Project.get!(),
  changelog_file: "CHANGELOG.md",
  repository_url: "https://github.com/boring-business-leads/boring",
  types: [types: [tidbit: [hidden?: true], important: [header: "Important Changes"]]],
  version_tag_prefix: "v",
  manage_mix_version?: true,
  manage_readme_version: true

config :live_debugger,
  browser_features?: true,
  # Uncomment when you want to send test emails:
  # Also ensure in config.exs that mailer_default_from_email is set to an email that is whitelisted on Amazon SES
  # config :boring, PetalPro.Mailer,
  #   adapter: Swoosh.Adapters.AmazonSES,
  #   region: System.get_env("AWS_SES_REGION"),
  debug_button?: true

config :logger, :console, format: "[$level] $message\n"

# Initialize plugs at runtime for faster development compilation
#       cipher_suite: :strong,
# in production as building large stacktraces may be expensive.
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ],
config :phoenix, :plug_init_mode, :runtime
config :phoenix, :stacktrace_depth, 20

config :phoenix_live_view, :debug_heex_annotations, true

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false

# Set a higher stacktrace during development. Avoid configuring such
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# config :swoosh, api_client: Swoosh.ApiClient.Finch, finch_name: PetalPro.Finch
