import Config

alias Ecto.Adapters.SQL.Sandbox

config :ash, :policies, show_policy_breakdowns?: true
config :ash, disable_async?: true

# Only in tests, remove the complexity from the password hashing algorithm
config :bcrypt_elixir, :log_rounds, 1

config :boring, Boring.Repo,
  username: "postgres",
  password: "postgres",
  database: "boring_test#{System.get_env("MIX_TEST_PARTITION")}",
  hostname: "localhost",
  pool: Sandbox,
  # In test we don't send emails.
  # to provide built-in test partitioning in CI environment.
  # Run `mix help test` for more information.
  pool_size: 50

config :boring, Oban, testing: :inline
config :boring, PetalPro.Mailer, adapter: Swoosh.Adapters.Test

config :boring, PetalProWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: String.to_integer(System.get_env("PORT", "4000")) + 2],
  secret_key_base: "cPNzM6yNbuYM9FcYYtqL/PPFpiGQD5Tdxe4pRe8KYGFJ8gwI3Zgl6VL80H6pFeOp",

  # The MIX_TEST_PARTITION environment variable can be used

  # this is a "real" product in Petal Pro's Stripe test account,
  # used for testing against the Stripe API, in conjunction with ExVCR
  server: true

config :boring, :billing_entity, :org

config :boring, :billing_products, [
  %{
    id: "prod1",
    name: "Prod 1",
    description: "Prod 1 description",
    feature_blurb: "Includes:",
    entitlements: %{
      opportunities: 10
    },
    plans: [
      %{
        id: "plan1-1",
        name: "Plan 1",
        amount: 100,
        interval: :month,
        allow_promotion_codes: true,
        items: [
          %{price: "item1-1-1", quantity: 1}
        ]
      }
    ]
  },
  %{
    id: "prod2",
    name: "Prod 2",
    description: "Prod 2 description",
    feature_blurb: "Everything in Prod 1, plus:",
    entitlements: %{
      opportunities: 20
    },
    plans: [
      %{
        id: "plan2-1",
        name: "Plan 2-1",
        amount: 200,
        interval: :month,
        allow_promotion_codes: true,
        items: [
          %{price: "item2-1-1", quantity: 1},
          %{price: "item2-1-2", quantity: 1}
        ]
      },
      %{
        id: "plan2-2",
        name: "Plan 2-2",
        amount: 2_000,
        interval: :year,
        allow_promotion_codes: true,
        items: [
          %{price: "item2-1-1", quantity: 1},
          %{price: "item2-2-1", quantity: 1}
        ]
      }
    ]
  },
  %{
    id: "stripe-test-plan-a",
    name: "Petal Pro Test Plan A",
    description: "Petal Pro Test Plan A",
    feature_blurb: "Includes:",
    entitlements: %{
      opportunities: 10
    },
    plans: [
      %{
        id: "stripe-test-plan-a-monthly",
        name: "Monthly",
        amount: 199,
        interval: :month,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1OQj8TIWVkWpNCp7ZlUSOaI9", quantity: 1}
        ]
      },
      %{
        id: "stripe-test-plan-a-yearly",
        name: "Yearly",
        amount: 1900,
        interval: :month,
        allow_promotion_codes: true,
        items: [
          %{price: "price_1OQj8pIWVkWpNCp74VstFtnd", quantity: 1}
        ]
      }
    ]
  }
]

config :boring, :billing_provider, PetalPro.Billing.Providers.Stripe
config :boring, :env, :test
config :boring, :http_client, FakeReq

config :boring, :mapbox,
  access_token: System.get_env("MAPBOX_ACCESS_TOKEN"),
  username: System.get_env("MAPBOX_USERNAME"),
  image_download_path: System.get_env("MAPBOX_IMAGE_PATH") || "priv/static/aerial_images",
  image_static_path: System.get_env("MAPBOX_IMAGE_STATIC_PATH") || "/aerial_images",
  assets_bucket: System.get_env("MAPBOX_ASSETS_BUCKET") || "boring-business-staging-mapbox-assets",
  io_backend: Boring.Mocks.FileOpsMock

config :boring, :passwordless_enabled, true
config :boring, :sandbox, Sandbox

config :boring,
  impersonation_enabled?: true,
  gdpr_mode: true

config :email_checker, validations: [EmailChecker.Check.Format]

config :exvcr,
  global_mock: true,
  vcr_cassette_library_dir: "test/support/fixtures/vcr_cassettes",
  # Oban - Disable plugins, enqueueing scheduled jobs and job dispatching altogether when testing
  # Print only warnings and errors during test
  # Configure your database
  filter_request_headers: ["Authorization"]

config :logger, level: :warning

# Initialize plugs at runtime for faster test compilation
config :phoenix, :plug_init_mode, :runtime

config :phoenix_test, :endpoint, PetalProWeb.Endpoint

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false

# Disable automatic timezone data updates during testing
config :tzdata, :autoupdate, :disabled
