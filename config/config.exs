# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

alias <PERSON><PERSON>oney.Types.Money

config :ash,
  allow_forbidden_field_for_relationships_by_default?: true,
  read_action_after_action_hooks_in_order?: true,
  include_embedded_source_by_default?: false,
  show_keysets_for_all_actions?: false,
  default_page_type: :keyset,
  policies: [no_filter_static_forbidden_reads?: false],
  keep_read_action_loads_when_loading?: false,
  default_actions_require_atomic?: true,
  known_types: [Money],
  custom_types: [
    user_role: Boring.Accounts.Types.UserRole,
    log_action: Boring.Logs.Types.LogAction,
    log_user_type: Boring.Logs.Types.LogUserType,
    org_role: Boring.Orgs.Types.OrgRole,
    billing_subscription_status: Boring.Billing.Types.SubscriptionStatus,
    user_notification_type: Boring.Notifications.Types.UserNotificationType,
    extract_interval_type: Boring.Google.Types.ExtractIntervalType,
    extract_status: Boring.Google.Types.ExtractStatus,
    opportunity_status: Boring.Acquisitions.Types.OpportunityStatus,
    country: Boring.Locations.Country,
    contact_role: Boring.Acquisitions.Types.ContactRole,
    money: Money,
    us_census_state_id: Boring.Api.USCensusBureau.Types.StateId,
    service_request_status: Boring.Services.Types.ServiceRequestStatus,
    service_request_type: Boring.Services.Types.ServiceRequestType,
    billing_meter_event: Boring.Billing.Meters.Types.Event
  ]

config :ash_oban,
  actor_persister: Boring.AshObanActorPersister

config :boring, Boring.Repo,
  migration_primary_key: [name: :id, type: :binary_id],
  migration_foreign_key: [column: :id, type: :binary_id]

# Oban:
# Queues are specified as a keyword list where the key is the name of the queue and the value is the maximum number of concurrent jobs.
# The following configuration would start four queues with concurrency ranging from 5 to 50: [default: 10, mailers: 20, events: 50, media: 5]
# For now we just have one default queue with up to 5 concurrent jobs (as our database only accepts up to 10 connections so we don't want to overload it)
# Oban provides active pruning of completed, cancelled and discarded jobs - we retain jobs for 24 hours
config :boring, Oban,
  engine: Oban.Engines.Basic,
  notifier: Oban.Notifiers.PG,
  repo: Boring.Repo,
  queues: [
    default: 5,
    aerial_images: 10,
    data_extraction: 3,
    data_ingestion: 5,
    skiptrace: 7
  ],
  plugins: [{Oban.Plugins.Pruner, max_age: 3600 * 24}, {Oban.Plugins.Cron, []}]

# Configures the mailer
#
# By default it uses the "Local" adapter which stores the emails
# locally. You can see the emails in your browser, at "/dev/mailbox".
#
# For production it's recommended to configure a different adapter
# at the `config/runtime.exs`.
# All IDs are generated using UUIDs.
config :boring, PetalPro.Mailer, adapter: Swoosh.Adapters.Local

# Configures the endpoint
config :boring, PetalProWeb.Endpoint,
  adapter: Bandit.PhoenixAdapter,
  url: [host: "localhost"],
  render_errors: [
    formats: [html: PetalProWeb.ErrorHTML, json: PetalProWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: PetalPro.PubSub,
  live_view: [signing_salt: "Fd8SWPu3"]

# Specify which languages you support
# To create .po files for a language run `mix gettext.merge priv/gettext --locale fr`
# (fr is France, change to whatever language you want - make sure it's included in the locales config below)
config :boring, PetalProWeb.Gettext, allowed_locales: ~w(en)

# :user or :org
config :boring, :billing_entity, :org

config :boring, :billing_features, [
  %{
    id: "opportunity_search",
    name: "Opportunity Search",
    tiers: ["starter", "plus", "pro"],
    benefits: [
      %{
        id: "opportunities_per_month",
        text_template: "{count} new opportunities per month",
        description: "Find potential deals using our interactive map",
        tier_values: %{
          "starter" => %{count: 80},
          "plus" => %{count: 160},
          "pro" => %{count: 320}
        },
        tiers: ["starter", "plus", "pro"]
      },
      %{
        id: "google_review_ratings",
        text: "Google Review Ratings",
        description: "See business reputation and review scores",
        tiers: ["starter", "plus", "pro"]
      },
      %{
        id: "unclaimed_on_google",
        text: "Unlisted on Google",
        description: "Find businesses not listed on Google My Business",
        tiers: ["plus", "pro"]
      }
    ]
  },
  %{
    id: "deal_flow_funnel",
    name: "Deal Flow Funnel",
    tiers: ["starter", "plus", "pro"],
    benefits: [
      %{
        id: "deal_flow_funnel",
        text: "Deal Flow Funnel",
        description: "Track deals from outreach to acquisition with status tracking, contact management, and follow-ups",
        tiers: ["starter", "plus", "pro"]
      }
    ]
  },
  %{
    id: "teams",
    name: "Teams",
    tiers: ["starter", "plus", "pro"],
    benefits: [
      %{
        id: "team_collaboration",
        text: "Team Collaboration",
        description: "Invite unlimited members to work the deal funnel together",
        tiers: ["starter", "plus", "pro"]
      }
    ]
  },
  %{
    id: "skip_tracing",
    name: "Automated Owner Skip-tracing",
    tiers: ["plus", "pro"],
    benefits: [
      %{
        id: "automated_skip_tracing",
        text: "Automated Skip-tracing",
        description: "Get contact info for individual owners",
        tiers: ["plus", "pro"]
      }
    ]
  }
]

config :boring, :billing_products, [
  %{
    id: "starter",
    name: "Starter",
    description: "Essential tools for lead generation and deal tracking.",
    feature_blurb: "Includes:",
    entitlements: %{
      opportunities: 20
    },
    plans: [
      %{
        id: "starter-monthly",
        name: "Monthly",
        amount: 79_00,
        interval: :month,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1RikxrRtMKGzuRugmBvSTv42", quantity: 1}
        ]
      },
      %{
        id: "starter-yearly",
        name: "Yearly",
        amount: 80_400,
        interval: :year,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1RikyeRtMKGzuRugnv17McbA", quantity: 1}
        ]
      }
    ]
  },
  %{
    id: "plus",
    name: "Plus",
    description: "Advanced search features with skip-tracing capabilities.",
    feature_blurb: "Everything in Starter, plus:",
    most_popular: true,
    entitlements: %{
      opportunities: 40
    },
    plans: [
      %{
        id: "plus-monthly",
        name: "Monthly",
        amount: 19_900,
        interval: :month,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1RikzgRtMKGzuRugxefZzynV", quantity: 1}
        ]
      },
      %{
        id: "plus-yearly",
        name: "Yearly",
        amount: 202_800,
        interval: :year,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1Ril03RtMKGzuRughHiMDF5N", quantity: 1}
        ]
      }
    ]
  },
  %{
    id: "pro",
    name: "Pro",
    description: "Maximum opportunity volume for serious investors.",
    feature_blurb: "Everything in previous tiers, plus:",
    entitlements: %{
      opportunities: 80
    },
    plans: [
      %{
        id: "pro-monthly",
        name: "Monthly",
        amount: 29_900,
        interval: :month,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1Ril0uRtMKGzuRugcW8rP2Zw", quantity: 1}
        ]
      },
      %{
        id: "pro-yearly",
        name: "Yearly",
        amount: 304_800,
        interval: :year,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1Ril1IRtMKGzuRugqYD4AtTG", quantity: 1}
        ]
      }
    ]
  }
]

config :boring, :billing_provider, PetalPro.Billing.Providers.Stripe

config :boring,
       :billing_provider_subscription_link,
       "https://dashboard.stripe.com/test/subscriptions/"

# Reduce XSS risks by declaring which dynamic resources are allowed to load
# If you use any CDNs, whitelist them here.
# Policy struct: https://github.com/mbramson/content_security_policy/blob/master/lib/content_security_policy/policy.ex
# Read more about the options: https://content-security-policy.com
# Note that we use unsafe-eval because Alpine JS requires it :( (see https://alpinejs.dev/advanced/csp)
config :boring, :content_security_policy, %{
  default_src: [
    "'unsafe-inline'",
    "'unsafe-eval'",
    "https:",
    "'self'",
    "data:",
    "blob:",
    "https://cdnjs.cloudflare.com",
    "https://cdn.skypack.dev",
    "https://cdn.jsdelivr.net",
    "https://rsms.me",
    "https://res.cloudinary.com",
    "https://api.cloudinary.com",
    "https://lh3.googleusercontent.com",
    "https://validator.swagger.io",
    "*.amazonaws.com",
    "ws://localhost:#{String.to_integer(System.get_env("PORT") || "4000")}",
    "#{if Mix.env() == :dev, do: "http://localhost:4007"}",

    # Editor.js
    "*.youtube.com",
    "play.google.com",
    "*.twitter.com",

    # Google Maps
    "https://www.google.com",
    "https://maps.googleapis.com",
    "https://maps.gstatic.com",
    "https://fonts.googleapis.com",
    "https://fonts.gstatic.com",

    # Plausible Analytics
    "https://plausible.io",

    # Termly
    "https://app.termly.io",

    # fly.io tigris asset hosting
    "https://boring-business-staging-mapbox-assets.fly.storage.tigris.dev/",
    "https://boring-business-production-mapbox-assets.fly.storage.tigris.dev/",

    # fly.io tigris file uploads
    "https://boring-business-dev-file-uploads.fly.storage.tigris.dev/",
    "https://boring-business-staging-file-uploads.fly.storage.tigris.dev/",
    "https://boring-business-production-file-uploads.fly.storage.tigris.dev/",

    # Our domain
    "https://boring-production.fly.dev",
    "wss://boring-production.fly.dev",
    "wss://boring-production.fly.dev/live/websocket",
    "https://*.boring-production.fly.dev",
    "wss://*.boring-production.fly.dev",
    "wss://*.boring-production.fly.dev/live/websocket",
    "https://boring-staging.fly.dev",
    "wss://boring-staging.fly.dev",
    "wss://boring-staging.fly.dev/live/websocket",
    "https://*.boring-staging.fly.dev",
    "wss://*.boring-staging.fly.dev",
    "wss://*.boring-staging.fly.dev/live/websocket",
    "https://boringbusinessleads.com",
    "wss://boringbusinessleads.com",
    "wss://boringbusinessleads.com/live/websocket",
    "https://*.boringbusinessleads.com",
    "wss://*.boringbusinessleads.com",
    "wss://*.boringbusinessleads.com/live/websocket",
    "https://gofishleads.com",
    "wss://gofishleads.com",
    "wss://gofishleads.com/live/websocket",
    "https://*.gofishleads.com",
    "wss://*.gofishleads.com",
    "wss://*.gofishleads.com/live/websocket",

    # Zendesk
    "https://static.zdassets.com",
    "https://*.zendesk.com",

    # Featurebase
    "https://*.featurebase.app",

    # Sentry
    "https://*.sentry.io"
  ]
}

config :boring, :faqs, [
  %{
    heading: "What does your service provide?",
    content:
      "Our service helps you find relevant leads in your target areas and offers tools to track their progress through your acquisition pipeline."
  },
  %{
    heading: "How does your service work?",
    content:
      "Sign up, select opportunities in your chosen areas, and use our tools to track their progress through your acquisition journey."
  },
  %{
    heading: "Who can benefit from your service?",
    content:
      "Our service is ideal for real estate investors looking to streamline their lead generation and tracking process."
  },
  %{
    heading: "How do you determine what is considered a Real Estate Investment Trust (REIT)?",
    content:
      "We maintain a list of publicly traded REITs and their subsidiaries internally. These are then filtered out of the leads that are populated in your feed."
  },
  %{
    heading: "What kind of support do you offer to subscribers?",
    content:
      "We offer support via email. Contact <NAME_EMAIL>, and we will get back to you as soon as possible."
  },
  %{
    heading: "How do I cancel my subscription?",
    content:
      "You can cancel your subscription at any time through your account settings or by contacting our customer service team."
  },
  %{
    heading: "Is there a refund policy?",
    content: "We do not offer any refunds. All sales are final."
  },
  %{
    heading: "Do you offer services for international leads?",
    content: "Currently we are only populating leads in the United States."
  },
  %{
    heading: "How can I provide feedback or suggestions?",
    content:
      "We welcome feedback and suggestions from our subscribers. You can provide feedback by contacting our customer service <NAME_EMAIL>."
  },
  %{
    heading: "How can I get in touch with customer service?",
    content: "You can contact our customer service team via <NAME_EMAIL>."
  }
]

config :boring, :language_options, [
  %{locale: "en", flag: "🇺🇸", label: "English"}
]

config :boring, :passwordless_enabled, false

config :boring,
  apify_task_id: System.get_env("APIFY_TASK_ID")

# SETUP_TODO - ensure these details are correct
# Option descriptions:
#   - app_name: This appears in your email layout and also your meta title tag
#   - business_name: This appears in your landing page footer next to the copyright symbol
#   - support_email: In your transactional emails there is a "Contact us" email - this is what will appear there
#   - mailer_default_from_name: The "from" name for your transactional emails
#   - mailer_default_from_email: The "from" email for your transactional emails
#   - logo_url_for_emails: The URL to your logo for your transactional emails (needs to be a full URL, not a path)
#   - seo_description: Will go in your meta description tag
#   - x_url: (deletable) The URL to your Twitter account (used in the landing page footer)
#   - github_url: (deletable) The URL to your Github account (used in the landing page footer)
#   - discord_url: (deletable) The URL to your Discord invititation (used in the landing page footer)
config :boring,
  app_name: "GoFish",
  business_name: "Boring Business Leads LLC",
  support_email: "<EMAIL>",
  mailer_default_from_name: "Support",
  mailer_default_from_email: "<EMAIL>",
  logo_url_for_emails: "https://res.cloudinary.com/boring-business-leads/image/upload/logo_for_emails_iqwi87.png",
  seo_description:
    "Find off-market self-storage opportunities, get owner contact info, track deals through your pipeline, and collaborate with your team—all in one platform.",
  x_url: "https://x.com/gofishleads",
  ecto_repos: [Boring.Repo],
  ash_domains: [
    Boring.Accounts,
    Boring.Logs,
    Boring.Orgs,
    Boring.Billing.Customers,
    Boring.Billing.Subscriptions,
    Boring.Notifications,
    Boring.Posts,
    Boring.Files,
    Boring.Acquisitions,
    Boring.Google,
    Boring.Locations,
    Boring.Api.Mapbox,
    Boring.Api.USCensusBureau,
    Boring.Accounts,
    Boring.Demographics,
    Boring.Services,
    Boring.Features,
    Boring.Billing.Meters
  ]

config :boring,
  ecto_repos: [Boring.Repo],
  generators: [binary_id: true]

# Petal Pro features:
#   - impersonation_enabled?: Allows admins to impersonate users
#   - gdpr_mode: Enables GDPR mode which will not send personal data to OpenAI
config :boring,
  impersonation_enabled?: true,
  gdpr_mode: true

config :boring,
  policies: [
    %{
      id: "58bd0df5-1051-4a53-b519-c67b1dd6ac6c",
      title: "Terms of Service",
      slug: "terms"
    },
    %{
      id: "84f2a4fe-4766-430d-96e6-7a6573af7316",
      title: "Privacy Policy",
      slug: "privacy"
    },
    %{
      id: "************************************",
      title: "Acceptable Use Policy",
      slug: "acceptable-use"
    },
    %{
      id: "418571b1-96b7-4876-ad24-1e8c7dc5569d",
      title: "Refund Policy",
      slug: "refund"
    },
    %{
      id: "e70d7363-fb3a-4791-bea1-ee0697598fa3",
      title: "Disclaimer",
      slug: "disclaimer"
    }
  ]

config :boring,
  skiptrace: [
    providers: [
      Boring.Api.SkiptraceService.Providers.Endato
    ]
  ]

config :ex_aws, :s3,
  scheme: "https://",
  host: "fly.storage.tigris.dev",
  region: "auto"

# We have to set access_key_id and secret_access_key at compile time
# However we don't have values for these at compile time
# Instead we set them at runtime through the :boring, :tigris runtime config
# We take the :tigris config and override the access_key_id and secret_access_key below
config :ex_aws,
  debug_requests: true,
  access_key_id: {:system, "TIGRIS_ACCESS_KEY_ID"},
  secret_access_key: {:system, "TIGRIS_SECRET_ACCESS_KEY"},
  json_codec: Jason,
  region: "auto"

config :ex_cldr,
  default_locale: "en",
  default_backend: Boring.Cldr

config :ex_money,
  default_cldr_backend: Boring.Cldr

config :flop, repo: Boring.Repo, default_limit: 20

config :langchain,
  openai_key: "",
  openai_org_id: "",
  # optional
  openai_proj_id: ""

# Configures Elixir's Logger
config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]

config :petal_components,
       :error_translator_function,
       {PetalProWeb.CoreComponents, :translate_error}

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

config :spark,
  formatter: [
    remove_parens?: true,
    "Ash.Resource": [
      section_order: [
        :admin,
        :postgres,
        :resource,
        :code_interface,
        :actions,
        :policies,
        :pub_sub,
        :preparations,
        :changes,
        :validations,
        :multitenancy,
        :attributes,
        :relationships,
        :calculations,
        :aggregates,
        :identities
      ]
    ],
    "Ash.Domain": [
      section_order: [:admin, :resources, :policies, :authorization, :domain, :execution]
    ]
  ]

config :tailwind,
  version: "4.1.11",
  default: [
    args: ~w(
    --input=assets/css/app.css
    --output=priv/static/assets/app.css
  ),
    cd: Path.expand("..", __DIR__)
  ]

# Social login providers
# Full list of strategies: https://github.com/ueberauth/ueberauth/wiki/List-of-Strategies
config :ueberauth, Ueberauth,
  providers: [
    # google: {Ueberauth.Strategy.Google, [default_scope: "email profile"]}
  ]

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"
