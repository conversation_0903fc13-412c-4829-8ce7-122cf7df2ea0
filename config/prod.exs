import Config

config :boring, PetalProWeb.Endpoint,
  check_origin: [
    "https://gofishleads.com",
    "https://boring-production.fly.dev",
    "https://boring-staging.fly.dev"
  ],
  cache_static_manifest: "priv/static/cache_manifest.json"

config :boring, :billing_features, [
  %{
    id: "opportunity_search",
    name: "Opportunity Search",
    tiers: ["starter", "plus", "pro"],
    benefits: [
      %{
        id: "opportunities_per_month",
        text_template: "{count} new opportunities per month",
        description: "Find potential deals using our interactive map",
        tier_values: %{
          "starter" => %{count: 80},
          "plus" => %{count: 160},
          "pro" => %{count: 320}
        },
        tiers: ["starter", "plus", "pro"]
      },
      %{
        id: "google_review_ratings",
        text: "Google Review Ratings",
        description: "See business reputation and review scores",
        tiers: ["starter", "plus", "pro"]
      },
      %{
        id: "unclaimed_on_google",
        text: "Unlisted on Google",
        description: "Find businesses not listed on Google My Business",
        tiers: ["plus", "pro"]
      }
    ]
  },
  %{
    id: "deal_flow_funnel",
    name: "Deal Flow Funnel",
    tiers: ["starter", "plus", "pro"],
    benefits: [
      %{
        id: "deal_flow_funnel",
        text: "Deal Flow Funnel",
        description: "Track deals from outreach to acquisition with status tracking, contact management, and follow-ups",
        tiers: ["starter", "plus", "pro"]
      }
    ]
  },
  %{
    id: "teams",
    name: "Teams",
    tiers: ["starter", "plus", "pro"],
    benefits: [
      %{
        id: "team_collaboration",
        text: "Team Collaboration",
        description: "Invite unlimited members to work the deal funnel together",
        tiers: ["starter", "plus", "pro"]
      }
    ]
  },
  %{
    id: "skip_tracing",
    name: "Automated Owner Skip-tracing",
    tiers: ["plus", "pro"],
    benefits: [
      %{
        id: "automated_skip_tracing",
        text: "Automated Skip-tracing",
        description: "Get contact info for individual owners",
        tiers: ["plus", "pro"]
      }
    ]
  }
]

config :boring, :billing_products, [
  %{
    id: "starter",
    name: "Starter",
    description: "Essential tools for lead generation and deal tracking.",
    feature_blurb: "Includes:",
    entitlements: %{
      opportunities: 80
    },
    plans: [
      %{
        id: "starter-monthly",
        name: "Monthly",
        amount: 79_00,
        interval: :month,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1Rgsa5Rx43TPwcgU8BdkBVUp", quantity: 1}
        ]
      },
      %{
        id: "starter-yearly",
        name: "Yearly",
        amount: 80_400,
        interval: :year,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1Rgsb1Rx43TPwcgUJTBvQzuJ", quantity: 1}
        ]
      }
    ]
  },
  %{
    id: "plus",
    name: "Plus",
    description: "Advanced search features with skip-tracing capabilities.",
    feature_blurb: "Everything in Starter, plus:",
    most_popular: true,
    entitlements: %{
      opportunities: 160
    },
    plans: [
      %{
        id: "plus-monthly",
        name: "Monthly",
        amount: 19_900,
        interval: :month,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1RMT6RRx43TPwcgUW3PHN4wO", quantity: 1}
        ]
      },
      %{
        id: "plus-yearly",
        name: "Yearly",
        amount: 202_800,
        interval: :year,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1RgshhRx43TPwcgUPPyJiztB", quantity: 1}
        ]
      }
    ]
  },
  %{
    id: "pro",
    name: "Pro",
    description: "Maximum opportunity volume for serious investors.",
    feature_blurb: "Everything in previous tiers, plus:",
    entitlements: %{
      opportunities: 320
    },
    plans: [
      %{
        id: "pro-monthly",
        name: "Monthly",
        amount: 29_900,
        interval: :month,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1RgsdIRx43TPwcgUSAO5FooY", quantity: 1}
        ]
      },
      %{
        id: "pro-yearly",
        name: "Yearly",
        amount: 304_800,
        interval: :year,
        allow_promotion_codes: true,
        trial_days: 7,
        items: [
          %{price: "price_1RgsdjRx43TPwcgUj8lSHkZV", quantity: 1}
        ]
      }
    ]
  }
]

config :boring,
       :billing_provider_subscription_link,
       # Do not print debug messages in production
       # manifest is generated by the `mix phx.digest` task,
       # which you should run after static files are built and
       # before starting your production server.
       "https://dashboard.stripe.com/subscriptions/"

config :boring, :env, :prod

config :logger, level: :info

# Configures Swoosh API Client
config :swoosh, api_client: Swoosh.ApiClient.Finch, finch_name: PetalPro.Finch

# Disable Swoosh Local Memory Storage
config :swoosh, local: false

# ## SSL Support
#
# To get SSL working, you will need to add the `https` key
# to the previous section and set your `:url` port to 443:
#
#     config :boring, PetalProWeb.Endpoint,
#       ...,
#       url: [host: "example.com", port: 443],
#       https: [
#         ...,
#         port: 443,
#         cipher_suite: :strong,
#         keyfile: System.get_env("SOME_APP_SSL_KEY_PATH"),
#         certfile: System.get_env("SOME_APP_SSL_CERT_PATH")
#       ]
#
# The `cipher_suite` is set to `:strong` to support only the
# latest and more secure SSL ciphers. This means old browsers
# and clients may not be supported. You can set it to
# `:compatible` for wider support.
#
# `:keyfile` and `:certfile` expect an absolute path to the key
# and cert in disk or a relative path inside priv, for example
# "priv/ssl/server.key". For all supported SSL configuration
# options, see https://hexdocs.pm/plug/Plug.SSL.html#configure/1
#
# We also recommend setting `force_ssl` in your endpoint, ensuring
# no data is ever sent via http, always redirecting to https:
#
#     config :boring, PetalProWeb.Endpoint,
#       force_ssl: [hsts: true]
#
# Check `Plug.SSL` for all available options in `force_ssl`.
