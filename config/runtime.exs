import Config

# config/runtime.exs is executed for all environments, including
# during releases. It is executed after compilation and before the
# system starts, so it is typically used to load production configuration
# and secrets from environment variables or elsewhere. Do not define
# any compile-time configuration in here, as it won't be applied.
# The block below contains prod specific runtime configuration.

stripe_api_key = Application.get_env(:stripity_stripe, :api_key)
stripe_signing_secret = Application.get_env(:stripity_stripe, :signing_secret)
openai_key = Application.get_env(:langchain, :openai_key)
openai_org_id = Application.get_env(:langchain, :openai_org_id)
openai_proj_id = Application.get_env(:langchain, :openai_proj_id)

# If you are using Cloudinary for file uploads:
config :boring, :cloudinary,
  cloud_name: System.get_env("CLOUDINARY_CLOUD_NAME"),
  api_key: System.get_env("CLOUDINARY_API_KEY"),
  api_secret: System.get_env("CLOUDINARY_API_SECRET"),
  folder: System.get_env("CLOUDINARY_FOLDER")

config :boring, :demographics, us_census_api_key: System.get_env("US_CENSUS_API_KEY")

config :boring, :featurebase,
  enabled?: System.get_env("FEATUREBASE_ENABLED") |> String.to_existing_atom() || false,
  organization: System.get_env("FEATUREBASE_ORGANIZATION"),
  app_id: System.get_env("FEATUREBASE_APP_ID"),
  identity_verification_key: System.get_env("FEATUREBASE_IDENTITY_VERIFICATION_KEY")

config :boring, :file_upload,
  scheme: System.get_env("FILE_UPLOAD_SCHEME") || "https",
  upload_bucket: System.get_env("FILE_UPLOAD_BUCKET") || "boring-business-dev-file-uploads",
  upload_bucket_url:
    System.get_env("FILE_UPLOAD_BUCKET_URL") ||
      "https://boring-business-dev-file-uploads.fly.storage.tigris.dev/",
  max_file_size: System.get_env("MAX_FILE_SIZE") || 20_000_000,
  upload_expiry: System.get_env("UPLOAD_EXPIRY") || 3_600_000

#   bucket: System.get_env("S3_FILE_UPLOAD_BUCKET"),
#   scheme: System.get_env("S3_SCHEME") || "http"
# In staging/prod this is prefix inside the bucket so if you are placing your files at the top level of the bucket, the value is ""
# This is the base path to serve the images from.
config :boring, :mapbox,
  access_token: System.get_env("MAPBOX_ACCESS_TOKEN"),
  #   region: System.get_env("AWS_REGION"),
  # # If you are using Amazon S3 for file uploads:
  username: System.get_env("MAPBOX_USERNAME"),
  image_download_path:
    System.get_env("MAPBOX_DOWNLOAD_IMAGE_PATH") || "priv/static/aerial_images",

  # In local dev using the FileOps backend we will download files to loca disk, an appropriate value could look like this: "priv/static/aerial_images"
  #   access_key: System.get_env("AWS_ACCESS_KEY"),

  # In local dev we're serving the images using PlugStatic, which serves from priv/static by default. Therefore, the value should be "/aerial_images"
  # This is the path to download the images to.

  # In staging/prod this is the path to the base url of the public bucket address in our case "https://boring-business-staging-mapbox-assets.fly.storage.tigris.dev/"
  #   secret: System.get_env("AWS_SECRET"),
  # config :boring, :s3,
  # This is the bucket where the images are stored.
  # prod is: "boring-business-production-mapbox-assets"
  # staging is: "boring-business-staging-mapbox-assets"
  image_static_path: System.get_env("MAPBOX_STATIC_IMAGE_PATH") || "/aerial_images",
  assets_bucket:
    System.get_env("MAPBOX_ASSETS_BUCKET") || "boring-business-staging-mapbox-assets",

  # This chooses the module which will be used to download the images.
  # In local dev we use the FileOps module to download the images to disk.
  # In staging/prod we use the S3Ops module to put objects in the bucket.
  io_backend:
    (cond do
       config_env() == :test -> Boring.Mocks.FileOpsMock
       System.get_env("MAPBOX_IO_BACKEND") == "S3_OPS" -> Boring.Api.Mapbox.S3Ops
       true -> Boring.Api.Mapbox.FileOps
     end)

config :boring, :stripe_production_mode, System.get_env("STRIPE_PRODUCTION_MODE") == "true"

config :boring, :tigris,
  access_key_id: System.get_env("TIGRIS_ACCESS_KEY_ID"),
  secret_access_key: System.get_env("TIGRIS_SECRET_ACCESS_KEY"),
  host: System.get_env("TIGRIS_HOST") || "fly.storage.tigris.dev"

config :boring,
  google_maps: [api_token: System.get_env("GOOGLE_MAPS_API_KEY")],
  apify: [api_token: System.get_env("APIFY_API_TOKEN")],
  endato: [
    api_token: System.get_env("ENDATO_API_TOKEN"),
    api_token_name: System.get_env("ENDATO_API_KEY_NAME")
  ]

config :langchain,
  openai_key: System.get_env("OPENAI_API_KEY") || openai_key,
  openai_org_id: System.get_env("OPENAI_ORG_ID") || openai_org_id,
  openai_proj_id: System.get_env("OPENAI_PROJ_ID") || openai_proj_id

config :stripity_stripe,
  api_key: System.get_env("STRIPE_SECRET") || stripe_api_key,
  signing_secret: System.get_env("STRIPE_WEBHOOK_SECRET") || stripe_signing_secret

config :ueberauth, Ueberauth.Strategy.Google.OAuth,
  client_id: System.get_env("GOOGLE_OAUTH_CLIENT_ID"),
  client_secret: System.get_env("GOOGLE_OAUTH_SECRET")

if config_env() == :prod do
  database_url =
    System.get_env("DATABASE_URL") ||
      raise """
      environment variable DATABASE_URL is missing.
      For example: ecto://USER:PASS@HOST/DATABASE
      """

  maybe_ipv6 = if System.get_env("ECTO_IPV6") in ~w(true 1), do: [:inet6], else: []

  # The secret key base is used to sign/encrypt cookies and other secrets.
  # A default value is used in config/dev.exs and config/test.exs but you
  # want to use a different value for prod and you most likely don't want
  # to check this value into version control, so we use an environment
  # variable instead.
  secret_key_base =
    System.get_env("SECRET_KEY_BASE") ||
      raise """
      environment variable SECRET_KEY_BASE is missing.
      You can generate one by calling: mix phx.gen.secret
      """

  host =
    System.get_env("PHX_HOST") ||
      raise """
      Environment variable PHX_HOST is missing.
      This is needed for your URLs to be generated properly.
      Set it to your domain name. eg 'example.com' or 'subdomain.example.com'."
      """

  port = String.to_integer(System.get_env("PORT") || "4000")

  environment_name =
    if String.contains?(host, "staging") do
      :staging
    else
      :production
    end

  config :boring, Boring.Repo,
    ssl: false,
    socket_options: maybe_ipv6,
    url: database_url,
    pool_size: String.to_integer(System.get_env("POOL_SIZE") || "10")

  config :boring, PetalPro.Mailer,
    adapter: Swoosh.Adapters.AmazonSES,
    region: System.get_env("AWS_SES_REGION"),
    access_key: System.get_env("AWS_ACCESS_KEY"),
    secret: System.get_env("AWS_SECRET")

  config :boring, PetalProWeb.Endpoint,
    server: true,
    url: [host: host, port: 443, scheme: "https"],
    http: [
      # Enable IPv6 and bind on all interfaces.
      # Set it to  {0, 0, 0, 0, 0, 0, 0, 1} for local network only access.
      # See the documentation on https://hexdocs.pm/plug_cowboy/Plug.Cowboy.html
      # for details about using IPv6 vs IPv4 and loopback vs public addresses.
      ip: {0, 0, 0, 0, 0, 0, 0, 0},
      port: port
    ],
    secret_key_base: secret_key_base

  config :boring, :dns_cluster_query, System.get_env("DNS_CLUSTER_QUERY")

  config :sentry,
    integrations: [
      oban: [
        # Capture errors:
        capture_errors: true,
        # Monitor cron jobs:
        cron: [enabled: true]
      ]
    ]

  config :tower, :reporters, [TowerSentry]

  config :tower_sentry,
    dsn: System.get_env("SENTRY_DSN"),
    environment_name: environment_name,
    enable_source_code_context: true,
    root_source_code_paths: [File.cwd!()],
    client: MyApp.SentryFinchHTTPClient
end
