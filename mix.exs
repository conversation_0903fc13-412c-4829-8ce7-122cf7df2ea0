defmodule PetalPro.MixProject do
  use Mix.Project

  @version "0.5.2"

  def project do
    [
      app: :boring,
      version: @version,
      elixir: "~> 1.18.3",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps(),
      dialyzer: [
        plt_add_apps: [:mix, :ex_unit],
        plt_core_path: "priv/plts",
        plt_file: {:no_warn, "priv/plts/dialyzer.plt"}
      ],
      test_coverage: [tool: ExCoveralls],
      preferred_cli_env: [
        quality: :test,
        coveralls: :test,
        "coveralls.detail": :test,
        "coveralls.post": :test,
        "coveralls.html": :test,
        vcr: :test,
        "vcr.delete": :test,
        "vcr.check": :test,
        "vcr.show": :test
      ]
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {PetalPro.Application, []},
      extra_applications: [:logger, :runtime_tools, :os_mon]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  # Type `mix deps.update --all` to update deps (won't updated this file)
  # Type `mix hex.outdated` to see deps that can be updated
  defp deps do
    [
      {:maplibre, "~> 0.1"},
      {:ash_ai, "~> 0.2"},
      {:geo_turf, "~> 0.3"},
      {:geo, "~> 4.0", override: true},
      {:git_ops, "~> 2.0", only: [:dev, :test], runtime: false},
      {:usage_rules, "~> 0.1", only: [:dev]},
      {:mix_audit, "~> 2.0", only: [:dev, :test], runtime: false},
      {:ex_check, "~> 0.14", only: [:dev], runtime: false},
      {:live_debugger, "~> 0.1", only: :dev},
      {:tidewave, "~> 0.1", only: :dev},
      {:tower, "~> 0.8", override: true},
      {:tower_sentry, "~> 0.3"},
      {:sentry, "~> 10.8"},
      {:remote_ip, "~> 1.2"},
      {:phx_2_ban, "~> 0.2.0", repo: "ezsuite"},
      {:recase, "~> 0.8"},
      {:igniter, "~> 0.5", only: [:dev, :test]},

      # Ash Framwork
      {:ash_oban, "~> 0.4"},
      {:ex_money_sql, "~> 1.11"},
      {:ex_money, "~> 5.19"},
      {:ash_money, "~> 0.1"},
      {:ash_archival, "~> 1.1"},
      {:ash_state_machine, "~> 0.2"},
      {:ash_phoenix, "~> 2.1"},
      {:ash_postgres, "~> 2.5"},
      {:ecto, "~> 3.12"},
      {:picosat_elixir, "~> 0.2"},
      {:ash, "~> 3.5"},

      # Phoenix base
      {:phoenix, "~> 1.7"},
      {:phoenix_ecto, "~> 4.6"},
      {:ecto_sql, "~> 3.12"},
      {:ecto_psql_extras, "~> 0.8"},
      {:postgrex, "~> 0.20"},
      {:phoenix_html, "~> 4.2"},
      {:phoenix_live_reload, "~> 1.5", only: :dev},
      {:phoenix_live_view, "~> 1.0"},
      {:heroicons, github: "tailwindlabs/heroicons", tag: "v2.2.0", app: false, compile: false, sparse: "optimized"},
      {:floki, "~> 0.37.1"},
      {:phoenix_live_dashboard, "~> 0.8"},
      {:swoosh, "~> 1.18"},
      {:telemetry_metrics, "~> 1.1"},
      {:telemetry_poller, "~> 1.1"},
      {:gettext, "~> 0.26"},
      {:jason, "~> 1.4"},
      {:bandit, "~> 1.6"},
      {:dns_cluster, "~> 0.2"},

      # Emails
      {:phoenix_swoosh, "~> 1.2"},
      {:gen_smtp, "~> 1.2"},
      {:premailex, "~> 0.3"},
      {:email_checker, "~> 0.2"},

      # Ecto querying / pagination
      {:query_builder, "~> 1.4"},
      {:flop, "~> 0.26"},
      {:typed_ecto_schema, "~> 0.4"},

      # Authentication
      {:bcrypt_elixir, "~> 3.2"},
      {:ueberauth, "~> 0.10"},
      {:ueberauth_google, "~> 0.12"},
      {:ueberauth_github, "~> 0.8"},

      # API
      {:open_api_spex, "~> 3.21"},

      # TOTP (2FA)
      {:nimble_totp, "~> 1.0.0"},
      {:eqrcode, "~> 0.2"},

      # Hashing
      {:hashids, "~> 2.1"},

      # Assets
      {:tailwind, "~> 0.3", runtime: Mix.env() == :dev},
      {:tailwind_formatter, "~> 0.4", only: [:dev, :test], runtime: false},

      # Petal components
      {:petal_components, "~> 3.0"},

      # Utils
      {:blankable, "~> 1.0.0"},
      {:currency_formatter, "~> 0.8"},
      {:timex, "~> 3.7", override: true},
      {:tzdata, "~> 1.1"},
      {:inflex, "~> 2.1"},
      {:slugify, "~> 1.3"},
      {:sizeable, "~> 1.0"},
      {:ex_phone_number, "~> 0.4"},

      # Localisation
      {:ex_cldr, "~> 2.40"},
      {:ex_cldr_dates_times, "~> 2.21"},
      {:ex_cldr_languages, "~> 0.3"},
      {:ex_cldr_numbers, "~> 2.33"},

      # HTTP client
      {:tesla, "~> 1.14"},
      {:finch, "~> 0.19"},
      {:httpoison, "~> 2.2"},
      {:req, "~> 0.5"},

      # Cloud Clients
      {:ex_aws, "~> 2.5"},
      {:ex_aws_s3, "~> 2.5"},

      # Testing
      {:faker, "~> 0.18"},
      {:mimic, "~> 1.11", only: :test},
      {:exvcr, "~> 0.16", only: :test},
      {:phoenix_test, "~> 0.5", only: :test, runtime: false},

      # Jobs / Cron
      {:oban, "~> 2.19"},
      {:oban_web, "~> 2.11"},

      # Markdown
      {:earmark, "~> 1.4"},
      {:html_sanitize_ex, "~> 1.4"},

      # Security
      {:content_security_policy, "~> 1.0"},

      # Code quality
      {:dialyxir, "~> 1.4", only: [:dev, :test], runtime: false},
      {:sobelow, "~> 0.13", only: [:dev, :test], runtime: false},
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
      {:excoveralls, "~> 0.18", only: [:dev, :test], runtime: false},
      {:styler, "~> 1.4", only: [:dev, :test], runtime: false},

      # Payments
      {:stripity_stripe, "~> 3.2"},

      # AI
      {:langchain, "~> 0.3"}

      # Temporary (to rename your project)
      # {:rename_project, "~> 0.1.0", only: :dev, runtime: false}
    ]
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: [
        "deps.get",
        "ash.setup",
        "assets.setup",
        "assets.build",
        "run priv/repo/seeds.exs",
        "cmd ./scripts/setup-hooks"
      ],
      reset: ["ash.reset", "run priv/repo/seeds.exs"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ash.setup --quiet", "test"],
      "assets.setup": ["tailwind.install --if-missing", "cmd npm --prefix assets install"],
      "assets.build": ["tailwind default", "cmd npm --prefix assets run build"],
      "assets.deploy": [
        "cmd npm --prefix assets install",
        "cmd npm --prefix assets run deploy",
        "tailwind default --minify",
        "phx.digest"
      ],
      # Run to check the quality of your code
      quality: [
        "format",
        "sobelow --config",
        "coveralls",
        "credo",
        "check_compilation_cycles"
      ],
      update_translations: ["gettext.extract --merge"],

      # Unlocks unused dependencies (no longer mentioned in the mix.exs file)
      clean_mix_lock: ["deps.unlock --unused"],
      check_compilation_cycles: "xref graph --format cycles --label compile-connected --fail-above 0",
      # Only run (e2e) tests
      feature_test: ["test --only feature"],
      seed: ["run priv/repo/seeds.exs"]
    ]
  end
end
