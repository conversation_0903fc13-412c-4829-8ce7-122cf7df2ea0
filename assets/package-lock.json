{"name": "myapp_assets", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "myapp_assets", "version": "0.1.0", "dependencies": {"@alpinejs/collapse": "^3.14.8", "@alpinejs/persist": "^3.13.10", "@editorjs/code": "^2.9.2", "@editorjs/delimiter": "^1.4.2", "@editorjs/editorjs": "^2.30.6", "@editorjs/embed": "^2.7.6", "@editorjs/header": "^2.8.7", "@editorjs/inline-code": "^1.5.1", "@editorjs/list": "^1.10.0", "@editorjs/marker": "^1.4.0", "@editorjs/quote": "^2.7.2", "@editorjs/simple-image": "^1.6.0", "@editorjs/table": "^2.4.1", "@editorjs/warning": "^1.4.0", "@floating-ui/dom": "^1.6.5", "@googlemaps/js-api-loader": "^1.16.8", "@sentry/browser": "^9.1.0", "alpinejs": "^3.13.10", "chart.js": "^4.4.3", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-autocolors": "^0.2.2", "luxon": "^3.4.4", "phoenix": "file:../deps/phoenix", "phoenix_html": "file:../deps/phoenix_html", "phoenix_live_view": "file:../deps/phoenix_live_view", "tippy.js": "^6.3.7", "tom-select": "^2.3.1"}, "devDependencies": {"esbuild": "^0.25"}}, "../deps/phoenix": {"version": "1.7.21", "license": "MIT"}, "../deps/phoenix_html": {"version": "4.2.1"}, "../deps/phoenix_live_view": {"version": "1.0.17", "license": "MIT", "dependencies": {"morphdom": "2.7.5"}, "devDependencies": {"@babel/cli": "7.27.0", "@babel/core": "7.26.10", "@babel/preset-env": "7.26.9", "@eslint/js": "^9.24.0", "@playwright/test": "^1.51.1", "@stylistic/eslint-plugin-js": "^4.2.0", "css.escape": "^1.5.1", "eslint": "9.24.0", "eslint-plugin-jest": "28.11.0", "eslint-plugin-playwright": "^2.2.0", "globals": "^16.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-monocart-coverage": "^1.1.1", "monocart-reporter": "^2.9.17", "phoenix": "1.7.21"}}, "node_modules/@alpinejs/collapse": {"version": "3.14.8", "resolved": "https://registry.npmjs.org/@alpinejs/collapse/-/collapse-3.14.8.tgz", "integrity": "sha512-dGlYLgqKemTcCGZJBTroShK0sEM6ZZsX4z5oeTppjJfZOlXKTn7UJF2CzCcGX/RhZRr6RuX0EU/bL+UJushYpw==", "license": "MIT"}, "node_modules/@alpinejs/persist": {"version": "3.13.10", "license": "MIT"}, "node_modules/@codexteam/icons": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.5.tgz", "integrity": "sha512-s6H2KXhLz2rgbMZSkRm8dsMJvyUNZsEjxobBEg9ztdrb1B2H3pEzY6iTwI4XUPJWJ3c3qRKwV4TrO3J5jUdoQA=="}, "node_modules/@editorjs/code": {"version": "2.9.2", "resolved": "https://registry.npmjs.org/@editorjs/code/-/code-2.9.2.tgz", "integrity": "sha512-KglaZMeL9fREMieyMJ5dWZR+eGPC/6qjWRc2EmwdwcuCsQVuQ1k7sgPzRL3phJWrtDqPvzNKJqjEji/OD7SF+Q==", "dependencies": {"@codexteam/icons": "^0.3.2"}}, "node_modules/@editorjs/code/node_modules/@codexteam/icons": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.3.2.tgz", "integrity": "sha512-P1ep2fHoy0tv4wx85eic+uee5plDnZQ1Qa6gDfv7eHPkCXorMtVqJhzMb75o1izogh6G7380PqmFDXV3bW3Pig=="}, "node_modules/@editorjs/delimiter": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/@editorjs/delimiter/-/delimiter-1.4.2.tgz", "integrity": "sha512-S8q2LpeYdYkVShLp7K8c4HLthDHBevLw+sT+iO0+SH0oMvFmld9SUon3DFzMQ2gG07EOdZGRZ958+sVxyvFjZw==", "dependencies": {"@codexteam/icons": "^0.3.2"}}, "node_modules/@editorjs/delimiter/node_modules/@codexteam/icons": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.3.2.tgz", "integrity": "sha512-P1ep2fHoy0tv4wx85eic+uee5plDnZQ1Qa6gDfv7eHPkCXorMtVqJhzMb75o1izogh6G7380PqmFDXV3bW3Pig=="}, "node_modules/@editorjs/dom": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/@editorjs/dom/-/dom-0.0.5.tgz", "integrity": "sha512-SZ78Gwpkp3EUhjBIp0lSojeQ35V9acF8SubJsMeOH/vlOUE40GOnvvwWZnF05lO7bIB0dOHhhJy4N7IIAWxP2w==", "dependencies": {"@editorjs/helpers": "^0.0.4"}}, "node_modules/@editorjs/editorjs": {"version": "2.30.6", "resolved": "https://registry.npmjs.org/@editorjs/editorjs/-/editorjs-2.30.6.tgz", "integrity": "sha512-6eQMc4Di3Hz9p4o+NGRgKaCeAF7eAk106m+bsDLc4eo94VGYO1M163OiGFdmanE+w503qTmXOzycWff5blEOAQ=="}, "node_modules/@editorjs/embed": {"version": "2.7.6", "resolved": "https://registry.npmjs.org/@editorjs/embed/-/embed-2.7.6.tgz", "integrity": "sha512-L3agW/23mOI0L+oksUE9UOR5VSNCqapxLH5lma+5j+idjKCC31nxbx07x53MSJ4rlOTO1L7cFVhkqptEdOliJA==", "dependencies": {"@editorjs/editorjs": "^2.29.1"}}, "node_modules/@editorjs/header": {"version": "2.8.7", "resolved": "https://registry.npmjs.org/@editorjs/header/-/header-2.8.7.tgz", "integrity": "sha512-rfxzYFR/Jhaocj3Xxx8XjEjyzfPbBIVkcPZ9Uy3rEz1n3ewhV0V4zwuxCjVfFhLUVgQQExq43BxJnTNlLOzqDQ==", "dependencies": {"@codexteam/icons": "^0.0.5", "@editorjs/editorjs": "^2.29.1"}}, "node_modules/@editorjs/helpers": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/@editorjs/helpers/-/helpers-0.0.4.tgz", "integrity": "sha512-ieg3dzo2m1/ELze/RMNADiAiC5amXxIlVXoJ5vvXITOu/p/dPsrF+Oi3h5gBYvtGk9vg5LJUSG5YWU0tBUO1tw=="}, "node_modules/@editorjs/inline-code": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/@editorjs/inline-code/-/inline-code-1.5.1.tgz", "integrity": "sha512-XvKpqw9y1bOYgyuVUHGDuu7KlNWCYIXD5uU1Lpc4s4LJ8VN4TjWFGKJ3sS+LR7zzasWe8hu2ffR1JC6MHIS0EQ==", "dependencies": {"@codexteam/icons": "^0.3.2"}}, "node_modules/@editorjs/inline-code/node_modules/@codexteam/icons": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.3.2.tgz", "integrity": "sha512-P1ep2fHoy0tv4wx85eic+uee5plDnZQ1Qa6gDfv7eHPkCXorMtVqJhzMb75o1izogh6G7380PqmFDXV3bW3Pig=="}, "node_modules/@editorjs/list": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/@editorjs/list/-/list-1.10.0.tgz", "integrity": "sha512-zXCHaNcIscpefnteBOS3x+98f/qBgEVsv+OvtKoTDZipMNqck2uVG+X2qMQr8xcwtJrj9ySX54lUac9FDlAHnA==", "dependencies": {"@codexteam/icons": "^0.0.4"}}, "node_modules/@editorjs/list/node_modules/@codexteam/icons": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.4.tgz", "integrity": "sha512-V8N/TY2TGyas4wLrPIFq7bcow68b3gu8DfDt1+rrHPtXxcexadKauRJL6eQgfG7Z0LCrN4boLRawR4S9gjIh/Q=="}, "node_modules/@editorjs/marker": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@editorjs/marker/-/marker-1.4.0.tgz", "integrity": "sha512-5ipEXfL44jTTRzgNp/p/YjMO7jT08S5z4V8qA3FFJTfdhKgQyM3mvP1zpdYKw47ZZpVWMCncvk5Nto3BxihEtg==", "dependencies": {"@codexteam/icons": "^0.0.5"}}, "node_modules/@editorjs/quote": {"version": "2.7.2", "resolved": "https://registry.npmjs.org/@editorjs/quote/-/quote-2.7.2.tgz", "integrity": "sha512-C2yB6TdBJsmfcwe2rirSjPMCQ3jTz8oOwOPbOFeLDcvUgxECEIws8kZOkuxXePdWua68QwIpUFH7yWf338lwUQ==", "dependencies": {"@codexteam/icons": "^0.3.2", "@editorjs/dom": "^0.0.5"}}, "node_modules/@editorjs/quote/node_modules/@codexteam/icons": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.3.2.tgz", "integrity": "sha512-P1ep2fHoy0tv4wx85eic+uee5plDnZQ1Qa6gDfv7eHPkCXorMtVqJhzMb75o1izogh6G7380PqmFDXV3bW3Pig=="}, "node_modules/@editorjs/simple-image": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@editorjs/simple-image/-/simple-image-1.6.0.tgz", "integrity": "sha512-WvdGfQPlozwZd3PXQrJnRXk6gEYbv1U2vRupYJ6lTd3/UsLInXYUX5jSFcnGB5ZMH3bd0JDZfcb4d4Sv1/1big==", "dependencies": {"@codexteam/icons": "^0.0.6"}}, "node_modules/@editorjs/simple-image/node_modules/@codexteam/icons": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.6.tgz", "integrity": "sha512-L7Q5PET8PjKcBT5wp7VR+FCjwCi5PUp7rd/XjsgQ0CI5FJz0DphyHGRILMuDUdCW2MQT9NHbVr4QP31vwAkS/A=="}, "node_modules/@editorjs/table": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@editorjs/table/-/table-2.4.1.tgz", "integrity": "sha512-2OtwS4+i9xYE0UCZV4NuQ4KM3dCnv6iqKEjDU5bu4Bolf7AkYTIfQ7+axJLCrb7glz3ulWXILweBpCnqYqr+AA==", "dependencies": {"@codexteam/icons": "^0.0.6"}}, "node_modules/@editorjs/table/node_modules/@codexteam/icons": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.6.tgz", "integrity": "sha512-L7Q5PET8PjKcBT5wp7VR+FCjwCi5PUp7rd/XjsgQ0CI5FJz0DphyHGRILMuDUdCW2MQT9NHbVr4QP31vwAkS/A=="}, "node_modules/@editorjs/warning": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@editorjs/warning/-/warning-1.4.0.tgz", "integrity": "sha512-JMYm3hrmN4/S6aHBHzKsJab9+8AJRPaPh9wDGE6QrVF3dj2fCo/DZcc0MaT5TN4YI5Hu4GKBH52wOKFGOyAuoA==", "dependencies": {"@codexteam/icons": "^0.0.4"}}, "node_modules/@editorjs/warning/node_modules/@codexteam/icons": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.4.tgz", "integrity": "sha512-V8N/TY2TGyas4wLrPIFq7bcow68b3gu8DfDt1+rrHPtXxcexadKauRJL6eQgfG7Z0LCrN4boLRawR4S9gjIh/Q=="}, "node_modules/@esbuild/aix-ppc64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.0.tgz", "integrity": "sha512-O7vun9Sf8DFjH2UtqK8Ku3LkquL9SZL8OLY1T5NZkA34+wG3OQF7cl4Ql8vdNzM6fzBbYfLaiRLIOZ+2FOCgBQ==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.0.tgz", "integrity": "sha512-PTyWCYYiU0+1eJKmw21lWtC+d08JDZPQ5g+kFyxP0V+es6VPPSUhM6zk8iImp2jbV6GwjX4pap0JFbUQN65X1g==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.0.tgz", "integrity": "sha512-grvv8WncGjDSyUBjN9yHXNt+cq0snxXbDxy5pJtzMKGmmpPxeAmAhWxXI+01lU5rwZomDgD3kJwulEnhTRUd6g==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.0.tgz", "integrity": "sha512-m/ix7SfKG5buCnxasr52+LI78SQ+wgdENi9CqyCXwjVR2X4Jkz+BpC3le3AoBPYTC9NHklwngVXvbJ9/Akhrfg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.0.tgz", "integrity": "sha512-mVwdUb5SRkPayVadIOI78K7aAnPamoeFR2bT5nszFUZ9P8UpK4ratOdYbZZXYSqPKMHfS1wdHCJk1P1EZpRdvw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.0.tgz", "integrity": "sha512-DgDaYsPWFTS4S3nWpFcMn/33ZZwAAeAFKNHNa1QN0rI4pUjgqf0f7ONmXf6d22tqTY+H9FNdgeaAa+YIFUn2Rg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.0.tgz", "integrity": "sha512-VN4ocxy6dxefN1MepBx/iD1dH5K8qNtNe227I0mnTRjry8tj5MRk4zprLEdG8WPyAPb93/e4pSgi1SoHdgOa4w==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.0.tgz", "integrity": "sha512-mrSgt7lCh07FY+hDD1TxiTyIHyttn6vnjesnPoVDNmDfOmggTLXRv8Id5fNZey1gl/V2dyVK1VXXqVsQIiAk+A==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.0.tgz", "integrity": "sha512-vkB3IYj2IDo3g9xX7HqhPYxVkNQe8qTK55fraQyTzTX/fxaDtXiEnavv9geOsonh2Fd2RMB+i5cbhu2zMNWJwg==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.0.tgz", "integrity": "sha512-9QAQjTWNDM/Vk2bgBl17yWuZxZNQIF0OUUuPZRKoDtqF2k4EtYbpyiG5/Dk7nqeK6kIJWPYldkOcBqjXjrUlmg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.0.tgz", "integrity": "sha512-43ET5bHbphBegyeqLb7I1eYn2P/JYGNmzzdidq/w0T8E2SsYL1U6un2NFROFRg1JZLTzdCoRomg8Rvf9M6W6Gg==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.0.tgz", "integrity": "sha512-fC95c/xyNFueMhClxJmeRIj2yrSMdDfmqJnyOY4ZqsALkDrrKJfIg5NTMSzVBr5YW1jf+l7/cndBfP3MSDpoHw==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.0.tgz", "integrity": "sha512-nkAMFju7KDW73T1DdH7glcyIptm95a7Le8irTQNO/qtkoyypZAnjchQgooFUDQhNAy4iu08N79W4T4pMBwhPwQ==", "cpu": ["mips64el"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.0.tgz", "integrity": "sha512-NhyOejdhRGS8Iwv+KKR2zTq2PpysF9XqY+Zk77vQHqNbo/PwZCzB5/h7VGuREZm1fixhs4Q/qWRSi5zmAiO4Fw==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.0.tgz", "integrity": "sha512-5S/rbP5OY+GHLC5qXp1y/Mx//e92L1YDqkiBbO9TQOvuFXM+iDqUNG5XopAnXoRH3FjIUDkeGcY1cgNvnXp/kA==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.0.tgz", "integrity": "sha512-XM2BFsEBz0Fw37V0zU4CXfcfuACMrppsMFKdYY2WuTS3yi8O1nFOhil/xhKTmE1nPmVyvQJjJivgDT+xh8pXJA==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.0.tgz", "integrity": "sha512-9yl91rHw/cpwMCNytUDxwj2XjFpxML0y9HAOH9pNVQDpQrBxHy01Dx+vaMu0N1CKa/RzBD2hB4u//nfc+Sd3Cw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.0.tgz", "integrity": "sha512-RuG4PSMPFfrkH6UwCAqBzauBWTygTvb1nxWasEJooGSJ/NwRw7b2HOwyRTQIU97Hq37l3npXoZGYMy3b3xYvPw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.0.tgz", "integrity": "sha512-jl+qisSB5jk01N5f7sPCsBENCOlPiS/xptD5yxOx2oqQfyourJwIKLRA2yqWdifj3owQZCL2sn6o08dBzZGQzA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.0.tgz", "integrity": "sha512-21sUNbq2r84YE+SJDfaQRvdgznTD8Xc0oc3p3iW/a1EVWeNj/SdUCbm5U0itZPQYRuRTW20fPMWMpcrciH2EJw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.0.tgz", "integrity": "sha512-2gwwriSMPcCFRlPlKx3zLQhfN/2WjJ2NSlg5TKLQOJdV0mSxIcYNTMhk3H3ulL/cak+Xj0lY1Ym9ysDV1igceg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.0.tgz", "integrity": "sha512-bxI7ThgLzPrPz484/S9jLlvUAHYMzy6I0XiU1ZMeAEOBcS0VePBFxh1JjTQt3Xiat5b6Oh4x7UC7IwKQKIJRIg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.0.tgz", "integrity": "sha512-ZUAc2YK6JW89xTbXvftxdnYy3m4iHIkDtK3CLce8wg8M2L+YZhIvO1DKpxrd0Yr59AeNNkTiic9YLf6FTtXWMw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.0.tgz", "integrity": "sha512-eSNxISBu8XweVEWG31/JzjkIGbGIJN/TrRoiSVZwZ6pkC6VX4Im/WV2cz559/TXLcYbcrDN8JtKgd9DJVIo8GA==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.0.tgz", "integrity": "sha512-ZENoHJBxA20C2zFzh6AI4fT6RraMzjYw4xKWemRTRmRVtN9c5DcH9r/f2ihEkMjOW5eGgrwCslG/+Y/3bL+DHQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@floating-ui/core": {"version": "1.6.2", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.0"}}, "node_modules/@floating-ui/dom": {"version": "1.6.5", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.0.0", "@floating-ui/utils": "^0.2.0"}}, "node_modules/@floating-ui/utils": {"version": "0.2.2", "license": "MIT"}, "node_modules/@googlemaps/js-api-loader": {"version": "1.16.8", "resolved": "https://registry.npmjs.org/@googlemaps/js-api-loader/-/js-api-loader-1.16.8.tgz", "integrity": "sha512-CROqqwfKotdO6EBjZO/gQGVTbeDps5V7Mt9+8+5Q+jTg5CRMi3Ii/L9PmV3USROrt2uWxtGzJHORmByxyo9pSQ==", "license": "Apache-2.0"}, "node_modules/@kurkle/color": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@kurkle/color/-/color-0.3.2.tgz", "integrity": "sha512-fuscdXJ9G1qb7W8VdHi+IwRqij3lBkosAm4ydQtEmbY58OzHXqQhvlxqEkoz0yssNVn38bcpRWgA9PP+OGoisw=="}, "node_modules/@orchidjs/sifter": {"version": "1.0.3", "license": "Apache-2.0", "dependencies": {"@orchidjs/unicode-variants": "^1.0.4"}}, "node_modules/@orchidjs/unicode-variants": {"version": "1.0.4", "license": "Apache-2.0"}, "node_modules/@popperjs/core": {"version": "2.11.8", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@sentry-internal/browser-utils": {"version": "9.1.0", "resolved": "https://registry.npmjs.org/@sentry-internal/browser-utils/-/browser-utils-9.1.0.tgz", "integrity": "sha512-S1uT+kkFlstWpwnaBTIJSwwAID8PS3aA0fIidOjNezeoUE5gOvpsjDATo9q+sl6FbGWynxMz6EnYSrq/5tuaBQ==", "license": "MIT", "dependencies": {"@sentry/core": "9.1.0"}, "engines": {"node": ">=18"}}, "node_modules/@sentry-internal/feedback": {"version": "9.1.0", "resolved": "https://registry.npmjs.org/@sentry-internal/feedback/-/feedback-9.1.0.tgz", "integrity": "sha512-jTDCqkqH3QDC8m9WO4mB06hqnBRsl3p7ozoh0E774UvNB6blOEZjShhSGMMEy5jbbJajPWsOivCofUtFAwbfGw==", "license": "MIT", "dependencies": {"@sentry/core": "9.1.0"}, "engines": {"node": ">=18"}}, "node_modules/@sentry-internal/replay": {"version": "9.1.0", "resolved": "https://registry.npmjs.org/@sentry-internal/replay/-/replay-9.1.0.tgz", "integrity": "sha512-E2xrUoms90qvm0BVOuaZ8QfkMoTUEgoIW/35uOeaqNcL7uOIj8c5cSEQQKit2Dr7CL6W+Ci5c6Khdyd5C0NL5w==", "license": "MIT", "dependencies": {"@sentry-internal/browser-utils": "9.1.0", "@sentry/core": "9.1.0"}, "engines": {"node": ">=18"}}, "node_modules/@sentry-internal/replay-canvas": {"version": "9.1.0", "resolved": "https://registry.npmjs.org/@sentry-internal/replay-canvas/-/replay-canvas-9.1.0.tgz", "integrity": "sha512-gxredVe+mOgfNqDJ3dTLiRON3FK1rZ8d0LHp7TICK/umLkWFkuso0DbNeyKU+3XCEjCr9VM7ZRqTDMzmY6zyVg==", "license": "MIT", "dependencies": {"@sentry-internal/replay": "9.1.0", "@sentry/core": "9.1.0"}, "engines": {"node": ">=18"}}, "node_modules/@sentry/browser": {"version": "9.1.0", "resolved": "https://registry.npmjs.org/@sentry/browser/-/browser-9.1.0.tgz", "integrity": "sha512-G55e5j77DqRW3LkalJLAjRRfuyKrjHaKTnwIYXa6ycO+Q1+l14pEUxu+eK5Abu2rtSdViwRSb5/G6a/miSUlYA==", "license": "MIT", "dependencies": {"@sentry-internal/browser-utils": "9.1.0", "@sentry-internal/feedback": "9.1.0", "@sentry-internal/replay": "9.1.0", "@sentry-internal/replay-canvas": "9.1.0", "@sentry/core": "9.1.0"}, "engines": {"node": ">=18"}}, "node_modules/@sentry/core": {"version": "9.1.0", "resolved": "https://registry.npmjs.org/@sentry/core/-/core-9.1.0.tgz", "integrity": "sha512-djWEzSBpMgqdF3GQuxO+kXCUX+Mgq42G4Uah/HSUBvPDHKipMmyWlutGRoFyVPPOnCDgpHu3wCt83wbpEyVmDw==", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/@vue/reactivity": {"version": "3.1.5", "license": "MIT", "dependencies": {"@vue/shared": "3.1.5"}}, "node_modules/@vue/shared": {"version": "3.1.5", "license": "MIT"}, "node_modules/alpinejs": {"version": "3.13.10", "license": "MIT", "dependencies": {"@vue/reactivity": "~3.1.1"}}, "node_modules/chart.js": {"version": "4.4.3", "resolved": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.3.tgz", "integrity": "sha512-qK1gkGSRYcJzqrrzdR6a+I0vQ4/R+SoODXyAjscQ/4mzuNzySaMCd+hyVxitSY1+L2fjPD1Gbn+ibNqRmwQeLw==", "dependencies": {"@kurkle/color": "^0.3.0"}, "engines": {"pnpm": ">=8"}}, "node_modules/chartjs-adapter-date-fns": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/chartjs-adapter-date-fns/-/chartjs-adapter-date-fns-3.0.0.tgz", "integrity": "sha512-Rs3iEB3Q5pJ973J93OBTpnP7qoGwvq3nUnoMdtxO+9aoJof7UFcRbWcIDteXuYd1fgAvct/32T9qaLyLuZVwCg==", "peerDependencies": {"chart.js": ">=2.8.0", "date-fns": ">=2.0.0"}}, "node_modules/chartjs-plugin-autocolors": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/chartjs-plugin-autocolors/-/chartjs-plugin-autocolors-0.2.2.tgz", "integrity": "sha512-zKSivIHRL3yB6a47bcxOfIEOEByL+AMXTtcnscb7I3Vu4PGNCyPe4C+4XxLGvR52lMYgFYcMcYmaXksi1bbA+A==", "peerDependencies": {"@kurkle/color": "^0.3.1", "chart.js": ">=2"}}, "node_modules/date-fns": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/date-fns/-/date-fns-3.6.0.tgz", "integrity": "sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==", "peer": true, "funding": {"type": "github", "url": "https://github.com/sponsors/kossnocorp"}}, "node_modules/esbuild": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.25.0.tgz", "integrity": "sha512-BXq5mqc8ltbaN34cDqWuYKyNhX8D/Z0J1xdtdQ8UcIIIyJyz+ZMKUt58tF3SrZ85jcfN/PZYhjR5uDQAYNVbuw==", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.0", "@esbuild/android-arm": "0.25.0", "@esbuild/android-arm64": "0.25.0", "@esbuild/android-x64": "0.25.0", "@esbuild/darwin-arm64": "0.25.0", "@esbuild/darwin-x64": "0.25.0", "@esbuild/freebsd-arm64": "0.25.0", "@esbuild/freebsd-x64": "0.25.0", "@esbuild/linux-arm": "0.25.0", "@esbuild/linux-arm64": "0.25.0", "@esbuild/linux-ia32": "0.25.0", "@esbuild/linux-loong64": "0.25.0", "@esbuild/linux-mips64el": "0.25.0", "@esbuild/linux-ppc64": "0.25.0", "@esbuild/linux-riscv64": "0.25.0", "@esbuild/linux-s390x": "0.25.0", "@esbuild/linux-x64": "0.25.0", "@esbuild/netbsd-arm64": "0.25.0", "@esbuild/netbsd-x64": "0.25.0", "@esbuild/openbsd-arm64": "0.25.0", "@esbuild/openbsd-x64": "0.25.0", "@esbuild/sunos-x64": "0.25.0", "@esbuild/win32-arm64": "0.25.0", "@esbuild/win32-ia32": "0.25.0", "@esbuild/win32-x64": "0.25.0"}}, "node_modules/luxon": {"version": "3.4.4", "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/phoenix": {"resolved": "../deps/phoenix", "link": true}, "node_modules/phoenix_html": {"resolved": "../deps/phoenix_html", "link": true}, "node_modules/phoenix_live_view": {"resolved": "../deps/phoenix_live_view", "link": true}, "node_modules/tippy.js": {"version": "6.3.7", "license": "MIT", "dependencies": {"@popperjs/core": "^2.9.0"}}, "node_modules/tom-select": {"version": "2.3.1", "license": "Apache-2.0", "dependencies": {"@orchidjs/sifter": "^1.0.3", "@orchidjs/unicode-variants": "^1.0.4"}, "engines": {"node": "*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/tom-select"}}}}