const esbuild = require("esbuild");

// Decide which mode to proceed with
let mode = "build";
process.argv.slice(2).forEach((arg) => {
  if (arg === "--watch") {
    mode = "watch";
  } else if (arg === "--deploy") {
    mode = "deploy";
  }
});

// Define esbuild options + extras for watch and deploy
let opts = {
  entryPoints: ["js/app.js"],
  bundle: true,
  logLevel: "info",
  target: "es2017",
  outdir: "../priv/static/assets",
  sourcemap: mode === "watch" ? "inline" : undefined, // Conditional sourcemap
  minify: mode === "deploy" ? true : undefined, // Conditional minify
};

// Start esbuild with previously defined options
// Stop the watcher when STDIN gets closed (no zombies please!)
const startEsbuild = async () => {
  try {
    if (mode === "watch") {
      let ctx = await esbuild.context(opts);
      await ctx.watch();

      // Stop the watcher when STDIN gets closed (no zombies please!)
      process.stdin.pipe(process.stdout);
      process.stdin.on("end", () => {
        ctx.dispose();
      });
    } else {
      await esbuild.build(opts);
    }
  } catch (error) {
    console.error(error);
    process.exit(1);
  }
};

startEsbuild();
