// No longer need js-api-loader - using native google.maps.importLibrary
import { CoreEventSerializers, genericSerialize } from "./google-maps-serializers/core.js";
import { DrawingEventSerializers } from "./google-maps-serializers/drawing.js";
import { PlacesEventSerializers } from "./google-maps-serializers/places.js";
import { GeometryEventSerializers } from "./google-maps-serializers/geometry.js";
import { MarkerEventSerializers } from "./google-maps-serializers/marker.js";

// Now using simplified event serialization system and native Google Maps library loading

// Simple mapping of event types to their serializers
const EVENT_SERIALIZERS = {
  ...CoreEventSerializers,
  ...DrawingEventSerializers,
  ...PlacesEventSerializers,
  ...GeometryEventSerializers,
  ...MarkerEventSerializers
};

/**
 * GoogleMapImprovedHook - A flexible Google Maps hook that provides full SDK access
 *
 * Features:
 * - Native Google Maps library loading
 * - All Google Maps SDK events forwarded to LiveView
 * - Flexible configuration through assigns
 * - Performance optimized with phx-update="ignore"
 */
const GoogleMapHook = {
  deadViewCompatible: true,

  mounted() {
    this.map = null;
    this.mapInitialized = false;
    this.eventListeners = [];
    this.currentConfig = null;
    this.markers = new Map(); // Store markers by ID
    this.markerEventListeners = new Map(); // Store marker event listeners
    this.shapes = new Map(); // Store shapes by ID
    this.drawingManager = null; // Store drawing manager instance
    this.debouncedEvents = new Map(); // Store debounced event handlers
    this.selectedMarkerId = null; // Track selected marker

    this.initializeMap();

    // Listen for generic update commands from LiveView
    this.handleEvent("update_map", (payload) => {
      this.handleMapUpdate(payload);
    });

    // Listen for drawing mode updates from LiveView
    this.handleEvent("update_drawing_mode", (payload) => {
      if (payload.target === this.el.id) {
        this.updateDrawingMode(payload.drawingMode);
      }
    });

    this.handleEvent("update_markers", (payload) => {
      this.updateMarkers(payload.markers);
    });

    // Listen for marker selection from LiveView
    this.handleEvent("select_marker", (payload) => {
      if (payload.target === this.el.id) {
        this.selectMarker(payload.markerId);
      }
    });

    this.handleEvent("deselect_markers", (payload) => {
      if (payload.target === this.el.id) {
        this.deselectAllMarkers();
      }
    });

    // Listen for map commands from LiveView
    this.handleEvent("map_add_shapes", (payload) => {
      if (payload.target === this.el.id) {
        console.log("Received map_add_shapes event:", payload);
        this.addShapes(payload.shapes);
      }
    });

    this.handleEvent("map_center_on", (payload) => {
      if (payload.target === this.el.id) {
        this.centerOn(payload.center, payload.zoom);
      }
    });

    this.handleEvent("map_clear_all", (payload) => {
      if (payload.target === this.el.id) {
        this.clearAll();
      }
    });

    // Listen for shape property updates from LiveView
    this.handleEvent("update_shape_radius", (payload) => {
      if (payload.target === this.el.id) {
        this.updateShapeRadius(payload.radius);
      }
    });

    // Listen for generic shape updates from LiveView
    this.handleEvent("update_shape", (payload) => {
      if (payload.target === this.el.id) {
        this.updateShape(payload.shapeId, payload.updates);
      }
    });
  },

  updated() {
    // With phx-update="ignore", this method is called when the LiveView updates
    // but the DOM element itself is not re-rendered
    // We need to check if the configuration has changed and update accordingly
    try {
      const newConfig = JSON.parse(this.el.dataset.config);

      // Check if configuration has actually changed
      if (JSON.stringify(newConfig) !== JSON.stringify(this.currentConfig)) {
        const oldConfig = this.currentConfig;
        this.currentConfig = newConfig;

        // Handle map options updates
        if (this.map && newConfig.mapOptions) {
          this.map.setOptions(newConfig.mapOptions);
        }

        // Handle marker updates if markers have changed
        if (this.mapInitialized && JSON.stringify(newConfig.markers) !== JSON.stringify(oldConfig?.markers)) {
          this.updateMarkers(newConfig.markers);
        }

        // Handle shape updates if shapes have changed
        if (this.mapInitialized && JSON.stringify(newConfig.shapes) !== JSON.stringify(oldConfig?.shapes)) {
          this.updateShapes(newConfig.shapes);
        }
      }
    } catch (error) {
      this.pushEvent("map_error", {
        mapId: this.el.id,
        error: error.message
      });
    }
  },

  destroyed() {
    this.cleanup();
  },

  async initializeMap() {
    try {
      const apiKey = this.el.dataset.apiKey;
      const config = JSON.parse(this.el.dataset.config);

      // Store the initial configuration
      this.currentConfig = config;

      if (!apiKey) {
        this.pushEvent("map_error", {
          mapId: this.el.id,
          error: "Google Maps API key is missing"
        });
        return;
      }

      // Load the Google Maps API with all libraries at once
      if (!window.google?.maps?.Map) {
        await this.loadGoogleMapsWithLibraries(apiKey, config);
      }

      // Initialize the map
      await this.createMap(config);

      // Set up event subscriptions
      this.setupEventHandlers(config.eventSubscriptions);

      // Create markers if any are provided
      if (config.markers && config.markers.length > 0) {
        await this.createMarkers(config.markers);
      }

      // Create shapes if any are provided
      if (config.shapes && config.shapes.length > 0) {
        this.createShapes(config.shapes);
      }

      // Initialize drawing manager only if drawingMode is explicitly provided
      if (config.libraryConfigs && config.libraryConfigs.drawing && config.libraryConfigs.drawing.drawingMode) {
        this.initializeDrawingManager(config.libraryConfigs.drawing);
      }

      this.mapInitialized = true;
    } catch (error) {
      this.pushEvent("map_error", {
        mapId: this.el.id,
        error: error.message
      });
    }
  },

  async loadGoogleMapsWithLibraries(apiKey, config) {
    // Load the Google Maps API with all libraries at once
    return new Promise((resolve, reject) => {
      // Check if Google Maps script is already loaded for this specific map
      const scriptId = `google-maps-script-${this.el.id}`;
      if (document.getElementById(scriptId) || window.google?.maps?.Map) {
        resolve();
        return;
      }

      // Determine libraries from libraryConfigs keys (plus always include 'maps')
      const libraryConfigs = config.libraryConfigs || {};
      const configLibraries = Object.keys(libraryConfigs);
      // Filter out 'maps' from config libraries and ensure we include 'marker' if present
      const filteredLibraries = configLibraries.filter(lib => lib !== 'maps');
      const libraries = ['maps', ...filteredLibraries];

      // Set up the callback function
      window.initGoogleMaps = () => {
        resolve();
      };

      const script = document.createElement('script');
      script.id = scriptId; // Set the script ID for tracking

      const params = new URLSearchParams({
        key: apiKey,
        callback: 'initGoogleMaps',
        loading: 'async'
      });

      const loaderOptions = config.loaderOptions || {};
      if (loaderOptions.version) params.set('v', loaderOptions.version);
      if (loaderOptions.region) params.set('region', loaderOptions.region);
      if (loaderOptions.language) params.set('language', loaderOptions.language);

      // Add all libraries to the URL
      if (libraries.length > 0) {
        params.set('libraries', libraries.join(','));
      }

      script.src = `https://maps.googleapis.com/maps/api/js?${params.toString()}`;
      script.async = true;

      script.onerror = (error) => {
        console.error('Failed to load Google Maps API:', error);
        reject(new Error('Failed to load Google Maps API'));
      };

      document.head.appendChild(script);
    });
  },

  async createMap(config) {
    // Ensure we have the Google Maps API loaded
    if (!window.google?.maps?.Map) {
      throw new Error("Google Maps library not loaded");
    }

    // Convert string constants to Google Maps API constants
    const processedMapOptions = this.processMapOptions(config.mapOptions);

    // Create the map instance
    this.map = new google.maps.Map(this.el, processedMapOptions);
  },

  processMapOptions(mapOptions) {
    if (!mapOptions) return mapOptions;

    const processed = { ...mapOptions };

    // Convert mapTypeControlOptions
    if (processed.mapTypeControlOptions) {
      const controlOptions = { ...processed.mapTypeControlOptions };

      // Convert style string to Google Maps constant
      if (controlOptions.style === "DROPDOWN_MENU") {
        controlOptions.style = google.maps.MapTypeControlStyle.DROPDOWN_MENU;
      } else if (controlOptions.style === "HORIZONTAL_BAR") {
        controlOptions.style = google.maps.MapTypeControlStyle.HORIZONTAL_BAR;
      } else if (controlOptions.style === "DEFAULT") {
        controlOptions.style = google.maps.MapTypeControlStyle.DEFAULT;
      }

      // Convert position string to Google Maps constant
      if (controlOptions.position) {
        controlOptions.position = this.convertControlPosition(controlOptions.position);
      }

      processed.mapTypeControlOptions = controlOptions;
    }

    // Convert zoomControlOptions
    if (processed.zoomControlOptions?.position) {
      processed.zoomControlOptions = {
        ...processed.zoomControlOptions,
        position: this.convertControlPosition(processed.zoomControlOptions.position)
      };
    }

    // Convert streetViewControlOptions
    if (processed.streetViewControlOptions?.position) {
      processed.streetViewControlOptions = {
        ...processed.streetViewControlOptions,
        position: this.convertControlPosition(processed.streetViewControlOptions.position)
      };
    }

    // Convert fullscreenControlOptions
    if (processed.fullscreenControlOptions?.position) {
      processed.fullscreenControlOptions = {
        ...processed.fullscreenControlOptions,
        position: this.convertControlPosition(processed.fullscreenControlOptions.position)
      };
    }

    return processed;
  },

  convertControlPosition(positionString) {
    const positionMap = {
      "TOP_CENTER": google.maps.ControlPosition.TOP_CENTER,
      "TOP_LEFT": google.maps.ControlPosition.TOP_LEFT,
      "TOP_RIGHT": google.maps.ControlPosition.TOP_RIGHT,
      "LEFT_TOP": google.maps.ControlPosition.LEFT_TOP,
      "RIGHT_TOP": google.maps.ControlPosition.RIGHT_TOP,
      "LEFT_CENTER": google.maps.ControlPosition.LEFT_CENTER,
      "RIGHT_CENTER": google.maps.ControlPosition.RIGHT_CENTER,
      "LEFT_BOTTOM": google.maps.ControlPosition.LEFT_BOTTOM,
      "RIGHT_BOTTOM": google.maps.ControlPosition.RIGHT_BOTTOM,
      "BOTTOM_CENTER": google.maps.ControlPosition.BOTTOM_CENTER,
      "BOTTOM_LEFT": google.maps.ControlPosition.BOTTOM_LEFT,
      "BOTTOM_RIGHT": google.maps.ControlPosition.BOTTOM_RIGHT
    };

    return positionMap[positionString] || positionString;
  },



  async createMarkers(markersData) {
    if (!this.map || !markersData || markersData.length === 0) {
      return;
    }

    // Ensure AdvancedMarkerElement is available
    if (!window.google?.maps?.marker?.AdvancedMarkerElement) {
      console.error("AdvancedMarkerElement not available - please ensure the marker library is loaded");
      return;
    }

    // Create all markers and wait for them to be ready
    const markerPromises = markersData.map(async (markerData) => {
      try {
        const marker = await this.createSingleAdvancedMarker(markerData);
        if (marker) {
          this.markers.set(markerData.id, marker);
          this.setupMarkerEventListeners(marker, markerData.id);
        }
        return marker;
      } catch (error) {
        console.error(`Failed to create marker ${markerData.id}:`, error);
        return null;
      }
    });

    // Wait for all markers to be created
    await Promise.all(markerPromises);
  },

  async createSingleAdvancedMarker(markerData) {
    const markerOptions = {
      map: this.map,
      position: markerData.position,
      title: markerData.title || null
    };

    if (markerData.color) {
      const pin = new google.maps.marker.PinElement({
        background: markerData.color.background,
        borderColor: markerData.color.border,
        glyphColor: markerData.color.glyph,
      });
      markerOptions.content = pin.element;
    }

    // Add custom content if provided
    if (markerData.content) {
      const contentElement = document.createElement('div');
      contentElement.innerHTML = markerData.content;
      markerOptions.content = contentElement;
    }

    // Add other marker options
    if (markerData.draggable !== undefined) {
      markerOptions.gmpDraggable = markerData.draggable;
    }

    if (markerData.zIndex !== undefined) {
      markerOptions.zIndex = markerData.zIndex;
    }

    // Create the AdvancedMarker
    const marker = new google.maps.marker.AdvancedMarkerElement(markerOptions);

    // Store marker data for event handling
    marker.id = markerData.id;
    marker.title = markerData.title;

    return marker;
  },

  createShapes(shapesData) {
    if (!this.map || !shapesData || shapesData.length === 0) {
      return;
    }

    shapesData.forEach(shapeData => {
      try {
        const shape = this.createSingleShape(shapeData);
        if (shape) {
          this.shapes.set(shapeData.id, shape);
        }
      } catch (error) {
        console.error(`Failed to create shape ${shapeData.id}:`, error);
      }
    });
  },

  createSingleShape(shapeData) {
    const { type, id, ...options } = shapeData;

    // Set common default options
    const defaultOptions = {
      map: this.map,
      strokeColor: "#FF0000",
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: "#FF0000",
      fillOpacity: 0.35,
      draggable: false,
      editable: false,
      clickable: true,
      zIndex: 1,
      visible: true
    };

    const shapeOptions = { ...defaultOptions, ...options };

    let shape;
    switch (type) {
      case 'circle':
        shape = this.createCircle(shapeOptions);
        break;
      case 'polygon':
        shape = this.createPolygon(shapeOptions);
        break;
      case 'polyline':
        shape = this.createPolyline(shapeOptions);
        break;
      case 'rectangle':
        shape = this.createRectangle(shapeOptions);
        break;
      default:
        console.error(`Unknown shape type: ${type}`);
        return null;
    }

    // Set up event listeners for draggable/editable shapes
    if (shape && (shapeOptions.draggable || shapeOptions.editable)) {
      const libraryConfigs = this.currentConfig?.libraryConfigs || {};
      const drawingConfig = libraryConfigs.drawing || {};
      const eventSubscriptions = drawingConfig.eventSubscriptions || [];

      if (eventSubscriptions.length > 0) {
        this.setupOverlayEventListeners(shape, eventSubscriptions, drawingConfig);
      }
    }

    return shape;
  },

  createCircle(options) {
    if (!options.center || typeof options.radius !== 'number') {
      console.error('Circle requires center and radius');
      return null;
    }

    return new google.maps.Circle({
      center: options.center,
      radius: options.radius,
      strokeColor: options.strokeColor,
      strokeOpacity: options.strokeOpacity,
      strokeWeight: options.strokeWeight,
      fillColor: options.fillColor,
      fillOpacity: options.fillOpacity,
      draggable: options.draggable,
      editable: options.editable,
      clickable: options.clickable,
      zIndex: options.zIndex,
      visible: options.visible,
      map: options.map
    });
  },

  createPolygon(options) {
    if (!options.paths || !Array.isArray(options.paths)) {
      console.error('Polygon requires paths array');
      return null;
    }

    return new google.maps.Polygon({
      paths: options.paths,
      strokeColor: options.strokeColor,
      strokeOpacity: options.strokeOpacity,
      strokeWeight: options.strokeWeight,
      fillColor: options.fillColor,
      fillOpacity: options.fillOpacity,
      draggable: options.draggable,
      editable: options.editable,
      clickable: options.clickable,
      zIndex: options.zIndex,
      visible: options.visible,
      map: options.map
    });
  },

  createPolyline(options) {
    if (!options.path || !Array.isArray(options.path)) {
      console.error('Polyline requires path array');
      return null;
    }

    return new google.maps.Polyline({
      path: options.path,
      strokeColor: options.strokeColor,
      strokeOpacity: options.strokeOpacity,
      strokeWeight: options.strokeWeight,
      draggable: options.draggable,
      editable: options.editable,
      clickable: options.clickable,
      zIndex: options.zIndex,
      visible: options.visible,
      map: options.map
    });
  },

  createRectangle(options) {
    if (!options.bounds) {
      console.error('Rectangle requires bounds');
      return null;
    }

    return new google.maps.Rectangle({
      bounds: options.bounds,
      strokeColor: options.strokeColor,
      strokeOpacity: options.strokeOpacity,
      strokeWeight: options.strokeWeight,
      fillColor: options.fillColor,
      fillOpacity: options.fillOpacity,
      draggable: options.draggable,
      editable: options.editable,
      clickable: options.clickable,
      zIndex: options.zIndex,
      visible: options.visible,
      map: options.map
    });
  },

  initializeDrawingManager(drawingConfig) {
    if (!this.map || !window.google?.maps?.drawing?.DrawingManager) {
      console.warn("DrawingManager not available");
      return;
    }

    // Clear existing drawing manager
    if (this.drawingManager) {
      this.drawingManager.setMap(null);
      this.drawingManager = null;
    }

    // Build drawing manager options
    const drawingManagerOptions = {
      map: this.map,
      drawingControl: drawingConfig.drawingControl || false,
      drawingControlOptions: {
        position: google.maps.ControlPosition.TOP_CENTER,
        drawingModes: [
          google.maps.drawing.OverlayType.MARKER,
          google.maps.drawing.OverlayType.CIRCLE,
          google.maps.drawing.OverlayType.POLYGON,
          google.maps.drawing.OverlayType.POLYLINE,
          google.maps.drawing.OverlayType.RECTANGLE
        ]
      }
    };

    // Set initial drawing mode if specified
    if (drawingConfig.drawingMode) {
      const modeMap = {
        'marker': google.maps.drawing.OverlayType.MARKER,
        'circle': google.maps.drawing.OverlayType.CIRCLE,
        'polygon': google.maps.drawing.OverlayType.POLYGON,
        'polyline': google.maps.drawing.OverlayType.POLYLINE,
        'rectangle': google.maps.drawing.OverlayType.RECTANGLE
      };

      if (modeMap[drawingConfig.drawingMode]) {
        drawingManagerOptions.drawingMode = modeMap[drawingConfig.drawingMode];
      }
    }

    // Add drawing options if specified
    if (drawingConfig.polygonOptions) {
      drawingManagerOptions.polygonOptions = drawingConfig.polygonOptions;
    }
    if (drawingConfig.circleOptions) {
      drawingManagerOptions.circleOptions = drawingConfig.circleOptions;
    }
    if (drawingConfig.polylineOptions) {
      drawingManagerOptions.polylineOptions = drawingConfig.polylineOptions;
    }
    if (drawingConfig.rectangleOptions) {
      drawingManagerOptions.rectangleOptions = drawingConfig.rectangleOptions;
    }
    if (drawingConfig.markerOptions) {
      drawingManagerOptions.markerOptions = drawingConfig.markerOptions;
    }

    // Create the drawing manager
    this.drawingManager = new google.maps.drawing.DrawingManager(drawingManagerOptions);

    // Set up drawing event listeners
    const eventHandlers = drawingConfig.eventSubscriptions || [];
    eventHandlers.forEach(eventType => {
      try {
        this.drawingManager.addListener(eventType, (event) => {
          this.handleDrawingEvent(eventType, event);

          // For overlaycomplete, also set up listeners on the created overlay
          if (eventType === 'overlaycomplete') {
            this.setupOverlayEventListeners(event.overlay, drawingConfig.eventSubscriptions, drawingConfig);
          }
        });
      } catch (error) {
        console.error(`Failed to add drawing event listener for ${eventType}:`, error);
      }
    });
  },

  updateDrawingMode(drawingMode) {
    if (!this.drawingManager) {
      console.warn("Drawing manager not initialized");
      return;
    }

    // Map drawing mode strings to Google Maps constants
    const modeMap = {
      'marker': google.maps.drawing.OverlayType.MARKER,
      'circle': google.maps.drawing.OverlayType.CIRCLE,
      'polygon': google.maps.drawing.OverlayType.POLYGON,
      'polyline': google.maps.drawing.OverlayType.POLYLINE,
      'rectangle': google.maps.drawing.OverlayType.RECTANGLE
    };

    // Set the drawing mode
    if (drawingMode && modeMap[drawingMode]) {
      this.drawingManager.setDrawingMode(modeMap[drawingMode]);
    } else {
      // Set to null to disable drawing
      this.drawingManager.setDrawingMode(null);
    }
  },

  setupEventHandlers(eventHandlers) {
    if (!this.map || !eventHandlers || eventHandlers.length === 0) {
      return;
    }

    // Clear existing event listeners first
    this.clearEventListeners();

    eventHandlers.forEach(eventType => {
      try {
        const listener = this.map.addListener(eventType, (event) => {
          this.handleMapEvent(eventType, event);
        });

        this.eventListeners.push({
          type: eventType,
          listener: listener
        });
      } catch (error) {
        // Silently skip failed event listeners
      }
    });
  },

  setupMarkerEventListeners(marker, markerId) {
    if (!marker) return;

    // Get marker event handlers from library config
    const libraryConfigs = this.currentConfig?.libraryConfigs || {};
    const markerConfig = libraryConfigs.marker || {};
    const eventHandlers = markerConfig.eventSubscriptions || markerConfig.eventHandlers || [];

    if (eventHandlers.length === 0) {
      // Default to common marker events if none specified
      eventHandlers.push('gmp-click', 'dragstart', 'drag', 'dragend', 'position_changed');
    }

    const markerListeners = [];

    eventHandlers.forEach(eventType => {
      try {
        const listener = marker.addListener(eventType, (event) => {
          this.handleMarkerEvent(eventType, event, marker);
        });

        markerListeners.push({
          type: eventType,
          listener: listener
        });
      } catch (error) {
        // Silently skip failed event listeners
      }
    });

    // Store listeners for cleanup
    this.markerEventListeners.set(markerId, markerListeners);
  },

  // Marker selection methods
  selectMarker(markerId) {
    // Deselect previous marker
    if (this.selectedMarkerId && this.selectedMarkerId !== markerId) {
      this.deselectMarker(this.selectedMarkerId);
    }

    // Select new marker
    const marker = this.markers.get(markerId);
    if (marker && marker.content) {
      // Scale up the marker by modifying its content
      const contentElement = marker.content;
      if (contentElement) {
        contentElement.style.transform = 'scale(1.3)';
        contentElement.style.zIndex = '1000';
        contentElement.style.transition = 'transform 0.2s ease-in-out';
      }
    }

    this.selectedMarkerId = markerId;
  },

  deselectMarker(markerId) {
    const marker = this.markers.get(markerId);
    if (marker && marker.content) {
      // Reset marker scale
      const contentElement = marker.content;
      if (contentElement) {
        contentElement.style.transform = 'scale(1)';
        contentElement.style.zIndex = '';
      }
    }
  },

  deselectAllMarkers() {
    if (this.selectedMarkerId) {
      this.deselectMarker(this.selectedMarkerId);
      this.selectedMarkerId = null;
    }
  },

  // Map command methods
  addShapes(shapes) {
    console.log("addShapes called with:", shapes);
    if (!this.map || !shapes) {
      console.log("addShapes early return - map:", !!this.map, "shapes:", !!shapes);
      return;
    }

    // Clear existing shapes first
    this.clearShapes();

    // Add new shapes using existing createShapes method
    console.log("Calling createShapes with:", shapes);
    this.createShapes(shapes);
  },

  centerOn(center, zoom) {
    if (!this.map || !center) return;

    this.map.setCenter(center);
    if (zoom !== undefined) {
      this.map.setZoom(zoom);
    }
  },

  clearAll() {
    this.clearMarkers();
    this.clearShapes();
    this.deselectAllMarkers();
  },

  clearShapes() {
    // Remove all shapes from map
    this.shapes.forEach((shape) => {
      if (shape && typeof shape.setMap === 'function') {
        shape.setMap(null);
      }
    });
    this.shapes.clear();
  },

  clearMarkers() {
    // Remove all markers from map
    this.markers.forEach((marker) => {
      if (marker && typeof marker.setMap === 'function') {
        marker.setMap(null);
      }
    });
    this.markers.clear();

    // Clear marker event listeners
    this.markerEventListeners.forEach((listeners) => {
      listeners.forEach((listener) => {
        if (listener && typeof listener.remove === 'function') {
          listener.remove();
        }
      });
    });
    this.markerEventListeners.clear();
  },

  // Debounce utility function
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func.apply(this, args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  clearEventListeners() {
    this.eventListeners.forEach(({ listener }) => {
      if (listener && listener.remove) {
        listener.remove();
      }
    });
    this.eventListeners = [];
  },

  handleMapEvent(eventType, event) {
    // Convert Google Maps event data to serializable format using the modular system
    const eventData = this.serializeEventData(eventType, event);

    // Send event to LiveView
    this.pushEvent("map_event", {
      type: eventType,
      data: eventData,
      mapId: this.el.id
    });
  },

  handleMarkerEvent(eventType, event, marker) {
    // Convert marker event data to serializable format
    const eventData = this.serializeEventData(eventType, event, marker);

    // Send marker event to LiveView
    this.pushEvent("marker_event", {
      type: eventType,
      data: eventData,
      mapId: this.el.id,
      markerId: marker.id
    });
  },

  handleDrawingEvent(eventType, event) {
    // Convert drawing event data to serializable format
    const eventData = this.serializeEventData(eventType, event);

    // Send drawing event to LiveView
    this.pushEvent("drawing_event", {
      type: eventType,
      data: eventData,
      mapId: this.el.id
    });
  },

  setupOverlayEventListeners(overlay, eventSubscriptions, drawingConfig = {}) {
    if (!overlay || !eventSubscriptions) return;

    // Store a unique ID on the overlay for tracking
    overlay._overlayId = 'overlay_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);

    // Map of overlay events we want to listen for
    const overlayEvents = [
      'path_changed',
      'center_changed',
      'radius_changed',
      'bounds_changed',
      'position_changed',
      'dragstart',
      'drag',
      'dragend'
    ];

    // Events that should be debounced (high frequency events)
    const debouncedEventTypes = ['path_changed', 'drag', 'center_changed', 'radius_changed', 'bounds_changed'];

    // Events that should fire immediately (start/end events)
    const immediateEventTypes = ['dragstart', 'dragend', 'position_changed'];

    // Only set up listeners for events that are subscribed to
    const eventsToListen = overlayEvents.filter(event =>
      eventSubscriptions.includes(event)
    );

    eventsToListen.forEach(eventType => {
      try {
        const eventHandler = () => {
          // Generate updated geometry when shape is modified
          const updatedGeometry = this.extractGeometryFromOverlay(overlay);

          this.pushEvent("shape_modified", {
            type: eventType,
            mapId: this.el.id,
            geometry: updatedGeometry,
            overlayId: overlay._overlayId
          });
        };

        if (debouncedEventTypes.includes(eventType)) {
          // Create debounced handler for high-frequency events
          const debounceMs = drawingConfig.debounceMs || 300; // Use config or default to 300ms
          const debouncedHandler = this.debounce(eventHandler, debounceMs);
          overlay.addListener(eventType, debouncedHandler);
        } else if (immediateEventTypes.includes(eventType)) {
          // Immediate handler for start/end events
          overlay.addListener(eventType, eventHandler);
        }
      } catch (error) {
        console.error(`Failed to add overlay event listener for ${eventType}:`, error);
      }
    });
  },

  extractGeometryFromOverlay(overlay) {
    // Determine overlay type and extract geometry as GeoJSON
    try {
      if (typeof overlay.getPath === 'function') {
        // Polygon or Polyline
        const path = overlay.getPath();
        const coordinates = [];
        for (let i = 0; i < path.getLength(); i++) {
          const point = path.getAt(i);
          coordinates.push([point.lng(), point.lat()]);
        }

        // Check if it's a polygon (closed path) or polyline
        const isPolygon = coordinates.length > 2 &&
          coordinates[0][0] === coordinates[coordinates.length - 1][0] &&
          coordinates[0][1] === coordinates[coordinates.length - 1][1];

        if (!isPolygon && coordinates.length > 2) {
          // Close polygon if not already closed
          coordinates.push(coordinates[0]);
        }

        // Return GeoJSON Feature
        if (isPolygon || coordinates.length > 2) {
          return {
            type: "Feature",
            geometry: {
              type: "Polygon",
              coordinates: [coordinates]
            },
            properties: {}
          };
        } else {
          return {
            type: "Feature",
            geometry: {
              type: "LineString",
              coordinates: coordinates
            },
            properties: {}
          };
        }
      }

      if (typeof overlay.getCenter === 'function' && typeof overlay.getRadius === 'function') {
        // Circle - return as GeoJSON Point with radius property
        const center = overlay.getCenter();
        return {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [center.lng(), center.lat()]
          },
          properties: {
            radius: overlay.getRadius(),
            shape: "circle"
          }
        };
      }

      if (typeof overlay.getBounds === 'function') {
        // Rectangle - return as GeoJSON Polygon
        const bounds = overlay.getBounds();
        const ne = bounds.getNorthEast();
        const sw = bounds.getSouthWest();

        const coordinates = [
          [sw.lng(), sw.lat()], // Southwest
          [ne.lng(), sw.lat()], // Southeast
          [ne.lng(), ne.lat()], // Northeast
          [sw.lng(), ne.lat()], // Northwest
          [sw.lng(), sw.lat()]  // Close polygon
        ];

        return {
          type: "Feature",
          geometry: {
            type: "Polygon",
            coordinates: [coordinates]
          },
          properties: {
            shape: "rectangle",
            bounds: {
              north: ne.lat(),
              south: sw.lat(),
              east: ne.lng(),
              west: sw.lng()
            }
          }
        };
      }

      if (typeof overlay.getPosition === 'function') {
        // Marker - return as GeoJSON Point
        const position = overlay.getPosition();
        return {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [position.lng(), position.lat()]
          },
          properties: {}
        };
      }

      return null;
    } catch (error) {
      console.error('Error extracting geometry from overlay:', error);
      return null;
    }
  },

  serializeEventData(eventType, event, marker = null) {
    // Use the simple mapping approach
    const data = { eventType };

    try {
      const serializer = EVENT_SERIALIZERS[eventType];

      if (serializer) {
        // Use specific serializer - pass marker as third parameter for marker events
        const eventData = serializer(event, this.map, marker);
        Object.assign(data, eventData);
      } else {
        // Use generic fallback
        const eventData = genericSerialize(event);
        Object.assign(data, eventData);
        data._generic = true;
      }
    } catch (error) {
      data.error = error.message;
    }

    return data;
  },

  async handleMapUpdate(payload) {
    if (!this.mapInitialized || !this.map) {
      return;
    }

    // Check if this event is for our map
    if (payload.target && payload.target !== this.el.id) {
      return;
    }

    try {
      // Handle map options updates
      if (payload.mapOptions) {
        this.map.setOptions(payload.mapOptions);
      }

      // Handle event subscription updates
      if (payload.eventSubscriptions) {
        this.setupEventHandlers(payload.eventSubscriptions);
      }

      // Handle marker updates
      if (payload.markers) {
        await this.updateMarkers(payload.markers);
      }

      // Handle shape updates
      if (payload.shapes) {
        this.updateShapes(payload.shapes);
      }
    } catch (error) {
      this.pushEvent("map_error", {
        mapId: this.el.id,
        error: error.message
      });
    }
  },

  async updateMarkers(newMarkersData) {
    // Clear existing markers
    this.clearMarkers();

    // Create new markers
    if (newMarkersData && newMarkersData.length > 0) {
      await this.createMarkers(newMarkersData);
    }
  },

  updateShapes(newShapesData) {
    // Clear existing shapes
    this.clearShapes();

    // Create new shapes
    if (newShapesData && newShapesData.length > 0) {
      this.createShapes(newShapesData);
    }
  },

  updateShapeRadius(newRadius) {
    // Update radius for all circle shapes in place
    this.shapes.forEach((shape, shapeId) => {
      if (typeof shape.setRadius === 'function') {
        shape.setRadius(newRadius);
      }
    });
  },

  updateShape(shapeId, updates) {
    // Generic shape update function that can modify any shape properties
    if (!shapeId || !updates || typeof updates !== 'object') {
      console.warn('updateShape requires shapeId and updates object');
      return;
    }

    // Handle special case: update all shapes if shapeId is '*'
    if (shapeId === '*') {
      this.shapes.forEach((shape, id) => {
        this.applySingleShapeUpdate(shape, updates);
      });
      return;
    }

    // Update specific shape
    const shape = this.shapes.get(shapeId);
    if (!shape) {
      console.warn(`Shape with ID ${shapeId} not found`);
      return;
    }

    this.applySingleShapeUpdate(shape, updates);
  },

  applySingleShapeUpdate(shape, updates) {
    // Apply updates based on shape type and available methods
    Object.entries(updates).forEach(([property, value]) => {
      try {
        switch (property) {
          // Circle-specific properties
          case 'radius':
            if (typeof shape.setRadius === 'function') {
              shape.setRadius(value);
            }
            break;

          case 'center':
            if (typeof shape.setCenter === 'function') {
              shape.setCenter(value);
            }
            break;

          // Common properties for all shapes
          case 'visible':
            if (typeof shape.setVisible === 'function') {
              shape.setVisible(value);
            }
            break;

          case 'draggable':
            if (typeof shape.setDraggable === 'function') {
              shape.setDraggable(value);
            }
            break;

          case 'editable':
            if (typeof shape.setEditable === 'function') {
              shape.setEditable(value);
            }
            break;

          case 'clickable':
            if (typeof shape.setClickable === 'function') {
              shape.setClickable(value);
            }
            break;

          // Style properties
          case 'strokeColor':
            if (typeof shape.setOptions === 'function') {
              shape.setOptions({ strokeColor: value });
            }
            break;

          case 'strokeOpacity':
            if (typeof shape.setOptions === 'function') {
              shape.setOptions({ strokeOpacity: value });
            }
            break;

          case 'strokeWeight':
            if (typeof shape.setOptions === 'function') {
              shape.setOptions({ strokeWeight: value });
            }
            break;

          case 'fillColor':
            if (typeof shape.setOptions === 'function') {
              shape.setOptions({ fillColor: value });
            }
            break;

          case 'fillOpacity':
            if (typeof shape.setOptions === 'function') {
              shape.setOptions({ fillOpacity: value });
            }
            break;

          case 'zIndex':
            if (typeof shape.setOptions === 'function') {
              shape.setOptions({ zIndex: value });
            }
            break;

          // Polygon/Polyline specific
          case 'path':
            if (typeof shape.setPath === 'function') {
              shape.setPath(value);
            }
            break;

          case 'paths':
            if (typeof shape.setPaths === 'function') {
              shape.setPaths(value);
            }
            break;

          // Rectangle specific
          case 'bounds':
            if (typeof shape.setBounds === 'function') {
              shape.setBounds(value);
            }
            break;

          // Generic options update - fallback for any property
          case 'options':
            if (typeof shape.setOptions === 'function') {
              shape.setOptions(value);
            }
            break;

          default:
            // Try to call a setter method if it exists
            const setterName = `set${property.charAt(0).toUpperCase() + property.slice(1)}`;
            if (typeof shape[setterName] === 'function') {
              shape[setterName](value);
            } else {
              console.warn(`Unknown shape property: ${property}`);
            }
            break;
        }
      } catch (error) {
        console.error(`Error updating shape property ${property}:`, error);
      }
    });
  },



  cleanup() {
    // Remove all event listeners
    this.clearEventListeners();

    // Clear all markers
    this.clearMarkers();

    // Clear all shapes
    this.clearShapes();

    // Clean up map instance
    if (this.map) {
      this.map = null;
    }

    this.mapInitialized = false;
  },


  // Public API methods that can be called from LiveView
  getMap() {
    return this.map;
  },

  getLoadedLibraries() {
    // Return the Google Maps API object instead of tracking libraries
    return window.google?.maps || {};
  },

  isInitialized() {
    return this.mapInitialized;
  }
};

export default GoogleMapHook;
