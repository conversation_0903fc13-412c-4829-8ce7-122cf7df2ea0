/**
 * FeaturebaseHook - A Phoenix LiveView hook for initializing Featurebase widgets
 * 
 * This hook dynamically loads the Featurebase SDK and initializes widgets
 * based on data attributes passed from the LiveView component.
 * 
 * Usage in LiveView:
 * <div id="featurebase-widgets" 
 *      phx-hook="FeaturebaseHook"
 *      data-config={Jason.encode!(@featurebase_config)}>
 * </div>
 */
const FeaturebaseHook = {
  mounted() {
    this.loadFeaturebaseSDK().then(() => {
      this.initializeWidgets();
    });
  },

  updated() {
    // Re-initialize widgets if config changes
    this.initializeWidgets();
  },

  destroyed() {
    // Clean up if needed
  },

  loadFeaturebaseSDK() {
    return new Promise((resolve) => {
      // Check if SDK is already loaded
      if (window.Featurebase) {
        resolve();
        return;
      }

      // Check if script is already in DOM
      if (document.getElementById("featurebase-sdk")) {
        // Wait for it to load
        const checkInterval = setInterval(() => {
          if (window.Featurebase) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 100);
        return;
      }

      // Create and load the SDK script
      const script = document.createElement("script");
      script.id = "featurebase-sdk";
      script.src = "https://do.featurebase.app/js/sdk.js";

      // Set up Featurebase function before script loads
      window.Featurebase = function () {
        (window.Featurebase.q = window.Featurebase.q || []).push(arguments);
      };

      script.onload = () => {
        resolve();
      };

      document.head.appendChild(script);
    });
  },

  initializeWidgets() {
    try {
      const config = JSON.parse(this.el.dataset.config);

      if (!window.Featurebase || !config) {
        return;
      }

      // Initialize embed widget if config exists
      if (config.identify) {
        window.Featurebase("identify", config.identify);
      }

      console.log(config.messenger);

      if (config.messenger) {
        window.Featurebase("boot", config.messenger);
      }

      // Initialize feedback widget if config exists
      if (config.identify && config.feedback) {
        window.Featurebase("initialize_feedback_widget", config.feedback);
      }

      // Initialize changelog widget if config exists
      if (config.identify && config.changelog) {
        window.Featurebase("init_changelog_widget", config.changelog);
      }
    } catch (error) {
      console.error("Error initializing Featurebase widgets");
    }
  }
};

export default FeaturebaseHook;