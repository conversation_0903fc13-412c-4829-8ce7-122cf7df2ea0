import Resize<PERSON><PERSON><PERSON>eaHook from "./resize-textarea-hook";
import <PERSON><PERSON><PERSON>Hook from "./clear-flash-hook";
import ScrollTopHook from "./scroll-top-hook";
import ColorSchemeHook from "./color-scheme-hook";
import ClipboardHook from "./clipboard-hook";
import Tip<PERSON>Hook from "./tippy-hook";
import LocalTimeHook from "./local-time-hook";
import ComboBoxHook from "./combo-box-hook";
import FloatingHook from "./floating-hook";
import AutosaveIndicator from "./autosave-indicator-hook";
import NotificationBellHook from "./notification-bell-hook";
import ChartJsHook from "./chart-js-hook";
import FocusBySelectorHook from "./focus-by-selector-hook";
import MicrophoneHook from "./microphone-hook";
import EditorJsHook from "./editorjs-hook";
import AuroraHook from "./aurora-hook";
import GoogleMapHook from "./google-map-hook";
import FeaturebaseHook from "./featurebase-hook";
import <PERSON><PERSON><PERSON><PERSON>Hook from "./infinite-scroll-hook";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ook from "./dual-slider-hook";
import ScrollInto<PERSON>iewHook from "./scroll-into-view-hook";
import <PERSON>fettiHook from "./confetti-hook";

// Either add hooks here or create a new file for each like ExampleHook

export default {
  AuroraHook,
  ResizeTextareaHook,
  ClearFlashHook,
  ScrollTopHook,
  ColorSchemeHook,
  ClipboardHook,
  TippyHook,
  LocalTimeHook,
  ComboBoxHook,
  FloatingHook,
  AutosaveIndicator,
  NotificationBellHook,
  ChartJsHook,
  FocusBySelectorHook,
  MicrophoneHook,
  EditorJsHook,
  GoogleMapHook,
  FeaturebaseHook,
  InfiniteScrollHook,
  DualRangeSliderHook,
  ScrollIntoViewHook,
  ConfettiHook,
};