const InfiniteScrollHook = {
    page() {
      return this.el.dataset.page;
    },
    pushEventName() {
      return this.el.dataset.pushEvent;
    },
    loadMore(entries) {
      const pushEventName = this.pushEventName();
      const target = entries[0];
      if (target.isIntersecting && this.pending == this.page()) {
        this.pending = this.page() + 1;
        this.pushEvent(pushEventName, {});
      }
    },
    mounted() {
      this.pending = this.page();
      this.observer = new IntersectionObserver(
        (entries) => this.loadMore(entries),
        {
          root: null, // window by default
          rootMargin: "400px",
          threshold: 0.1,
        }
      );
      this.observer.observe(this.el);
    },
    destroyed() {
      this.observer.unobserve(this.el);
    },
    updated() {
      this.pending = this.page();
    },
  };
  
  export default InfiniteScrollHook;