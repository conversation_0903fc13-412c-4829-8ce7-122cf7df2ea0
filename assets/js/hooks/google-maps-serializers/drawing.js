// Drawing Library Event Serializers
// These handle events from the Google Maps Drawing Library

export const DrawingEventSerializers = {
  // === DrawingManager Events ===

  // Main drawing completion event
  overlaycomplete: (event) => {

    // Use the event type directly to determine geometry
    const geometry = generateGeometryFromEvent(event);

    return {
      overlayType: event.type,
      geometry: geometry,
      overlayId: generateOverlayId()
    };
  },

  // Drawing mode changed
  drawingmodechanged: (event, map, drawingManager) => ({
    drawingMode: drawingManager?.getDrawingMode() || null,
    previousMode: event.previousMode || null
  }),

  // === Overlay Events (for all drawable shapes) ===

  // Click events on overlays
  overlay_click: (event) => ({
    latLng: event.latLng ? {
      lat: event.latLng.lat(),
      lng: event.latLng.lng()
    } : null,
    overlay: serializeOverlay(event.overlay)
  }),

  overlay_dblclick: (event) => DrawingEventSerializers.overlay_click(event),
  overlay_rightclick: (event) => DrawingEventSerializers.overlay_click(event),
  overlay_mousedown: (event) => DrawingEventSerializers.overlay_click(event),
  overlay_mouseup: (event) => DrawingEventSerializers.overlay_click(event),
  overlay_mouseover: (event) => DrawingEventSerializers.overlay_click(event),
  overlay_mouseout: (event) => DrawingEventSerializers.overlay_click(event),

  // === Circle Events ===

  // Circle center changed
  center_changed: (event, map, circle) => ({
    center: circle?.getCenter() ? {
      lat: circle.getCenter().lat(),
      lng: circle.getCenter().lng()
    } : null,
    radius: circle?.getRadius() || null
  }),

  // Circle radius changed
  radius_changed: (event, map, circle) => ({
    radius: circle?.getRadius() || null,
    center: circle?.getCenter() ? {
      lat: circle.getCenter().lat(),
      lng: circle.getCenter().lng()
    } : null
  }),

  // === Polygon Events ===

  // Polygon path changed
  path_changed: (event, map, polygon) => {
    const path = polygon?.getPath();
    return {
      path: path ? path.getArray().map(latLng => ({
        lat: latLng.lat(),
        lng: latLng.lng()
      })) : []
    };
  },

  // Polygon paths changed (for polygons with holes)
  paths_changed: (event, map, polygon) => {
    const paths = polygon?.getPaths();
    return {
      paths: paths ? paths.getArray().map(path =>
        path.getArray().map(latLng => ({
          lat: latLng.lat(),
          lng: latLng.lng()
        }))
      ) : []
    };
  },

  // === Polyline Events ===

  // Polyline path changed (same as polygon path_changed but for polylines)
  polyline_path_changed: (event, map, polyline) => {
    const path = polyline?.getPath();
    return {
      path: path ? path.getArray().map(latLng => ({
        lat: latLng.lat(),
        lng: latLng.lng()
      })) : []
    };
  },

  // === Rectangle Events ===

  // Rectangle bounds changed
  bounds_changed: (event, map, rectangle) => {
    const bounds = rectangle?.getBounds();
    return bounds ? {
      bounds: {
        north: bounds.getNorthEast().lat(),
        south: bounds.getSouthWest().lat(),
        east: bounds.getNorthEast().lng(),
        west: bounds.getSouthWest().lng()
      }
    } : {};
  },

  // === Marker Events ===

  // Marker position changed
  position_changed: (event, map, marker) => ({
    position: marker?.getPosition() ? {
      lat: marker.getPosition().lat(),
      lng: marker.getPosition().lng()
    } : null
  }),

  // Marker drag events
  dragstart: (event, map, marker) => ({
    position: marker?.getPosition() ? {
      lat: marker.getPosition().lat(),
      lng: marker.getPosition().lng()
    } : null
  }),

  drag: (event, map, marker) => DrawingEventSerializers.dragstart(event, map, marker),
  dragend: (event, map, marker) => DrawingEventSerializers.dragstart(event, map, marker),

  // === Generic Overlay Events ===

  // Overlay added to map
  overlay_added: (event) => ({
    overlay: serializeOverlay(event.overlay)
  }),

  // Overlay removed from map
  overlay_removed: (event) => ({
    overlay: serializeOverlay(event.overlay)
  }),

  // Overlay visibility changed
  visible_changed: (event, map, overlay) => ({
    visible: overlay?.getVisible() || false,
    overlay: serializeOverlay(overlay)
  }),

  // Overlay z-index changed
  zindex_changed: (event, map, overlay) => ({
    zIndex: overlay?.getZIndex() || null,
    overlay: serializeOverlay(overlay)
  })
};

// Serialize different types of drawing overlays (kept for compatibility with other events)
function serializeOverlay(overlay) {
  if (!overlay) return null;

  // This function is kept for other drawing events that might need it
  // For overlaycomplete, we use generateGeometryFromEvent instead
  return {
    type: 'overlay',
    hasOverlay: true,
    constructorName: overlay.constructor?.name
  };
}

// Generate geometry directly from the drawing event
function generateGeometryFromEvent(event) {
  if (!event || !event.overlay) return null;

  const overlay = event.overlay;

  // Use the overlay type directly from the event
  switch (event.type) {
    case google.maps.drawing.OverlayType.POLYGON:
      return extractPolygonFromOverlay(overlay);
    case google.maps.drawing.OverlayType.CIRCLE:
      return extractCircleFromOverlay(overlay);
    case google.maps.drawing.OverlayType.RECTANGLE:
      return extractRectangleFromOverlay(overlay);
    case google.maps.drawing.OverlayType.POLYLINE:
      return extractPolylineFromOverlay(overlay);
    case google.maps.drawing.OverlayType.MARKER:
      return extractPointFromOverlay(overlay);
    default:
      console.error('Unknown overlay type in drawing event:', event.type);
      return null;
  }
}

function extractPolygonFromOverlay(overlay) {
  const path = overlay.getPath();
  const coordinates = [];

  for (let i = 0; i < path.getLength(); i++) {
    const point = path.getAt(i);
    coordinates.push([point.lng(), point.lat()]);
  }

  // Close the polygon if not already closed
  if (coordinates.length > 0 &&
    (coordinates[0][0] !== coordinates[coordinates.length - 1][0] ||
      coordinates[0][1] !== coordinates[coordinates.length - 1][1])) {
    coordinates.push(coordinates[0]);
  }

  // Return GeoJSON Polygon
  return {
    type: "Feature",
    geometry: {
      type: "Polygon",
      coordinates: [coordinates]
    },
    properties: {}
  };
}

function extractCircleFromOverlay(overlay) {
  const center = overlay.getCenter();
  const radius = overlay.getRadius();

  // Return GeoJSON Point with circle properties
  // Note: GeoJSON doesn't have a native Circle type, so we use Point with radius property
  return {
    type: "Feature",
    geometry: {
      type: "Point",
      coordinates: [center.lng(), center.lat()]
    },
    properties: {
      radius: radius,
      shape: "circle"
    }
  };
}

function extractRectangleFromOverlay(overlay) {
  const bounds = overlay.getBounds();
  const ne = bounds.getNorthEast();
  const sw = bounds.getSouthWest();

  // Convert rectangle to GeoJSON Polygon
  const coordinates = [
    [sw.lng(), sw.lat()], // Southwest
    [ne.lng(), sw.lat()], // Southeast
    [ne.lng(), ne.lat()], // Northeast
    [sw.lng(), ne.lat()], // Northwest
    [sw.lng(), sw.lat()]  // Close polygon
  ];

  return {
    type: "Feature",
    geometry: {
      type: "Polygon",
      coordinates: [coordinates]
    },
    properties: {
      shape: "rectangle",
      bounds: {
        north: ne.lat(),
        south: sw.lat(),
        east: ne.lng(),
        west: sw.lng()
      }
    }
  };
}

function extractPolylineFromOverlay(overlay) {
  const path = overlay.getPath();
  const coordinates = [];

  for (let i = 0; i < path.getLength(); i++) {
    const point = path.getAt(i);
    coordinates.push([point.lng(), point.lat()]);
  }

  // Return GeoJSON LineString
  return {
    type: "Feature",
    geometry: {
      type: "LineString",
      coordinates: coordinates
    },
    properties: {}
  };
}

function extractPointFromOverlay(overlay) {
  const position = overlay.getPosition();

  // Return GeoJSON Point
  return {
    type: "Feature",
    geometry: {
      type: "Point",
      coordinates: [position.lng(), position.lat()]
    },
    properties: {}
  };
}

function generateOverlayId() {
  return 'overlay_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// Export the serialization function for external use
export { serializeOverlay, generateGeometryFromEvent };
