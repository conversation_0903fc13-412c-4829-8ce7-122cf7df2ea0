// Core Google Maps Event Serializers
// These handle the basic map events that are always available

export const CoreEventSerializers = {
  // Click-based events
  click: (event) => ({
    position: event?.latLng ? { lat: event.latLng.lat(), lng: event.latLng.lng() } : null,
    pixel: event?.pixel ? { x: event.pixel.x, y: event.pixel.y } : null
  }),

  dblclick: (event) => CoreEventSerializers.click(event),
  rightclick: (event) => CoreEventSerializers.click(event),
  mouseover: (event) => CoreEventSerializers.click(event),
  mouseout: (event) => CoreEventSerializers.click(event),

  // State change events (query map directly since event object is usually empty)
  zoom_changed: (event, map) => ({
    zoom: map.getZoom()
  }),

  center_changed: (event, map) => {
    const center = map.getCenter();
    return center ? {
      center: { lat: center.lat(), lng: center.lng() }
    } : {};
  },

  bounds_changed: (event, map) => {
    const bounds = map.getBounds();
    return bounds ? {
      bounds: {
        north: bounds.getNorthEast().lat(),
        south: bounds.getSouthWest().lat(),
        east: bounds.getNorthEast().lng(),
        west: bounds.getSouthWest().lng()
      }
    } : {};
  },

  maptypeid_changed: (event, map) => ({
    mapTypeId: map.getMapTypeId()
  }),

  // Drag events
  dragstart: (event) => CoreEventSerializers.click(event),
  drag: (event) => CoreEventSerializers.click(event),
  dragend: (event) => CoreEventSerializers.click(event),

  // Idle event (fired when map becomes idle after panning or zooming)
  idle: (event, map) => ({
    center: map.getCenter() ? {
      lat: map.getCenter().lat(),
      lng: map.getCenter().lng()
    } : null,
    zoom: map.getZoom(),
    bounds: map.getBounds() ? {
      north: map.getBounds().getNorthEast().lat(),
      south: map.getBounds().getSouthWest().lat(),
      east: map.getBounds().getNorthEast().lng(),
      west: map.getBounds().getSouthWest().lng()
    } : null
  }),

  // === Additional Core Events ===

  // Heading changed (for Street View)
  heading_changed: (event, map) => ({
    heading: map.getHeading ? map.getHeading() : null
  }),

  // Tilt changed
  tilt_changed: (event, map) => ({
    tilt: map.getTilt ? map.getTilt() : null
  }),

  // === KML Layer Events ===

  // KML layer status changed
  status_changed: (event, map, kmlLayer) => ({
    status: kmlLayer?.getStatus() || null
  }),

  // === Info Window Events ===

  // Info window position changed
  infowindow_position_changed: (event, map, infoWindow) => {
    const position = infoWindow?.getPosition();
    return {
      position: position ? {
        lat: position.lat(),
        lng: position.lng()
      } : null
    };
  },

  // Info window z-index changed
  infowindow_zindex_changed: (event, map, infoWindow) => ({
    zIndex: infoWindow?.getZIndex() || null
  })
};

// Generic fallback serializer for unknown events
export const genericSerialize = (event) => {
  const data = {};

  if (!event) return data;

  // Extract latLng if available
  if (event.latLng && typeof event.latLng.lat === 'function') {
    data.latLng = {
      lat: event.latLng.lat(),
      lng: event.latLng.lng()
    };
  }

  // Extract pixel coordinates if available
  if (event.pixel) {
    data.pixel = {
      x: event.pixel.x,
      y: event.pixel.y
    };
  }

  // Copy primitive values
  Object.keys(event).forEach(key => {
    const value = event[key];
    if (['string', 'number', 'boolean'].includes(typeof value)) {
      data[key] = value;
    }
  });

  return data;
};
