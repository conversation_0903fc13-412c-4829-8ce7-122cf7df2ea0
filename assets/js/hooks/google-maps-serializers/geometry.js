// Geometry Library Event Serializers
// These handle events from the Google Maps Geometry Library

export const GeometryEventSerializers = {
  // Geometry calculation results
  distance_calculated: (event, map, result) => ({
    distance: result?.distance || null,
    duration: result?.duration || null,
    origin: result?.origin ? {
      lat: result.origin.lat(),
      lng: result.origin.lng()
    } : null,
    destination: result?.destination ? {
      lat: result.destination.lat(),
      lng: result.destination.lng()
    } : null
  }),
  
  // Area calculation
  area_calculated: (event, map, polygon, area) => ({
    area: area,
    unit: 'square_meters', // Default unit
    polygon_path: polygon?.getPath() ? polygon.getPath().getArray().map(latLng => ({
      lat: latLng.lat(),
      lng: latLng.lng()
    })) : []
  }),
  
  // Length calculation (for polylines)
  length_calculated: (event, map, polyline, length) => ({
    length: length,
    unit: 'meters', // Default unit
    polyline_path: polyline?.getPath() ? polyline.getPath().getArray().map(latLng => ({
      lat: latLng.lat(),
      lng: latLng.lng()
    })) : []
  }),
  
  // Point in polygon test result
  point_in_polygon_result: (event, map, point, polygon, isInside) => ({
    point: point ? {
      lat: point.lat(),
      lng: point.lng()
    } : null,
    is_inside: isInside,
    polygon_path: polygon?.getPath() ? polygon.getPath().getArray().map(latLng => ({
      lat: latLng.lat(),
      lng: latLng.lng()
    })) : []
  }),
  
  // Bearing calculation
  bearing_calculated: (event, map, from, to, bearing) => ({
    from: from ? {
      lat: from.lat(),
      lng: from.lng()
    } : null,
    to: to ? {
      lat: to.lat(),
      lng: to.lng()
    } : null,
    bearing: bearing,
    unit: 'degrees'
  }),
  
  // Interpolation result
  interpolation_result: (event, map, from, to, fraction, result) => ({
    from: from ? {
      lat: from.lat(),
      lng: from.lng()
    } : null,
    to: to ? {
      lat: to.lat(),
      lng: to.lng()
    } : null,
    fraction: fraction,
    result: result ? {
      lat: result.lat(),
      lng: result.lng()
    } : null
  })
};

// Helper functions for geometry calculations that might be useful

// Calculate distance between two points using the geometry library
export function calculateDistance(point1, point2) {
  if (!window.google?.maps?.geometry?.spherical) {
    throw new Error('Geometry library not loaded');
  }
  
  return window.google.maps.geometry.spherical.computeDistanceBetween(point1, point2);
}

// Calculate area of a polygon
export function calculateArea(polygon) {
  if (!window.google?.maps?.geometry?.spherical) {
    throw new Error('Geometry library not loaded');
  }
  
  const path = polygon.getPath();
  return window.google.maps.geometry.spherical.computeArea(path);
}

// Calculate length of a polyline
export function calculateLength(polyline) {
  if (!window.google?.maps?.geometry?.spherical) {
    throw new Error('Geometry library not loaded');
  }
  
  const path = polyline.getPath();
  return window.google.maps.geometry.spherical.computeLength(path);
}

// Check if a point is inside a polygon
export function isPointInPolygon(point, polygon) {
  if (!window.google?.maps?.geometry?.poly) {
    throw new Error('Geometry library not loaded');
  }
  
  return window.google.maps.geometry.poly.containsLocation(point, polygon);
}

// Calculate bearing between two points
export function calculateBearing(from, to) {
  if (!window.google?.maps?.geometry?.spherical) {
    throw new Error('Geometry library not loaded');
  }
  
  return window.google.maps.geometry.spherical.computeHeading(from, to);
}

// Interpolate between two points
export function interpolate(from, to, fraction) {
  if (!window.google?.maps?.geometry?.spherical) {
    throw new Error('Geometry library not loaded');
  }
  
  return window.google.maps.geometry.spherical.interpolate(from, to, fraction);
}
