// Places Library Event Serializers
// These handle events from the Google Maps Places Library

export const PlacesEventSerializers = {
  // === Autocomplete Events ===

  // Place changed event (from Autocomplete)
  place_changed: (event, map, autocomplete) => {
    const place = autocomplete?.getPlace();
    return place ? serializePlace(place) : {};
  },

  // === PlacesService Events ===

  // Nearby search completed
  places_nearby_search: (results, status) => ({
    status: status,
    resultsCount: results?.length || 0,
    results: results ? results.slice(0, 5).map(place => serializePlaceSummary(place)) : []
  }),

  // Text search completed
  places_text_search: (results, status) => ({
    status: status,
    resultsCount: results?.length || 0,
    results: results ? results.slice(0, 5).map(place => serializePlaceSummary(place)) : []
  }),

  // Radar search completed
  places_radar_search: (results, status) => ({
    status: status,
    resultsCount: results?.length || 0,
    results: results ? results.slice(0, 5).map(place => serializePlaceSummary(place)) : []
  }),

  // Place details loaded
  place_details_loaded: (event, map, place, status) => ({
    status: status,
    place: place ? serializePlace(place) : null
  }),

  // === SearchBox Events ===

  // SearchBox places changed
  places_changed: (event, map, searchBox) => {
    const places = searchBox?.getPlaces();
    return {
      placesCount: places?.length || 0,
      places: places ? places.slice(0, 5).map(place => serializePlaceSummary(place)) : []
    };
  },

  // === PlaceResult Events ===

  // Place result click
  place_result_click: (event, map, place) => ({
    place: place ? serializePlaceSummary(place) : null,
    latLng: event.latLng ? {
      lat: event.latLng.lat(),
      lng: event.latLng.lng()
    } : null
  }),

  // === Street View Events (part of Places) ===

  // Street View panorama changed
  pano_changed: (event, map, streetViewPanorama) => ({
    pano: streetViewPanorama?.getPano() || null
  }),

  // Street View position changed
  position_changed: (event, map, streetViewPanorama) => {
    const position = streetViewPanorama?.getPosition();
    return {
      position: position ? {
        lat: position.lat(),
        lng: position.lng()
      } : null
    };
  },

  // Street View POV changed
  pov_changed: (event, map, streetViewPanorama) => {
    const pov = streetViewPanorama?.getPov();
    return {
      pov: pov ? {
        heading: pov.heading,
        pitch: pov.pitch,
        zoom: pov.zoom
      } : null
    };
  },

  // Street View visibility changed
  visible_changed: (event, map, streetViewPanorama) => ({
    visible: streetViewPanorama?.getVisible() || false
  }),

  // === Geocoder Events ===

  // Geocoding completed
  geocode_complete: (event, map, geocoder, results, status) => ({
    status: status,
    resultsCount: results?.length || 0,
    results: results ? results.slice(0, 5).map(result => ({
      formatted_address: result.formatted_address,
      place_id: result.place_id,
      types: result.types,
      geometry: result.geometry ? {
        location: {
          lat: result.geometry.location.lat(),
          lng: result.geometry.location.lng()
        },
        location_type: result.geometry.location_type
      } : null
    })) : []
  }),

  // Reverse geocoding completed
  reverse_geocode_complete: (event, map, geocoder, results, status) =>
    PlacesEventSerializers.geocode_complete(event, map, geocoder, results, status)
};

// Serialize a Google Maps Place object
function serializePlace(place) {
  if (!place) return null;

  const serialized = {
    place_id: place.place_id,
    name: place.name,
    formatted_address: place.formatted_address,
    types: place.types || [],
    rating: place.rating,
    price_level: place.price_level,
    user_ratings_total: place.user_ratings_total
  };

  // Add geometry if available
  if (place.geometry) {
    if (place.geometry.location) {
      serialized.location = {
        lat: place.geometry.location.lat(),
        lng: place.geometry.location.lng()
      };
    }

    if (place.geometry.viewport) {
      serialized.viewport = {
        north: place.geometry.viewport.getNorthEast().lat(),
        south: place.geometry.viewport.getSouthWest().lat(),
        east: place.geometry.viewport.getNorthEast().lng(),
        west: place.geometry.viewport.getSouthWest().lng()
      };
    }
  }

  // Add address components if available
  if (place.address_components) {
    serialized.address_components = place.address_components.map(component => ({
      long_name: component.long_name,
      short_name: component.short_name,
      types: component.types
    }));
  }

  // Add photos info (but not the actual photo data)
  if (place.photos && place.photos.length > 0) {
    serialized.photos_count = place.photos.length;
    serialized.photos = place.photos.map(photo => ({
      height: photo.height,
      width: photo.width,
      html_attributions: photo.html_attributions
      // Note: We don't include getUrl() here as it requires parameters
      // The LiveView can request photo URLs separately if needed
    }));
  }

  // Add opening hours if available
  if (place.opening_hours) {
    serialized.opening_hours = {
      open_now: place.opening_hours.open_now,
      periods: place.opening_hours.periods,
      weekday_text: place.opening_hours.weekday_text
    };
  }

  // Add reviews count (but not full reviews to keep payload small)
  if (place.reviews) {
    serialized.reviews_count = place.reviews.length;
    // Could add summary of reviews if needed
  }

  // Add contact info
  if (place.formatted_phone_number) {
    serialized.phone_number = place.formatted_phone_number;
  }

  if (place.international_phone_number) {
    serialized.international_phone_number = place.international_phone_number;
  }

  if (place.website) {
    serialized.website = place.website;
  }

  return serialized;
}

// Serialize a place summary (lighter version for lists)
function serializePlaceSummary(place) {
  if (!place) return null;

  const summary = {
    place_id: place.place_id,
    name: place.name,
    formatted_address: place.formatted_address,
    types: place.types || [],
    rating: place.rating,
    price_level: place.price_level
  };

  // Add basic geometry
  if (place.geometry && place.geometry.location) {
    summary.location = {
      lat: place.geometry.location.lat(),
      lng: place.geometry.location.lng()
    };
  }

  return summary;
}

// Export the serialization functions for external use
export { serializePlace, serializePlaceSummary };
