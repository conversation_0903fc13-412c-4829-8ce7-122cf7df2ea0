// Marker Library Event Serializers
// These handle events from Google Maps AdvancedMarker

export const MarkerEventSerializers = {
  // Marker click events (both regular and AdvancedMarker)
  marker_click: (event, map, marker) => ({
    markerId: marker?.id || null,
    position: marker?.position ? {
      lat: marker.position.lat,
      lng: marker.position.lng
    } : null,
    title: marker?.title || null
  }),

  // AdvancedMarker click event (gmp-click)
  'gmp-click': (event, map, marker) => MarkerEventSerializers.marker_click(event, map, marker),

  // Marker drag events
  dragstart: (event, map, marker) => ({
    markerId: marker?.id || null,
    position: marker?.position ? {
      lat: marker.position.lat,
      lng: marker.position.lng
    } : null,
    title: marker?.title || null
  }),

  drag: (event, map, marker) => MarkerEventSerializers.dragstart(event, map, marker),

  dragend: (event, map, marker) => MarkerEventSerializers.dragstart(event, map, marker),

  // Marker position changed
  position_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    position: marker?.position ? {
      lat: marker.position.lat,
      lng: marker.position.lng
    } : null,
    title: marker?.title || null
  }),

  // Marker animation changed
  animation_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    animation: marker?.animation || null
  }),

  // Marker cursor changed
  cursor_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    cursor: marker?.cursor || null
  }),

  // Marker draggable changed
  draggable_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    draggable: marker?.draggable || false
  }),

  // Marker flat changed (for 3D markers)
  flat_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    flat: marker?.flat || false
  }),

  // Marker icon changed
  icon_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    icon: marker?.icon || null
  }),

  // Marker map changed (when marker is added/removed from map)
  map_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    hasMap: !!marker?.map
  }),

  // Marker shape changed (for click detection)
  shape_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    shape: marker?.shape || null
  }),

  // Marker title changed
  title_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    title: marker?.title || null
  }),

  // Marker visible changed
  visible_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    visible: marker?.visible !== false
  }),

  // Marker z-index changed
  zindex_changed: (event, map, marker) => ({
    markerId: marker?.id || null,
    zIndex: marker?.zIndex || null
  }),

  // Mouse events on markers
  mouseover: (event, map, marker) => ({
    markerId: marker?.id || null,
    position: marker?.position ? {
      lat: marker.position.lat,
      lng: marker.position.lng
    } : null
  }),

  mouseout: (event, map, marker) => MarkerEventSerializers.mouseover(event, map, marker),
  mouseup: (event, map, marker) => MarkerEventSerializers.mouseover(event, map, marker),
  mousedown: (event, map, marker) => MarkerEventSerializers.mouseover(event, map, marker),

  // Double click on marker
  marker_dblclick: (event, map, marker) => MarkerEventSerializers.marker_click(event, map, marker),

  // Right click on marker
  marker_rightclick: (event, map, marker) => MarkerEventSerializers.marker_click(event, map, marker)
};
