/**
 * ScrollIntoView Hook
 * 
 * A generic hook for scrolling elements into view based on Phoenix LiveView events.
 * 
 * Usage:
 * 1. Add the hook to an element: <div phx-hook="ScrollIntoView">
 * 2. Send an event from LiveView: push_event("scroll_into_view", %{target_id: "element-id"})
 * 
 * Events:
 * - scroll_into_view: Scrolls the specified element into view
 *   - target_id: The ID of the element to scroll into view
 *   - behavior: "smooth" (default) or "instant"
 *   - block: "start", "center", "end", or "nearest" (default: "center")
 *   - inline: "start", "center", "end", or "nearest" (default: "nearest")
 */

export default {
  mounted() {
    // Listen for scroll_into_view events
    this.handleEvent("scroll_into_view", (payload) => {
      this.scrollElementIntoView(payload);
    });
  },

  scrollElementIntoView(payload) {
    const { target_id, behavior = "smooth", block = "center", inline = "nearest" } = payload;

    if (!target_id) {
      console.warn("ScrollIntoView: target_id is required");
      return;
    }

    const targetElement = document.getElementById(target_id);

    if (!targetElement) {
      console.warn(`ScrollIntoView: Element with ID '${target_id}' not found`);
      return;
    }

    // Use the native scrollIntoView API
    targetElement.scrollIntoView({
      behavior: behavior,
      block: block,
      inline: inline
    });

    // Optional: Add a temporary highlight effect
    this.addTemporaryHighlight(targetElement);
  },

  addTemporaryHighlight(element) {
    // Add a subtle flash effect to indicate the scrolled element
    const originalTransition = element.style.transition;
    const originalBoxShadow = element.style.boxShadow;

    element.style.transition = "box-shadow 0.3s ease";
    element.style.boxShadow = "0 0 0 2px rgba(59, 130, 246, 0.5)";

    setTimeout(() => {
      element.style.boxShadow = originalBoxShadow;
      setTimeout(() => {
        element.style.transition = originalTransition;
      }, 300);
    }, 600);
  }
};