// This will wait for a window level variable (eg. window.foo) to be defined before executing the callback.
// Good for waiting for an async library to be loaded and unsure when it'll finish.
// Tries for 10 seconds before giving up
export default function whenAvailable(name, callback, attemptsRemaining = 500) {
  var interval = 20; // ms
  window.setTimeout(function () {
    if (window[name]) {
      callback(window[name]);
    } else {
      whenAvailable(name, callback, attemptsRemaining - 1);
    }
  }, interval);
}