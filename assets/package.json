{"name": "myapp_assets", "version": "0.1.0", "private": true, "scripts": {"build": "node esbuild.js", "deploy": "node esbuild.js --deploy", "watch": "node esbuild.js --watch"}, "dependencies": {"@alpinejs/collapse": "^3.14.8", "@alpinejs/persist": "^3.13.10", "@editorjs/code": "^2.9.2", "@editorjs/delimiter": "^1.4.2", "@editorjs/editorjs": "^2.30.6", "@editorjs/embed": "^2.7.6", "@editorjs/header": "^2.8.7", "@editorjs/inline-code": "^1.5.1", "@editorjs/list": "^1.10.0", "@editorjs/marker": "^1.4.0", "@editorjs/quote": "^2.7.2", "@editorjs/simple-image": "^1.6.0", "@editorjs/table": "^2.4.1", "@editorjs/warning": "^1.4.0", "@floating-ui/dom": "^1.6.5", "@googlemaps/js-api-loader": "^1.16.8", "@sentry/browser": "^9.1.0", "alpinejs": "^3.13.10", "chart.js": "^4.4.3", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-autocolors": "^0.2.2", "luxon": "^3.4.4", "phoenix": "file:../deps/phoenix", "phoenix_html": "file:../deps/phoenix_html", "phoenix_live_view": "file:../deps/phoenix_live_view", "tippy.js": "^6.3.7", "tom-select": "^2.3.1"}, "devDependencies": {"esbuild": "^0.25"}}