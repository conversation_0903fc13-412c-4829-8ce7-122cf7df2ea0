/* Custom Range Slider Component */
.slider-input {
    @apply appearance-none pointer-events-none absolute h-0 w-full outline-hidden z-10 bg-transparent;
}

.slider-input::-webkit-slider-thumb {
    @apply appearance-none w-5 h-5 bg-primary-500 rounded-full border-0 pointer-events-auto cursor-pointer shadow-sm relative z-20 translate-y-0.5;
}

.slider-input::-moz-range-thumb {
    @apply w-5 h-5 bg-primary-500 rounded-full border-0 pointer-events-auto cursor-pointer shadow-sm relative z-20 translate-y-0.5;
}

.slider-track {
    @apply absolute w-full h-1 bg-gray-200 rounded-sm z-0;
}

.slider-range {
    @apply absolute h-1 bg-primary-500 rounded-sm z-0;
}