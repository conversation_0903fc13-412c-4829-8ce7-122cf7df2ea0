# Google Places Field Mapping

This document provides a comprehensive mapping between Google Places API fields, Apify scraper fields, and the `Boring.Google.Place` Ash resource attributes.

## Field Mapping Table

| **Google Places API Field** | **API Struct Field** | **Boring.Google.Place Field** | **Apify Equivalent** | **Transformation Notes** |
|----------------------------|---------------------|------------------------------|---------------------|-------------------------|
| `places.id` | `id` | `place_id` | `placeId` | Direct mapping |
| `places.displayName` | `display_name.text` | `business_name` | `title` | Extract text from nested struct |
| `places.formattedAddress` | `formatted_address` | `full_address` | `address` | Direct mapping |
| `places.location.latitude` | `location.latitude` | `latitude` | `location.lat` | Extract from nested struct |
| `places.location.longitude` | `location.longitude` | `longitude` | `location.lng` | Extract from nested struct |
| `places.addressComponents` | `city` (parsed) | `city` | `city` | Parsed from address components |
| `places.addressComponents` | `state` (parsed) | `google_state` | `state` | Parsed from address components |
| `places.addressComponents` | `postal_code` (parsed) | `postal_code` | `postalCode` | Parsed from address components |
| `places.addressComponents` | `country` (parsed) | `google_country` | `countryCode` | Parsed from address components |
| `places.addressComponents` | `street` (parsed) | `street` | `street` | Parsed from address components |
| `places.rating` | `rating` | `review_score` | `totalScore` | Direct mapping |
| `places.userRatingCount` | *(need to add parsing)* | `review_count` | `reviewsCount` | **Missing from API parsing** |
| `places.types` | `types` | `categories` | `categories` | Direct mapping |
| `places.primaryType` | *(need to add parsing)* | *(could be first category)* | *(not available)* | **Missing from API parsing** |
| `places.primaryTypeDisplayName` | *(need to add parsing)* | *(additional_info?)* | *(not available)* | **Missing from API parsing** |
| `places.nationalPhoneNumber` | `national_phone_number` | `phone_number` | `phone` | Direct mapping |
| `places.photos` | `photos` | `image_urls` | `imageUrls` | Transform photo refs to URLs |
| `places.websiteURI` | `website_uri` | `website` | `website` | Direct mapping |
| `places.viewport` | `viewport` | *(not stored)* | *(not available)* | Not needed for database |
| `places.openingHours` | `opening_hours` | `opening_hours` | `openingHours` | Transform structure |
| `places.businessStatus` | `business_status` | `permanently_closed`/`temporarily_closed` | `permanentlyClosed`/`temporarilyClosed` | Transform string to booleans |
| *(not available)* | *(not available)* | `customer_id` | `cid` | Apify only |
| *(not available)* | *(not available)* | `maps_url` | `url` | Apify only |
| *(not available)* | *(not available)* | `description` | `description` | Apify only |
| *(not available)* | *(not available)* | `result_rank` | `rank` | Apify only |
| *(not available)* | *(not available)* | `review_distribution` | `reviewsDistribution` | Apify only |
| *(not available)* | *(not available)* | `review_tags` | `reviewsTags` | Apify only |
| *(not available)* | *(not available)* | `additional_info` | `additionalInfo` | Apify only |
| *(not available)* | *(not available)* | `is_business_claimed` | `!claimThisBusiness` | Apify only (inverted boolean) |

## Default Fields from Google Places API

The following fields are requested by default from the Google Places API (defined in `lib/boring/api/google/places.ex`):

```elixir
default_fields = [
  # Essentials tier - free/low cost fields
  "places.id",
  "places.displayName",
  "places.formattedAddress",
  "places.location",
  "places.addressComponents",
  "places.rating",
  "places.userRatingCount",
  "places.types",
  "places.primaryType",
  "places.primaryTypeDisplayName",
  "places.nationalPhoneNumber",
  "places.photos",
  "places.websiteURI",
  "places.viewport",
  "places.openingHours",
  "places.businessStatus"
]
```

## Missing API Parsing

The following fields are requested from the API but not currently parsed in the response:

1. **`places.userRatingCount`** → should map to `review_count`
2. **`places.primaryType`** → could be used for `categories[0]`
3. **`places.primaryTypeDisplayName`** → could be stored in `additional_info`

## Business Status Transformation

The `places.businessStatus` field needs special handling:

- **API Values**: `"OPERATIONAL"`, `"CLOSED_TEMPORARILY"`, `"CLOSED_PERMANENTLY"`
- **Database Fields**: 
  - `permanently_closed: boolean`
  - `temporarily_closed: boolean`

**Transformation Logic**:
```elixir
case business_status do
  "CLOSED_PERMANENTLY" -> %{permanently_closed: true, temporarily_closed: false}
  "CLOSED_TEMPORARILY" -> %{permanently_closed: false, temporarily_closed: true}
  _ -> %{permanently_closed: false, temporarily_closed: false}
end
```

## Apify-Specific Fields

The following fields are only available from the Apify scraper and not from the Google Places API:

- `customer_id` (from `cid`)
- `maps_url` (from `url`)
- `description`
- `result_rank` (from `rank`)
- `review_distribution` (from `reviewsDistribution`)
- `review_tags` (from `reviewsTags`)
- `additional_info` (from `additionalInfo`)
- `is_business_claimed` (from `!claimThisBusiness` - inverted boolean)

## Key Transformation Patterns

1. **Nested Struct Access**: 
   - `display_name.text` → `business_name`
   - `location.latitude/longitude` → `latitude/longitude`

2. **Address Component Parsing**: 
   - Extract city, state, postal_code, country, street from `addressComponents` array

3. **Photo Processing**: 
   - Convert photo references to actual image URLs

4. **Case Conversion**: 
   - Apify uses `Recase.to_snake/1` for nested objects

5. **Boolean Inversion**: 
   - `claimThisBusiness` → `!claimThisBusiness` for `is_business_claimed`

## Usage in PlaceTransformer

This mapping serves as the foundation for the `Boring.Google.PlaceTransformer` module, which standardizes data structures throughout the application and simplifies database upsert operations.
