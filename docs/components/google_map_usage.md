# Google Maps Component Usage Guide

The `PetalProWeb.Components.GoogleMap` component provides a flexible and powerful interface to the Google Maps SDK with full library support, event forwarding, and advanced features for building interactive mapping applications.

## Interactive Circle Placement Example

The component includes a complete example demonstrating interactive circle placement with dynamic radius controls. Key features include:

- **Click-to-Place**: Users click on the map to place circles at specific locations
- **Dynamic Radius Selection**: Radio buttons for 1, 3, 5, and 10-mile search radii
- **Real-time Updates**: Changing the radius selection updates all existing circles instantly
- **Placement Mode Toggle**: Visual feedback when in placement mode
- **Event Normalization**: Proper event handling through `handle_info` patterns

### Live Demo

Visit `/dev/components/google-map` to see the interactive circle placement example in action.

## Features

- **Dynamic Library Loading**: Load any Google Maps libraries (maps, places, drawing, geometry, etc.)
- **Full SDK Access**: Complete access to Google Maps SDK options and controls
- **Event Forwarding**: All Google Maps events automatically forwarded to your LiveView
- **Assign-Driven**: All configuration controlled through LiveView assigns
- **Performance Optimized**: Uses `phx-ignore` to prevent unnecessary re-renders
- **Error Handling**: Graceful error handling with user-friendly error messages
- **Advanced Markers**: Support for Google Maps AdvancedMarkerElement with custom content
- **Drawing Tools**: Built-in support for drawing circles, polygons, and other shapes
- **Geocoding**: Built-in geocoding functionality for location search
- **Reactive Configuration**: Dynamic library configuration based on LiveView state

## Basic Usage

```elixir
<.live_component
  module={PetalProWeb.Components.GoogleMap}
  id="my-map"
  map_options={%{
    center: %{lat: 37.7749, lng: -122.4194},
    zoom: 12,
    mapTypeId: "roadmap"
  }}
  libraries={%{
    maps: %{
      eventSubscriptions: ["click", "zoom_changed"]
    }
  }}
  class="w-full h-96"
/>
```

## Component Attributes

### Required
- `id` - Unique identifier for the map

### Optional
- `api_key` - Google Maps API key (defaults to `GOOGLE_MAPS_API_KEY` env var)
- `class` - CSS classes for the map container
- `style` - Inline styles for the map container
- `height` - Map height (default: "100%")
- `width` - Map width (default: "100%")

### Google Maps Configuration
- `libraries` - Map of Google Maps libraries and their configurations
- `loader_options` - Additional Google Maps Loader options
- `map_options` - Google Maps map options
- `map_controls` - Google Maps control options
- `markers` - Array of marker configurations for AdvancedMarkerElement

### Advanced
- `json_library` - JSON library module to use (e.g., JSON, Jason, Poison)
- `error_message` - Custom error message to display on map load failure

## Library Configuration

The `libraries` attribute uses a map-based configuration system that allows you to configure each library with specific options and event subscriptions:

```elixir
libraries={%{
  maps: %{
    eventSubscriptions: ["click", "zoom_changed", "center_changed"]
  },
  drawing: %{
    drawingMode: "circle",  # or "polygon", "marker", etc.
    drawingControl: false,  # Hide default drawing controls
    eventSubscriptions: ["overlaycomplete", "drawingmode_changed"],
    circleOptions: %{
      fillColor: "#3B82F6",
      fillOpacity: 0.2,
      strokeColor: "#1D4ED8",
      strokeWeight: 2
    },
    polygonOptions: %{
      fillColor: "#3B82F6",
      fillOpacity: 0.2,
      strokeColor: "#1D4ED8",
      strokeWeight: 2
    }
  },
  marker: %{
    eventSubscriptions: ["click", "dragend", "position_changed"]
  },
  places: %{
    placeFields: "basic",  # "basic", "contact", "atmosphere"
    eventSubscriptions: ["place_changed", "places_changed"]
  }
}}
```

### Available Libraries

- `maps` - Core Maps API (always loaded)
- `places` - Places API for location search and details
- `drawing` - Drawing tools for shapes and annotations
- `geometry` - Geometry utilities for calculations
- `marker` - Advanced markers with custom content
- `streetView` - Street View panorama integration
- `visualization` - Data visualization layers

## Map Options

The `map_options` attribute accepts any valid Google Maps MapOptions:

```elixir
map_options={%{
  center: %{lat: 37.7749, lng: -122.4194},
  zoom: 12,
  mapTypeId: "roadmap", # "roadmap", "satellite", "hybrid", "terrain"
  disableDefaultUI: false,
  gestureHandling: "auto", # "auto", "cooperative", "greedy", "none"
  restriction: %{
    latLngBounds: %{
      north: 85,
      south: -85,
      west: -180,
      east: 180
    }
  }
}}
```

## Shapes

The component supports all Google Maps shape types: circles, polygons, polylines, and rectangles. All shapes are created programmatically and support events through the existing drawing event system.

### Circle Shapes

```elixir
# Helper function for mile-to-meter conversion
defp miles_to_meters(miles), do: Boring.Utils.Distance.miles_to_meters(miles)

shapes={[
  %{
    id: "search-radius",
    type: :circle,
    center: %{lat: 37.7749, lng: -122.4194},
    radius: miles_to_meters(2), # 2-mile radius
    strokeColor: "#0000FF",
    strokeOpacity: 1.0,
    strokeWeight: 3,
    fillColor: "#0000FF", 
    fillOpacity: 0.2,
    draggable: true,
    editable: true
  }
]}
```

### Polygon Shapes

```elixir
shapes={[
  %{
    id: "restricted-area",
    type: :polygon,
    paths: [
      [
        %{lat: 37.7849, lng: -122.4294},
        %{lat: 37.7949, lng: -122.4194},
        %{lat: 37.7849, lng: -122.4094},
        %{lat: 37.7749, lng: -122.4194}
      ]
    ],
    strokeColor: "#FF0000",
    strokeOpacity: 0.8,
    strokeWeight: 2,
    fillColor: "#FF0000",
    fillOpacity: 0.3,
    editable: true
  }
]}
```

### Polyline Shapes

```elixir
shapes={[
  %{
    id: "route-path",
    type: :polyline,
    path: [
      %{lat: 37.7749, lng: -122.4194},
      %{lat: 37.7849, lng: -122.4094},
      %{lat: 37.7949, lng: -122.3994}
    ],
    strokeColor: "#00FF00",
    strokeOpacity: 1.0,
    strokeWeight: 4,
    editable: true
  }
]}
```

### Rectangle Shapes

```elixir
shapes={[
  %{
    id: "bounding-box",
    type: :rectangle,
    bounds: %{
      north: 37.7849,
      south: 37.7649,
      east: -122.4094,
      west: -122.4294
    },
    strokeColor: "#FF0000",
    strokeOpacity: 0.8,
    strokeWeight: 2,
    fillColor: "#FF0000",
    fillOpacity: 0.35,
    editable: true
  }
]}
```

### Multiple Shape Types

```elixir
# Example with distance conversion utility
alias Boring.Utils.Distance

shapes={[
  # 1-mile search radius
  %{
    id: "search-area",
    type: :circle,
    center: %{lat: 37.7749, lng: -122.4194},
    radius: Distance.miles_to_meters(1),
    strokeColor: "#0000FF",
    fillColor: "#0000FF",
    fillOpacity: 0.2,
    editable: true
  },
  # Exclusion zone polygon
  %{
    id: "exclusion-zone",
    type: :polygon,
    paths: [
      [
        %{lat: 37.7849, lng: -122.4294},
        %{lat: 37.7949, lng: -122.4194},
        %{lat: 37.7849, lng: -122.4094},
        %{lat: 37.7749, lng: -122.4194}
      ]
    ],
    strokeColor: "#FF0000",
    fillColor: "#FF0000",
    fillOpacity: 0.3
  },
  # Route polyline
  %{
    id: "suggested-route",
    type: :polyline,
    path: [
      %{lat: 37.7749, lng: -122.4194},
      %{lat: 37.7849, lng: -122.4094},
      %{lat: 37.7949, lng: -122.3994}
    ],
    strokeColor: "#00FF00",
    strokeWeight: 4
  }
]}
```

### Dynamic Shape Updates

The component supports efficient in-place shape updates without recreating Google Maps shape objects:

```elixir
# Update specific shape properties using the generic update function
def handle_event("update_search_radius", %{"radius_miles" => radius_miles}, socket) do
  socket = 
    socket
    |> push_event("update_shape", %{
      target: "search-map",
      shapeId: "search-area",  # Update specific shape by ID
      updates: %{
        radius: Distance.miles_to_meters(radius_miles)
      }
    })
    |> update_local_shape_state("search-area", %{radius: Distance.miles_to_meters(radius_miles)})

  {:noreply, socket}
end

# Update all circles at once
def handle_event("update_all_circles", %{"new_radius" => radius}, socket) do
  socket = 
    push_event(socket, "update_shape", %{
      target: "search-map",
      shapeId: "*",  # Update all shapes
      updates: %{
        radius: radius,
        fillOpacity: 0.3,
        strokeWeight: 2
      }
    })

  {:noreply, socket}
end

# Update shape style properties
def handle_event("highlight_shape", %{"shape_id" => shape_id}, socket) do
  socket = 
    push_event(socket, "update_shape", %{
      target: "search-map",
      shapeId: shape_id,
      updates: %{
        strokeColor: "#FF0000",
        strokeWeight: 4,
        fillOpacity: 0.5
      }
    })

  {:noreply, socket}
end

# Update shape interaction properties
def handle_event("make_shape_editable", %{"shape_id" => shape_id}, socket) do
  socket = 
    push_event(socket, "update_shape", %{
      target: "search-map",
      shapeId: shape_id,
      updates: %{
        editable: true,
        draggable: true
      }
    })

  {:noreply, socket}
end

# Add new shape
def handle_event("add_exclusion_area", %{"bounds" => bounds}, socket) do
  new_shape = %{
    id: "exclusion-#{System.unique_integer()}",
    type: :rectangle,
    bounds: bounds,
    strokeColor: "#FF0000",
    fillColor: "#FF0000",
    fillOpacity: 0.3
  }

  shapes = [new_shape | socket.assigns.shapes]
  {:noreply, assign(socket, :shapes, shapes)}
end

# Helper function to update local state
defp update_local_shape_state(socket, shape_id, updates) do
  updated_shapes = 
    Enum.map(socket.assigns.shapes, fn shape ->
      if shape.id == shape_id do
        Map.merge(shape, updates)
      else
        shape
      end
    end)

  assign(socket, :shapes, updated_shapes)
end
```

#### Supported Update Properties

The generic `update_shape` function supports updating any Google Maps shape property:

**Circle Properties:**
- `radius` - Circle radius in meters
- `center` - Circle center coordinates `%{lat: float, lng: float}`

**Common Properties (all shapes):**
- `visible` - Show/hide shape (boolean)
- `draggable` - Enable/disable dragging (boolean)
- `editable` - Enable/disable editing (boolean)
- `clickable` - Enable/disable click events (boolean)

**Style Properties:**
- `strokeColor` - Border color (hex string)
- `strokeOpacity` - Border opacity (0.0-1.0)
- `strokeWeight` - Border width in pixels
- `fillColor` - Fill color (hex string)
- `fillOpacity` - Fill opacity (0.0-1.0)
- `zIndex` - Layer ordering

**Polygon/Polyline Properties:**
- `path` - Coordinate array for polylines
- `paths` - Nested coordinate arrays for polygons

**Rectangle Properties:**
- `bounds` - Rectangle bounds object

**Generic Update:**
- `options` - Any Google Maps shape options object

## Advanced Markers

The component supports Google Maps AdvancedMarkerElement with custom content:

```elixir
markers={[
  %{
    id: "marker-1",
    position: %{lat: 37.7749, lng: -122.4194},
    title: "San Francisco",
    content: "<div class='bg-blue-500 text-white p-2 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold'>SF</div>",
    draggable: true
  },
  %{
    id: "marker-2",
    position: %{lat: 37.7849, lng: -122.4094},
    title: "Another Location"
    # Uses default marker appearance
  }
]}
```

### Marker Events

Handle marker events in your LiveView:

```elixir
def handle_event("marker_event", %{"type" => "click", "markerId" => marker_id, "data" => data}, socket) do
  # Handle marker click - data contains marker position and details
  {:noreply, assign(socket, :selected_marker, marker_id)}
end

def handle_event("marker_event", %{"type" => "dragend", "markerId" => marker_id, "data" => data}, socket) do
  # Handle marker drag end - data contains new position
  new_position = data["position"]
  # Update your data store with new marker position
  {:noreply, socket}
end
```

## Drawing Tools

Enable drawing functionality with dynamic configuration:

```elixir
# In your LiveView template
libraries={%{
  maps: %{
    eventSubscriptions: ["click", "zoom_changed"]
  },
  drawing: if(@is_drawing, do: %{
    drawingMode: @drawing_mode,  # "circle" or "polygon"
    drawingControl: false,       # Use custom UI controls
    eventSubscriptions: ["overlaycomplete", "drawingmode_changed"],
    circleOptions: %{
      fillColor: "#3B82F6",
      fillOpacity: 0.2,
      strokeColor: "#1D4ED8",
      strokeWeight: 2
    },
    polygonOptions: %{
      fillColor: "#3B82F6",
      fillOpacity: 0.2,
      strokeColor: "#1D4ED8",
      strokeWeight: 2
    }
  }, else: %{
    # Drawing disabled - no drawingMode set
    drawingControl: false,
    eventSubscriptions: ["overlaycomplete", "drawingmode_changed"]
  })
}}
```

### Drawing Events

```elixir
def handle_event("drawing_event", %{"type" => "overlaycomplete", "data" => data}, socket) do
  # Handle completed drawing - data contains geometry as GeoJSON
  geometry = data["geometry"]
  {:noreply, assign(socket, :drawn_shape, geometry)}
end
```

## Geocoding

The component includes built-in geocoding functionality:

```elixir
# In your LiveView
def handle_event("search_location", %{"location" => location}, socket) do
  {:noreply, push_event(socket, "geocode_and_center", %{location: location})}
end

# Handle geocoding results
def handle_event("geocoding_success", %{"location" => location, "formatted_address" => address}, socket) do
  {:noreply,
   socket
   |> assign(:map_center, %{lat: location["lat"], lng: location["lng"]})
   |> assign(:current_address, address)}
end

def handle_event("geocoding_error", %{"error" => error, "location" => location}, socket) do
  # Handle geocoding errors
  {:noreply, put_flash(socket, :error, "Could not find location: #{location}")}
end
```

## JSON Library Configuration

The component supports multiple JSON libraries with smart defaults:

### Default Behavior
```elixir
<.live_component module={PetalProWeb.Components.GoogleMap} id="my-map" />
```

By default, the component uses the built-in `JSON` module (Elixir 1.18+). If you're on an older Elixir version or want to use a different library, specify it explicitly.

### Explicit Library Specification
```elixir
# Use Jason explicitly
<.live_component
  module={PetalProWeb.Components.GoogleMap}
  id="my-map"
  json_library={Jason}
/>

# Use built-in JSON (Elixir 1.18+)
<.live_component
  module={PetalProWeb.Components.GoogleMap}
  id="my-map"
  json_library={JSON}
/>

# Use Poison
<.live_component
  module={PetalProWeb.Components.GoogleMap}
  id="my-map"
  json_library={Poison}
/>
```

### Library Validation
The component validates that the specified JSON library:
- Is available and loaded
- Exports the `encode!/1` function
- Provides helpful error messages if validation fails

## Event Handling

The component forwards all Google Maps events to your LiveView. Configure which events to receive using the `eventSubscriptions` option in each library configuration.

### Map Events

```elixir
def handle_event("map_event", %{"type" => "click", "data" => data}, socket) do
  # data contains: %{"latLng" => %{"lat" => 37.7749, "lng" => -122.4194}}
  {:noreply, socket}
end

def handle_event("map_event", %{"type" => "zoom_changed", "data" => data}, socket) do
  # data contains: %{"zoom" => 12}
  {:noreply, socket}
end

def handle_event("map_event", %{"type" => "center_changed", "data" => data}, socket) do
  # data contains: %{"center" => %{"lat" => 37.7749, "lng" => -122.4194}}
  {:noreply, socket}
end
```

### Drawing Events

```elixir
def handle_event("drawing_event", %{"type" => "overlaycomplete", "data" => data}, socket) do
  # data contains: %{"overlayType" => "circle", "geometry" => %{...}}
  {:noreply, socket}
end

def handle_event("shape_modified", %{"geometry" => geometry, "overlayId" => overlay_id}, socket) do
  # Handle shape modifications (drag, resize, etc.)
  {:noreply, socket}
end
```

### Error Events

```elixir
def handle_event("map_error", %{"error" => error, "mapId" => map_id}, socket) do
  # Handle map loading or API errors
  {:noreply, put_flash(socket, :error, "Map error: #{error}")}
end
```

## Advanced Usage

### Custom Configuration

```elixir
custom_config={%{
  onMapReady: fn(map, libraries) ->
    # Custom JavaScript function called when map is ready
    # Access to map instance and loaded libraries
  end
}}
```

### Dynamic Updates

The component provides a powerful generic update system that can handle any type of map configuration change:

#### Using the Helper Function

```elixir
# Update map options (type, center, zoom, etc.)
def handle_event("change_view", _params, socket) do
  socket = PetalProWeb.Components.GoogleMap.update_map(socket, "my-map", %{
    mapOptions: %{mapTypeId: "satellite", zoom: 15}
  })

  {:noreply, socket}
end

# Load new libraries dynamically
def handle_event("enable_drawing", _params, socket) do
  socket = PetalProWeb.Components.GoogleMap.update_map(socket, "my-map", %{
    libraries: ["drawing", "geometry"],
    eventHandlers: ["click", "overlaycomplete", "drawing_mode_changed"]
  })

  {:noreply, socket}
end
```

#### Update Types

The `update_map/3` function supports these update types:

- **`mapOptions`**: Any Google Maps map options (center, zoom, mapTypeId, etc.)
- **`libraries`**: Load additional Google Maps libraries dynamically
- **`eventHandlers`**: Update which events are forwarded to LiveView
- **`customConfig`**: Apply custom JavaScript configuration

## Dynamic Updates

The component supports dynamic updates through LiveView assigns. When assigns change, the component automatically updates the map configuration:

```elixir
# Update map center and zoom
def handle_event("center_on_location", %{"lat" => lat, "lng" => lng}, socket) do
  {:noreply,
   socket
   |> assign(:map_center, %{lat: lat, lng: lng})
   |> assign(:map_zoom, 15)}
end

# Toggle drawing mode
def handle_event("toggle_drawing", _params, socket) do
  {:noreply, assign(socket, :is_drawing, !socket.assigns.is_drawing)}
end

# Update markers
def handle_event("add_marker", %{"lat" => lat, "lng" => lng}, socket) do
  new_marker = %{
    id: "marker-#{System.unique_integer()}",
    position: %{lat: lat, lng: lng},
    title: "New Marker"
  }

  markers = [new_marker | socket.assigns.markers]
  {:noreply, assign(socket, :markers, markers)}
end
```

## Circle Placement Example

This example demonstrates placing circles on the map by clicking. Users select a radius (1, 3, 5, or 10 miles) and then click "Place Circle" to enter placement mode. When they click on the map, a circle with the selected radius is placed at that location.

### Key Features:
- **Radius Selection**: Radio buttons for common search radius options
- **Click-to-Place**: Users click on the map to place circles at specific locations
- **Visual Feedback**: UI shows when placement mode is active
- **Multiple Circles**: Users can place multiple circles with different radii
- **Interactive Circles**: Placed circles are editable and draggable

## Complete Example

```elixir
defmodule MyAppWeb.OpportunitySearchLive do
  use MyAppWeb, :live_view
  alias Boring.Utils.Distance

  def mount(_params, _session, socket) do
    form_data = %{
      "location" => ""
    }

    {:ok,
     socket
     |> assign(:page_title, "Map Search")
     |> assign(:map_center, %{lat: 39.8283, lng: -98.5795})
     |> assign(:map_zoom, 5)
     |> assign(:markers, [])
     |> assign(:shapes, [])
     |> assign(:places, [])
     |> assign(:selected_place_id, nil)
     |> assign(:form, to_form(form_data, as: :search))
     |> assign(:selected_radius_miles, 1)
     |> assign(:placement_mode, false)}
  end

  def render(assigns) do
    ~H"""
    <div class="h-screen flex flex-col">
      <!-- Search Controls -->
      <div class="p-4 bg-white border-b">
        <div class="space-y-4">
          <!-- Location Search -->
          <.simple_form for={@form} phx-change="form_changed">
            <.field
              field={@form[:location]}
              type="text"
              label="Search Location"
              placeholder="Enter city or address..."
              class="max-w-md"
            />
          </.simple_form>

          <!-- Circle Placement Controls -->
          <div class="flex items-center gap-6">
            <!-- Radius Selection -->
            <div class="flex flex-col gap-2">
              <label class="text-sm font-medium text-gray-700">Search Radius</label>
              <div class="flex items-center gap-4">
                <label class="flex items-center gap-2">
                  <input
                    type="radio"
                    name="radius"
                    value="1"
                    checked={@selected_radius_miles == 1}
                    phx-click="set_radius"
                    phx-value-miles="1"
                    class="text-blue-600"
                  />
                  <span class="text-sm">1 mile</span>
                </label>
                <label class="flex items-center gap-2">
                  <input
                    type="radio"
                    name="radius"
                    value="3"
                    checked={@selected_radius_miles == 3}
                    phx-click="set_radius"
                    phx-value-miles="3"
                    class="text-blue-600"
                  />
                  <span class="text-sm">3 miles</span>
                </label>
                <label class="flex items-center gap-2">
                  <input
                    type="radio"
                    name="radius"
                    value="5"
                    checked={@selected_radius_miles == 5}
                    phx-click="set_radius"
                    phx-value-miles="5"
                    class="text-blue-600"
                  />
                  <span class="text-sm">5 miles</span>
                </label>
                <label class="flex items-center gap-2">
                  <input
                    type="radio"
                    name="radius"
                    value="10"
                    checked={@selected_radius_miles == 10}
                    phx-click="set_radius"
                    phx-value-miles="10"
                    class="text-blue-600"
                  />
                  <span class="text-sm">10 miles</span>
                </label>
              </div>
            </div>

            <!-- Placement Button -->
            <div class="flex flex-col gap-2">
              <.button
                type="button"
                phx-click="toggle_placement_mode"
                class={[
                  if(@placement_mode, do: "bg-red-600 hover:bg-red-700", else: "bg-blue-600 hover:bg-blue-700"),
                  "text-white px-4 py-2"
                ]}
              >
                {if @placement_mode, do: "Cancel Placement", else: "Place Circle"}
              </.button>

              <%= if @placement_mode do %>
                <div class="text-sm text-blue-600 font-medium">
                  Click on the map to place a <%= @selected_radius_miles %>-mile radius circle
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Map -->
      <div class="flex-1">
        <.live_component
          module={PetalProWeb.Components.GoogleMap}
          id="search-map"
          height="100%"
          width="100%"
          map_options={%{
            center: @map_center,
            zoom: @map_zoom,
            mapTypeControl: true,
            fullscreenControl: true,
            gestureHandling: "greedy"
          }}
          markers={@markers}
          shapes={@shapes}
          libraries={%{
            maps: %{
              eventSubscriptions: ["click", "zoom_changed", "center_changed"]
            }
          }}
        />
      </div>
    </div>
    """
  end

  # Event Handlers
  def handle_event("form_changed", %{"search" => %{"location" => location}}, socket) do
    socket = 
      if String.trim(location) != "" do
        push_event(socket, "geocode_and_center", %{location: location})
      else
        socket
      end

    {:noreply, socket}
  end

  def handle_event("set_radius", %{"miles" => miles_str}, socket) do
    {miles, _} = Integer.parse(miles_str)
    {:noreply, assign(socket, :selected_radius_miles, miles)}
  end

  def handle_event("toggle_placement_mode", _params, socket) do
    {:noreply, assign(socket, :placement_mode, !socket.assigns.placement_mode)}
  end

  def handle_event("geocoding_success", %{"location" => location}, socket) do
    {:noreply,
     socket
     |> assign(:map_center, %{lat: location["lat"], lng: location["lng"]})
     |> assign(:map_zoom, 15)}
  end

  def handle_event("map_event", %{"type" => "click", "data" => %{"latLng" => lat_lng}}, socket) do
    socket = 
      if socket.assigns.placement_mode do
        socket
        |> add_search_circle(lat_lng)
        |> assign(:placement_mode, false)
      else
        socket
      end

    {:noreply, socket}
  end

  def handle_event("map_event", %{"type" => _type, "data" => _data}, socket) do
    # Handle other map events
    {:noreply, socket}
  end

  # Helper functions
  defp add_search_circle(socket, center) do
    # Generate unique ID for each circle
    circle_id = "circle-#{System.unique_integer([:positive])}"
    
    search_circle = %{
      id: circle_id,
      type: :circle,
      center: center,
      radius: Distance.miles_to_meters(socket.assigns.selected_radius_miles),
      strokeColor: "#0000FF",
      strokeOpacity: 1.0,
      strokeWeight: 3,
      fillColor: "#0000FF",
      fillOpacity: 0.2,
      editable: true,
      draggable: true
    }

    # Add the new circle to existing shapes
    shapes = [search_circle | socket.assigns.shapes]
    assign(socket, :shapes, shapes)
  end
end
```

## Debugging

The component provides several debugging features:

1. **Console Logging**: Enable detailed logging by checking browser console
2. **Error Messages**: User-friendly error display for common issues
3. **Event Inspection**: All events are logged to console during development
4. **Map Instance Access**: Access the map instance via the hook for debugging

### Common Issues

- **Geocoding not working**: Ensure the map is fully initialized before sending geocoding requests
- **Drawing not activating**: Check that `drawingMode` is set in the library configuration
- **Events not firing**: Verify `eventSubscriptions` are configured for the relevant library
- **Markers not appearing**: Ensure the `marker` library is loaded and marker data is valid

## Performance Notes

- **Reactive Updates**: Uses `phx-update="ignore"` to prevent unnecessary re-renders
- **Library Loading**: Libraries are loaded once and cached per map instance
- **Event Cleanup**: Event listeners are properly cleaned up on component destruction
- **Efficient Serialization**: Event data is efficiently serialized for LiveView communication
- **Dynamic Configuration**: Only loads libraries and features when needed based on assigns

## Best Practices

1. **Use Dynamic Configuration**: Configure libraries based on LiveView state to only load what's needed
2. **Handle Events Gracefully**: Always include catch-all event handlers to prevent crashes
3. **Validate Marker Data**: Ensure marker positions are valid before passing to the component
4. **Debounce User Input**: Use `phx-debounce` on location search inputs to avoid excessive geocoding requests
5. **Error Handling**: Always handle geocoding and map errors gracefully with user feedback

## API Reference

### Component Attributes

| Attribute | Type | Default | Description |
|-----------|------|---------|-------------|
| `id` | `string` | required | Unique identifier for the map |
| `api_key` | `string` | env var | Google Maps API key |
| `height` | `string` | `"100%"` | Map container height |
| `width` | `string` | `"100%"` | Map container width |
| `class` | `string` | `""` | CSS classes for container |
| `style` | `string` | `""` | Inline styles for container |
| `map_options` | `map` | `%{}` | Google Maps MapOptions |
| `libraries` | `map` | `%{}` | Library configurations |
| `markers` | `list` | `[]` | Marker configurations |
| `shapes` | `list` | `[]` | Shape configurations (circles, polygons, polylines, rectangles) |
| `loader_options` | `map` | `%{}` | Google Maps Loader options |
| `json_library` | `module` | auto-detect | JSON encoding library |

### Events Sent to LiveView

| Event | Data | Description |
|-------|------|-------------|
| `map_event` | `%{type, data, mapId}` | Map interaction events |
| `marker_event` | `%{type, markerId, data, mapId}` | Marker interaction events |
| `drawing_event` | `%{type, data, mapId}` | Drawing tool events |
| `shape_modified` | `%{geometry, overlayId, mapId}` | Shape modification events |
| `geocoding_success` | `%{location, formatted_address}` | Successful geocoding |
| `geocoding_error` | `%{error, location}` | Failed geocoding |
| `map_error` | `%{error, mapId}` | Map loading errors |

### Events Sent from LiveView

| Event | Data | Description |
|-------|------|-------------|
| `geocode_and_center` | `%{location}` | Geocode address and center map |
| `update_map` | `%{target, mapOptions}` | Update map configuration |
| `update_drawing_mode` | `%{target, drawingMode}` | Change drawing mode |
