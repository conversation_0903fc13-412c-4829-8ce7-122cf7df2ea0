## Get up and running

**Assumptions:**

- You have <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON> installed
- You have Postgres installed and running (optional - see "Using Docker for Postgres" below

**The steps**

1. `npm i --prefix assets` Install npm packages
2. Add EzSuite repo for Phx2Ban
  ```
  mix hex.repo add ezsuite https://ezsuite.dev/repo \
  --auth-key $EZSUITE_AUTH_KEY \
  --fetch-public-key SHA256:5WqcbEXE2PRHFpPrlJeaCCS1mAokfq6Bf/rdKzukVQ4
  ```
3. `mix setup` (this get dependencies, setup database, migrations, seeds, install esbuild + Tailwind, and install git hooks)
4. `iex -S mix phx.server` (starts the server in IEX mode)
5. Now you can visit [`localhost:4000`](http://localhost:4000) from your browser. If you'd like to use a different port, set the `PORT` environment variable to your preference. Note that if you're using the VS Code dev container, you'll need to update the `"forwardPorts"` configuration accordingly.

## Using Docker for Postgres

If your system doesn't have Postgres you can use docker-compose to run it in a container (saves you having to install it).

| Command                                           | Description                                              |
| ------------------------------------------------- | -------------------------------------------------------- |
| `docker compose up`                               | Start in the foreground                                  |
| `docker compose up -d`                            | Start in the background                                  |
| `docker compose down`                             | Stop the containers                                      |
| `docker compose down -v`                          | Stop and delete all Postgres data by removing the volume |
| `docker compose exec db psql --username postgres` | Access through psql                                      |

The connection details for any local clients would be the following:

```
Host: localhost
Port: 5432
User: postgres
Password: postgres
```

## Maintaining code quality as you develop

Run `mix quality` to look for issues with your code. This will run each of these tasks:

- `mix format` (formats your code)
- `mix sobelow --config` (security analysis)
- `mix coveralls` (test coverage)
- `mix credo` (code quality)

If the output is overwhelming, try running one at a time.

## Git Hooks

This project uses git hooks to maintain code quality automatically:

- **pre-commit**: Automatically runs `mix format` before each commit
- **commit-msg**: Validates commit messages follow Conventional Commits specification

Git hooks are automatically installed when you run `mix setup`. If you need to install or update them manually, run:

```bash
./scripts/setup-hooks
```

For more details, see [scripts/hooks/README.md](scripts/hooks/README.md).
