name: Boring CI

on:
  push:
    tags-ignore:
      - '**'
  pull_request:
    branches: [main]
  release:
    types: [prereleased, released]

jobs:
  elixir-deps:
    name: mix deps.get
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: team-alembic/staple-actions/actions/install-elixir@main
      - uses: team-alembic/staple-actions/actions/mix-task-composable@main
        with:
          mix-env: test
          task: "hex.repo add ezsuite https://ezsuite.dev/repo \
            --auth-key ${{ secrets.EZSUITE_AUTH_KEY }} \
            --fetch-public-key SHA256:5WqcbEXE2PRHFpPrlJeaCCS1mAokfq6Bf/rdKzukVQ4"
      - uses: team-alembic/staple-actions/actions/mix-deps-get-composable@main

  js-deps:
    name: npm install
    runs-on: ubuntu-latest
    needs: elixir-deps
    steps:
      - uses: actions/checkout@v4
      - name: Cache JS deps
        id: node-cache
        uses: actions/cache@v4
        with:
          path: |
            assets/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/assets/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-
      - name: Install JS deps
        if: steps.node-cache.outputs.cache-hit != 'true'
        run: npm install --prefix assets
      

  auditor:
    name: mix hex.audit
    runs-on: ubuntu-latest
    needs: elixir-deps
    steps:
      - uses: actions/checkout@v4
      - uses: team-alembic/staple-actions/actions/mix-hex-audit@main

  build-test:
    name: MIX_ENV=test mix.compile
    runs-on: ubuntu-latest
    needs: elixir-deps
    steps:
      - uses: actions/checkout@v4
      - uses: team-alembic/staple-actions/actions/install-elixir@main
      - uses: team-alembic/staple-actions/actions/mix-compile@main
        with:
          mix-env: test

  formatter:
    name: mix format --check-formatted
    runs-on: ubuntu-latest
    needs: build-test
    steps:
      - uses: actions/checkout@v4
      - uses: team-alembic/staple-actions/actions/mix-format@main
        with:
          mix-env: test

  credo:
    name: mix credo --strict
    runs-on: ubuntu-latest
    needs: build-test
    steps:
      - uses: actions/checkout@v4
      - uses: team-alembic/staple-actions/actions/mix-task@main
        with:
          mix-env: test
          task: credo --strict --only warning

  test:
    name: mix test
    runs-on: ubuntu-latest
    needs: build-test
    services:
      postgres:
        image: postgres:15.8
        env:
          POSTGRES_HOST_AUTH_METHOD: trust
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - uses: actions/checkout@v4
      - uses: team-alembic/staple-actions/actions/mix-test@main
        with:
          mix-env: test
        env:
          PGUSER: postgres
          PGPASS: postgres
          PGHOST: postgres

  # conventional-commit:
  #   name: mix git_ops.check_message
  #   runs-on: ubuntu-latest
  #   needs: build-test
  #   steps:
  #     - uses: actions/checkout@v4
  #     - uses: team-alembic/staple-actions/actions/conventional-commit@main
  #       with:
  #         mix-env: test

  sobelow:
    name: mix sobelow --config
    runs-on: ubuntu-latest
    needs: build-test
    steps:
      - uses: actions/checkout@v4
      - uses: team-alembic/staple-actions/actions/mix-sobelow@main
        with:
          mix-env: test

  compilation-cycles:
    name: mix xref compile-connected
    runs-on: ubuntu-latest
    needs: build-test
    steps:
      - uses: actions/checkout@v4
      - uses: team-alembic/staple-actions/actions/mix-task@main
        with:
          mix-env: test
          task: xref graph --format cycles --label compile-connected --fail-above 9

  unused-deps:
    name: mix deps.unlock --unused
    runs-on: ubuntu-latest
    needs: build-test
    steps:
      - uses: actions/checkout@v4
      - uses: team-alembic/staple-actions/actions/mix-deps-unlock@main
        with:
          mix-env: test

  codegen:
    name: mix ash.codegen --check
    runs-on: ubuntu-latest
    needs: build-test
    steps:
      - uses: actions/checkout@v4
      - uses: team-alembic/staple-actions/actions/mix-task@main
        with:
          mix-env: test
          task: ash.codegen --check

  # build-dev:
  #   name: MIX_ENV=dev mix.compile
  #   runs-on: ubuntu-latest
  #   needs:
  #     - build-test
  #   steps:
  #     - uses: actions/checkout@v4
  #     - uses: team-alembic/staple-actions/actions/mix-compile@main
  #       with:
  #         mix-env: dev

  igniter-upgrade:
    name: mix igniter.upgrade
    runs-on: ubuntu-latest
    permissions:
      # Give the default GITHUB_TOKEN write permission to commit and push the
      # added or changed files to the repository.
      contents: write
    steps:
      - name: Dependabot metadata
        id: dependabot-metadata
        uses: dependabot/fetch-metadata@v2
        if: github.event.pull_request.user.login == 'dependabot[bot]'
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.head_ref }}
        if: github.event.pull_request.user.login == 'dependabot[bot]'
      - uses: team-alembic/staple-actions/actions/mix-task@main
        with:
          task: igniter.upgrade --git-ci --yes
        if: github.event.pull_request.user.login == 'dependabot[bot]'
      - name: Commit Changes
        uses: stefanzweifel/git-auto-commit-action@v6
        if: github.event.pull_request.user.login == 'dependabot[bot]'
        with:
          commit_message: "[dependabot skip] Apply Igniter Upgrades"
          commit_user_name: dependabot[bot]

  pre-release:
    name: Pre-Release
    runs-on: ubuntu-latest
    needs:
      - credo
      - unused-deps
      - codegen
      - igniter-upgrade
      - sobelow
      - formatter
      - auditor
      - compilation-cycles
      - test
    if: github.event_name == 'release' && github.event.action == 'prereleased'
    env:
      FLY_ORG: boring-business-staging
      FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN_STAGING }}
      MIX_ENV: 'prod'
    steps:
      # This step checks out a copy of your repository.
      - uses: actions/checkout@v4
      # This step runs `flyctl deploy`.
      - uses: superfly/flyctl-actions@master
        with:
          config: deployment/staging/fly.toml
          args: "deploy --app boring-staging --remote-only --build-secret ezsuite_auth_key=${{ secrets.EZSUITE_AUTH_KEY }}"

  release:
    name: Release
    runs-on: ubuntu-latest
    needs:
      - credo
      - unused-deps
      - codegen
      - igniter-upgrade
      - sobelow
      - formatter
      - auditor
      - compilation-cycles
      - test
    if: github.event_name == 'release' && github.event.action == 'released'
    env:
      FLY_ORG: boring-business-production
      FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN_PRODUCTION }}
      MIX_ENV: 'prod'
    steps:
      # This step checks out a copy of your repository.
      - uses: actions/checkout@v4
      # This step runs `flyctl deploy`.
      - uses: superfly/flyctl-actions@master
        with:
          config: deployment/production/fly.toml
          args: "deploy --app boring-production --remote-only --build-secret ezsuite_auth_key=${{ secrets.EZSUITE_AUTH_KEY }}"
