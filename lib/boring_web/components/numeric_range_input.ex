defmodule BoringWeb.Components.NumericRangeInput do
  @moduledoc false
  use PetalProWeb, :component
  use PetalComponents

  attr :id, :string, required: true
  attr :label, :string, default: ""
  attr :range_min, :integer, default: 5
  attr :range_max, :integer, default: 24
  attr :min_field, :map, required: true
  attr :max_field, :map, required: true
  attr :min_amount, :integer, default: 0
  attr :max_amount, :integer, default: 100
  attr :step, :integer, default: 1
  attr :wrapper_class, :string, default: ""

  def numeric_range_input(assigns) do
    ~H"""
    <div class={[@wrapper_class, "pc-form-field-wrapper", "md:mt-1", "w-full"]}>
      <div class="flex flex-row gap-2">
        <.field
          type="text"
          step={@step}
          name={@min_field.name}
          label={@label}
          value={@min_field.value}
          placeholder="No Min"
          min={@range_min}
          max={@max_field.value}
          id={@id <> "_min"}
          inputmode="numeric"
          wrapper_class="w-full"
        />
        <div class="flex flex-col items-center justify-center">-</div>
        <.field
          class="mt-7"
          type="text"
          step={@step}
          name={@max_field.name}
          label=""
          value={@max_field.value}
          placeholder="No Max"
          min={@min_field.value}
          max={@range_max}
          id={@id <> "_max"}
          inputmode="numeric"
          wrapper_class="w-full"
        />
      </div>
    </div>
    """
  end
end
