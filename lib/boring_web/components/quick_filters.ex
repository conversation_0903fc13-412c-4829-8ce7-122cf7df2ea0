defmodule BoringWeb.Components.QuickFilters do
  @moduledoc false
  use PetalProWeb, :component
  use PetalComponents

  attr :current_filter, :string, required: true
  attr :filters, :list, required: true
  attr :size, :string, default: "lg", values: ["sm", "md", "lg", "xl"]
  attr :rest, :global

  def quick_filters(assigns) do
    ~H"""
    <div {@rest}>
      <.button_group aria_label={gettext("Quick filters")} size={@size}>
        <:button
          :for={{filter, details} <- @filters}
          phx-click="preset_filter"
          phx-value-filter={filter}
          disabled={@current_filter == Atom.to_string(filter)}
        >
          {Phoenix.Naming.humanize(filter)}
          <.badge
            :if={Map.get(details, :count)}
            color={Map.get(details, :color)}
            class="pc-tab__number ml-3"
            label={Map.get(details, :count)}
            variant="soft"
          />
        </:button>
      </.button_group>
    </div>
    """
  end
end
