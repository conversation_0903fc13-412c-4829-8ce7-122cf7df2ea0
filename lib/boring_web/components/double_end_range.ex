defmodule BoringWeb.Components.DoubleEndRange do
  @moduledoc false
  use PetalProWeb, :component
  use PetalComponents

  attr :id, :string, required: true
  attr :label, :string, default: ""
  attr :range_min_label, :string, default: nil
  attr :range_max_label, :string, default: nil
  attr :range_min, :integer, default: 5
  attr :range_max, :integer, default: 24
  attr :step, :integer, default: 1
  attr :min_field, :map, required: true
  attr :max_field, :map, required: true
  attr :wrapper_class, :string, default: ""

  def double_end_range(assigns) do
    ~H"""
    <div class={[@wrapper_class, "pc-form-field-wrapper", "w-full"]}>
      <.field_label>{@label}</.field_label>
      <div id={@id} class="relative mt-4 h-12">
        <div class="flex flex-row items-center justify-center space-x-2">
          <div class="relative h-1 w-full">
            <div class="slider-track"></div>
            <div
              class="slider-range"
              data-slider-id={@id}
              id={@id <> "_slider-range"}
              style={"left: #{calculate_slider(@min_field.value, @range_min, @range_max)}%; right: #{100 - calculate_slider(@max_field.value, @range_min, @range_max)}%;"}
            >
            </div>
            <input
              type="range"
              min={@range_min}
              max={@range_max}
              step={@step}
              name={@min_field.name}
              value={@min_field.value}
              class="slider-input"
              id={@id <> "_min-range"}
              phx-hook="DualRangeSliderHook"
              data-slider-id={@id}
              data-slider-type="min"
              data-range-min={@range_min}
              data-range-max={@range_max}
            />
            <input
              type="range"
              min={@range_min}
              max={@range_max}
              step={@step}
              name={@max_field.name}
              value={@max_field.value}
              class="slider-input"
              id={@id <> "_max-range"}
              phx-hook="DualRangeSliderHook"
              data-slider-id={@id}
              data-slider-type="max"
              data-range-min={@range_min}
              data-range-max={@range_max}
            />
          </div>
        </div>
        <div class="mt-4 grid grid-cols-3 text-sm">
          <span class="flex items-start justify-start text-gray-500 dark:text-gray-400">
            {@range_min_label || @range_min}
          </span>
          <span class="flex justify-center text-gray-600 dark:text-gray-300">
            {"#{money_to_string(@min_field.value)} - #{money_to_string(@max_field.value)}"}
          </span>
          <span class="flex items-end justify-end text-gray-500 dark:text-gray-400">
            {@range_max_label || @range_max}
          </span>
        </div>
      </div>
    </div>
    """
  end

  defp calculate_slider(nil, _range_min, _range_max), do: 0

  defp calculate_slider(value, range_min, range_max) when is_integer(value) do
    round((value - range_min) / (range_max - range_min) * 100)
  end

  defp calculate_slider(value, range_min, range_max) when is_struct(value, Money) do
    int_value =
      value
      |> Money.to_decimal()
      |> Decimal.round(0)
      |> Decimal.to_integer()

    round((int_value - range_min) / (range_max - range_min) * 100)
  end

  defp money_to_string(value) when is_integer(value) do
    :USD
    |> Money.new!(value, fractional_digits: 0)
    |> money_to_string()
  end

  defp money_to_string(value) when is_struct(value, Money) do
    Money.to_string!(value, no_fraction_if_integer: true)
  end

  defp money_to_string(_), do: :USD |> Money.new!(0) |> money_to_string()
end
