defmodule BoringWeb.Components.FavoriteButton do
  @moduledoc false
  use PetalProWeb, :component
  use PetalComponents

  attr :item_id, :string, required: true
  attr :item_favorite?, :boolean, required: true

  def favorite_button(assigns) do
    attrs =
      if assigns.item_favorite? do
        %{
          event: "unfavorite",
          icon: "hero-trophy-solid",
          variant: "solid",
          color: "warning",
          tooltip: "Unfavorite"
        }
      else
        %{
          event: "favorite",
          icon: "hero-trophy",
          variant: "outline",
          color: "gray",
          tooltip: "Favorite"
        }
      end

    assigns = assign(assigns, favorite_button: attrs)

    ~H"""
    <.button
      class="ml-auto hover:scale-105"
      color={@favorite_button.color}
      with_icon
      variant={@favorite_button.variant}
      phx-click={@favorite_button.event}
      phx-value-id={@item_id}
      id={"favorite-opportunity-#{@item_id}"}
      data-tippy-content={@favorite_button.tooltip}
      data-tippy-placement="bottom"
      phx-hook="TippyHook"
    >
      <.icon name={@favorite_button.icon} class="h-5 w-5" />
    </.button>
    """
  end
end
