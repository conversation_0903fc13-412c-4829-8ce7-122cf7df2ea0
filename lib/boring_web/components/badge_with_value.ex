defmodule BoringWeb.Components.BadgeWithValue do
  @moduledoc false
  use PetalProWeb, :component
  use PetalComponents

  attr :label, :string, default: ""
  attr :variant, :string, default: "light", values: ["light", "dark", "soft", "outline"]

  attr :color, :string,
    default: "primary",
    values: ["primary", "secondary", "info", "success", "warning", "danger", "gray"]

  attr :size, :string, default: "md", values: ["xs", "sm", "md", "lg", "xl"]
  attr :value, :any, required: true
  attr :class, :string, default: ""
  attr :with_icon, :boolean, default: false
  attr :rest, :global

  slot :inner_block, required: false

  def badge_with_value(assigns) do
    ~H"""
    <div {@rest} class={["flex flex-row items-center", @class]}>
      <.badge
        color={@color}
        variant={@variant}
        with_icon={@with_icon}
        size={@size}
        class="rounded-r-none border-r-0"
      >
        {render_slot(@inner_block) || @label}
      </.badge>
      <.badge
        color={@color}
        variant={@variant}
        with_icon={@with_icon}
        size={@size}
        class={[
          "rounded-l-none px-2",
          @variant != "outline" && "bg-transparent text-black dark:bg-transparent dark:text-white"
        ]}
      >
        {@value}
      </.badge>
    </div>
    """
  end
end
