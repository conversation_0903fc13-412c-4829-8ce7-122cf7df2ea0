defmodule PetalPro.FileUploads.Tigris do
  @moduledoc """
  Add the following environment variables to your .envrc:

      export TIGRIS_ACCESS_KEY=""
      export TIGRIS_SECRET=""
      export TIGRIS_FILE_UPLOAD_BUCKET=""

  In your live view mount() function:

      socket
      |> allow_upload(:avatar,
          accept: ~w(.jpg .jpeg .png .gif .svg .webp),
          max_entries: 1,
          external: &PetalPro.FileUploads.S3.presign_upload/2
        )}

  When you want to retrieve the URLs:

      def handle_event("submit", %{"user" => user_params}, socket) do
        uploaded_files = PetalPro.FileUploads.S3.consume_uploaded_entries(socket, :avatar)
        # => ["http://your-bucket.s3.your-region.amazonaws.com/file"]

        # Do something with the uploaded files
      end
  """

  @doc """
  Signs a form upload.

  The configuration is a map which must contain the following keys:

    * `:access_key_id` - The Tigris access key id
    * `:secret_access_key` - The Tigris secret access key

  Returns a map of form fields to be used on the client via the JavaScript `FormData` API.

  ## Options

    * `:key` - The required key of the object to be uploaded.
    * `:max_file_size` - The required maximum allowed file size in bytes.
    * `:content_type` - The required MIME type of the file to be uploaded.
    * `:expires_in` - The required expiration time in milliseconds from now
      before the signed upload expires.

  ## Examples

      config = %{
        access_key_id: System.fetch_env!("TIGRIS_ACCESS_KEY_ID"),
        secret_access_key: System.fetch_env!("TIGRIS_SECRET_ACCESS_KEY")
      }

      {:ok, fields} =
        S3.sign_form_upload(config, "my-bucket",
          key: "public/my-file-name",
          content_type: "image/png",
          max_file_size: 10_000,
          expires_in: :timer.hours(1)
        )
  """

  @typedoc """
  Your AWS config
  """
  @type tigris_config :: %{
          :access_key_id => binary,
          :secret_access_key => binary
        }

  @spec presign_upload(map(), map()) :: {:ok, map(), map()} | {:error, term()}
  def presign_upload(entry, socket) do
    bucket_as_host = PetalPro.config([:file_upload, :upload_bucket_url])
    bucket = PetalPro.config([:file_upload, :upload_bucket])
    max_file_size = PetalPro.config([:file_upload, :max_file_size])
    upload_expiry = PetalPro.config([:file_upload, :upload_expiry])

    key =
      case entry.client_meta do
        %{remote_filename: filename} ->
          filename

        _ ->
          Ecto.UUID.generate() <> Path.extname(entry.client_name)
      end

    config =
      %{
        json_codec: Jason,
        access_key_id: Application.get_env(:boring, :tigris)[:access_key_id],
        secret_access_key: PetalPro.config([:tigris, :secret_access_key]),
        region: "auto"
      }

    %{fields: fields} =
      Boring.Api.S3Client.presigned_post(config, bucket, key,
        expires_in: upload_expiry,
        content_length_range: [0, max_file_size]
      )

    meta = %{
      uploader: "ExternalUploader",
      key: key,
      url: bucket_as_host,
      fields: fields
    }

    {:ok, meta, socket}
  end

  @spec consume_uploaded_entries(Phoenix.LiveView.Socket.t(), any, function()) :: list
  def consume_uploaded_entries(socket, uploads_key, callback \\ nil) do
    default_callback = fn upload, _entry ->
      {:ok, file_id_to_url(upload.fields["key"])}
    end

    callback = callback || default_callback

    Phoenix.LiveView.consume_uploaded_entries(socket, uploads_key, callback)
  end

  @spec file_id_to_url(any) :: String.t()
  defp file_id_to_url(key) do
    bucket_url = PetalPro.config([:file_upload, :upload_bucket_url])

    Path.join(bucket_url, key)
  end
end
