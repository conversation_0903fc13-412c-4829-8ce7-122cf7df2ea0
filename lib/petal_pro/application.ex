defmodule PetalPro.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      PetalProWeb.Telemetry,
      Boring.Repo,
      {DNSCluster, query: Application.get_env(:boring, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: PetalPro.PubSub},
      PetalProWeb.Presence,
      {Task.Supervisor, name: PetalPro.BackgroundTask},
      # Start the Finch HTTP client for sending emails and Tesla
      {<PERSON>, name: PetalPro.Finch},
      {Oban,
       AshOban.config(
         Application.fetch_env!(:boring, :ash_domains),
         Application.fetch_env!(:boring, Oban)
       )},
      # Start a worker by calling: PetalPro.Worker.start_link(arg)
      # {PetalPro.Worker, arg}
      {Phx2Ban, router: PetalProWeb.Router},
      # Start to serve requests, typically the last entry
      PetalProWeb.Endpoint
    ]

    if PetalPro.config(:env) != :dev do
      Oban.Telemetry.attach_default_logger(:info)
      Oban.Web.Telemetry.attach_default_logger(:info)
    end

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: PetalPro.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    PetalProWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
