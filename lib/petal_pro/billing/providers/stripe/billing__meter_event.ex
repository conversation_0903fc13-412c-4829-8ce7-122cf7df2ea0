defmodule Stripe.Billing.MeterEvent do
  @moduledoc """
  Represents a metered billing event in the Stripe API.

  Meter events track usage-based billing actions that customers take in your system.
  Each meter event is associated with a billing meter, which defines:

  * The contents of the event's payload
  * How to aggregate those events for billing purposes
  * How the events are translated into invoice line items

  Meter events are the foundation of usage-based billing in Stripe, allowing you to:

  * Track granular usage data for each customer
  * Bill customers based on their actual consumption
  * Support various aggregation methods (sum, max, latest, etc.)
  * Create custom pricing tiers based on usage volume

  For more information, see the [Stripe Billing Meters documentation](https://stripe.com/docs/billing/subscriptions/usage-based).
  """
  use Stripe.Entity

  defstruct [:created, :event_name, :identifier, :livemode, :object, :payload, :timestamp]

  @type t :: %__MODULE__{
          created: integer(),
          event_name: binary(),
          identifier: binary(),
          livemode: boolean(),
          object: binary(),
          payload: term(),
          timestamp: integer()
        }

  @doc """
  Creates a new billing meter event in Stripe.

  This function sends usage data to Strip<PERSON> for a specific customer, which can then be
  used for usage-based billing. The meter event includes:

  * An event name that corresponds to a billing meter in your Stripe account
  * A payload containing the customer ID and usage value
  * A timestamp for when the event occurred

  ## API Details

  * Method: `POST`
  * Path: `/v1/billing/meter_events`

  ## Parameters

  * `:event_name` - (Required) The name of the billing meter this event is for
  * `:payload` - (Required) A map containing:
    * `:stripe_customer_id` - The Stripe customer ID to bill
    * `:value` - The numeric value representing the usage amount
  * `:timestamp` - (Required) When the event occurred (Unix timestamp format)
  * `:identifier` - (Optional) A custom identifier for the event

  ## Examples

  ```elixir
  # Create a meter event for a customer using 5 units of the "skiptrace" service
  params = %{
    event_name: "skiptrace",
    payload: %{
      stripe_customer_id: "cus_12345",
      value: 5
    },
    timestamp: DateTime.utc_now() |> DateTime.to_unix()
  }

  {:ok, meter_event} = Stripe.Billing.MeterEvent.create(params)
  ```

  ## Returns

  * `{:ok, meter_event}` - A successful response with the created meter event
  * `{:error, error}` - An error occurred during the API request
  """
  @spec create(
          params :: %{
            :event_name => binary(),
            optional(:identifier) => binary(),
            :payload => %{:stripe_customer_id => binary(), :value => integer()},
            :timestamp => binary()
          },
          opts :: Keyword.t()
        ) ::
          {:ok, Stripe.Billing.MeterEvent.t()}
          | {:error, Stripe.ApiErrors.t()}
          | {:error, term()}
  def create(params, opts \\ []) do
    path = Stripe.OpenApi.Path.replace_path_params("/v1/billing/meter_events", [], [])

    opts
    |> Stripe.Request.new_request()
    |> Stripe.Request.put_endpoint(path)
    |> Stripe.Request.put_params(params)
    |> Stripe.Request.put_method(:post)
    |> Stripe.Request.make_request()
  end
end
