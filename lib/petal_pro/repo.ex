defmodule Boring.Repo do
  use AshPostgres.Repo,
    otp_app: :boring

  use PetalPro.Extensions.Ecto.RepoExt

  def installed_extensions do
    # Add extensions here, and the migration generator will install them.
    ["citext", "ash-functions", AshMoney.AshPostgresExtension]
  end

  # Don't open unnecessary transactions
  # will default to `false` in 4.0
  def prefer_transaction? do
    false
  end

  def min_pg_version do
    %Version{major: 15, minor: 7, patch: 0}
  end
end
