<h1>Header 1</h1>
<h2>Header 2</h2>
<h3>Header 3</h3>

<EmailComponents.gap />

<h1>Paragraphs &amp; Formatting</h1>
<p>
  Transactional email is fun for the whole family! You can design it, write it, code it, and test it. And test it. And test it. And send it. And find a bug.
</p>
<p>
  This paragraph has some <b>bold text</b>
  and <strong>strong text</strong>
  along with <i>italicized text</i>
  and <em>emphasized text</em>
  .
</p>

<EmailComponents.gap />

<h1>Lists</h1>
<ul>
  <li>Unordered list item 1</li>
  <li>Unordered list item 2</li>
  <li>Unordered list item 3</li>
</ul>
<ol>
  <li>Ordered list item 1</li>
  <li>Ordered list item 2</li>
  <li>Ordered list item 3</li>
</ol>

<EmailComponents.gap />

<h1>Action Buttons</h1>

<EmailComponents.centered>
  <EmailComponents.button to="/" size="sm" color="green">
    Green button
  </EmailComponents.button>
</EmailComponents.centered>

<EmailComponents.centered>
  <EmailComponents.button to="/" size="md" color="blue">
    Blue button
  </EmailComponents.button>
</EmailComponents.centered>

<EmailComponents.centered>
  <EmailComponents.button to="/" size="md" color="gray">
    Gray button
  </EmailComponents.button>
</EmailComponents.centered>

<EmailComponents.centered>
  <EmailComponents.button to="/" size="lg" color="red">
    Red button
  </EmailComponents.button>
</EmailComponents.centered>

<h1>Attribute List</h1>
<EmailComponents.gray_box>
  <strong>SignIn Page:</strong> https://google.com <br />
  <strong>Username:</strong> username
</EmailComponents.gray_box>

<EmailComponents.gap />

<h1>Example Closing</h1>
<p>
  Thanks, <br /> [Sender Name] and the [Product Name] Team
</p>
<p>
  <strong>P.S.</strong>
  Need help getting started? Check out our help documentation. Or, just reply to this email with any questions or issues you have. The [Product Name] support team is always excited to help you.
</p>

<EmailComponents.gap />

<h1>Discount Code</h1>

<EmailComponents.dotted_gray_box>
  <h1 class="align-center">
    10% off your next purchase!
  </h1>
  <p class="align-center">
    Thanks for your support! Here's a coupon for 10% off your next purchase if used by [expiration_date].
  </p>
  <EmailComponents.centered>
    <EmailComponents.button to="/">
      Button centered
    </EmailComponents.button>
  </EmailComponents.centered>
</EmailComponents.dotted_gray_box>

<EmailComponents.gap />

<h1>Sub-text</h1>
<p>
  Sub-text is for any content that needs to be included at the bottom of the email but doesn't need to stand out. This can be good for disclaimers and text alternatives.
</p>

<EmailComponents.top_border>
  <EmailComponents.small_text>
    If you’re having trouble clicking the confirm account button, copy and paste the URL below into your web browser.
  </EmailComponents.small_text>
  <EmailComponents.small_text>
    <a href="/">
      Some link
    </a>
  </EmailComponents.small_text>
</EmailComponents.top_border>
