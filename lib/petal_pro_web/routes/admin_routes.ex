defmodule PetalProWeb.AdminRoutes do
  @moduledoc false
  import Oban.Web.Router
  import Phoenix.LiveDashboard.Router

  defmacro __using__(_) do
    quote do
      scope "/admin", PetalProWeb do
        pipe_through [:browser, :authenticated, :require_admin_user]

        live_dashboard_opts =
          live_dashboard "/server",
            metrics: PetalProWeb.Telemetry,
            ecto_repos: [Boring.Repo],
            ecto_psql_extras_options: [long_running_queries: [threshold: "200 milliseconds"]],
            additional_pages: [
              route_name: Phx2Ban.LiveDashboardPlugin
            ]

        oban_dashboard("/oban")

        live_session :require_admin_user,
          on_mount: [
            {PetalProWeb.UserOnMountHooks, :require_admin_user},
            {PetalProWeb.AssignScopeHook, :default}
          ] do
          live "/dashboard", AdminDashboardLive, :index
          live "/users", AdminUserLive.Index, :index
          live "/users/new", AdminUserLive.Index, :new
          live "/users/:user_id/edit", AdminUserLive.Index, :edit
          live "/users/:user_id", AdminUserLive.Show, :show
          live "/users/:user_id/show/edit", AdminUserLive.Show, :edit

          live "/orgs", AdminOrgLive.Index, :index
          live "/orgs/new", AdminOrgLive.Index, :new
          live "/orgs/:slug/edit", AdminOrgLive.Index, :edit
          live "/orgs/:slug", AdminOrgLive.Show, :show
          live "/orgs/:slug/show/edit", AdminOrgLive.Show, :edit

          live "/service-requests", ServiceRequestsLive.Index
          live "/service-requests/:opportunity_id", ServiceRequestsLive.Process

          live "/logs", LogsLive, :index
          live "/ai-chat", AdminAiChatLive
          live "/subscriptions", AdminSubscriptionsLive

          live "/posts", AdminPostLive.Index, :index
          live "/posts/new", AdminPostLive.Index, :new
          live "/posts/:id/edit", AdminPostLive.Index, :edit

          live "/posts/:id", AdminPostLive.Show, :show
          live "/posts/:id/show/edit", AdminPostLive.Show, :edit
          live "/posts/:id/show/edit/files/:image_target", AdminPostLive.Show, :files
          live "/posts/:id/show/publish", AdminPostLive.Show, :publish

          scope "/google" do
            live "/place_extracts", AdminGooglePlaceExtractLive.Index, :index
            live "/place_extracts/new", AdminGooglePlaceExtractLive.Index, :new
            live "/place_extracts/:id/edit", AdminGooglePlaceExtractLive.Index, :edit
            live "/place_extracts/:id", AdminGooglePlaceExtractLive.Show, :show
            live "/place_extracts/:id/show/edit", AdminGooglePlaceExtractLive.Show, :edit

            live "/places", AdminGooglePlaceLive.Index, :index
            live "/places/new", AdminGooglePlaceLive.Index, :new
            live "/places/:id/edit", AdminGooglePlaceLive.Index, :edit
            live "/places/:id", AdminGooglePlaceLive.Show, :show
            live "/places/:id/show/edit", AdminGooglePlaceLive.Show, :edit
          end

          live "/opportunity_exclusions", AdminOpportunityExclusionLive.Index, :index
          live "/opportunity_exclusions/new", AdminOpportunityExclusionLive.Index, :new
          live "/opportunity_exclusions/:id/edit", AdminOpportunityExclusionLive.Index, :edit

          live "/apify-actor-runs", AdminApifyActorRunsLive.Index, :index
          live "/apify-actor-runs/:id", AdminApifyActorRunsLive.Show, :show

          live "/waitlist_requests", AdminWaitlistRequestLive.Index, :index
        end
      end
    end
  end
end
