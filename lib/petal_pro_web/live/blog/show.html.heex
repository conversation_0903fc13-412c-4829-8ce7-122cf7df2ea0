<%= if @live_action in [:new, :edit, :publish] do %>
  <.container max_width="md" class="py-16">
    <.live_component
      module={PetalProWeb.AdminPostLive.FormComponent}
      id={@post.id || :new}
      action={@live_action}
      post={@post}
      return_to={~p"/blog/#{@post.slug}"}
    />
  </.container>
<% else %>
  <.container max_width="sm" class="py-16">
    <div class="grid gap-8">
      <div class="flex justify-between">
        <.breadcrumbs
          separator="chevron"
          links={[
            %{
              to: ~p"/blog",
              icon: "hero-signal",
              label: gettext("Blog"),
              link_type: "live_redirect"
            },
            %{
              label: @post.published_category || gettext("This post"),
              to: ~p"/blog/#{@post.published_slug}",
              link_type: "live_redirect"
            }
          ]}
        />

        <.button
          :if={@current_user && @current_user.role == :admin}
          link_type="live_redirect"
          label={gettext("Edit Post")}
          to={~p"/admin/posts/#{@post}/show/edit"}
        />
      </div>

      <.p :if={@post.published_duration} class="-mb-8 text-xs uppercase">
        Reading Time - {formatted_duration(@post)}
      </.p>

      <.page_header title={@post.published_title} class="!mb-0" />

      <div class="-mx-8 grid gap-3">
        <.p class="m-0">
          <img :if={@post.published_cover} src={@post.published_cover} class="w-full rounded-3xl" />
        </.p>
        <.p :if={@post.published_cover_caption} class="m-0 text-center text-sm text-gray-500">
          {@post.published_cover_caption}
        </.p>
      </div>

      <.p :if={@post.published_summary} class="mb-0 font-semibold">
        {@post.published_summary}
      </.p>

      <.pretty_content json={@post.published_content} />

      <div class="mt-16 flex items-center gap-4">
        <.avatar size="xl" src={user_avatar_url(@post.author)} /> {user_name(@post.author)}
      </div>
    </div>
  </.container>
<% end %>
