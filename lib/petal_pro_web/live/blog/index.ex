defmodule PetalProWeb.BlogLive.Index do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalComponents.Card
  import PetalProWeb.PageComponents

  alias PetalPro.Posts
  alias PetalPro.Posts.Post

  @impl true
  def mount(_params, _session, socket) do
    {:ok, stream(socket, :posts, Posts.list_live_posts())}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Post")
    |> assign(:post, Posts.get_post!(id))
  end

  defp apply_action(socket, :new, _params) do
    current_user = socket.assigns.current_user

    socket
    |> assign(:page_title, "New Post")
    |> assign(:post, %Post{author_id: current_user.id})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Blog")
    |> assign(:post, nil)
  end

  @impl true
  def handle_info({PetalProWeb.PostLive.FormComponent, {:saved, post}}, socket) do
    {:noreply, stream_insert(socket, :posts, post)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    post = Posts.get_post!(id)
    {:ok, _} = Posts.delete_post(post)

    socket = put_flash(socket, :info, "Post deleted")

    {:noreply, stream_delete(socket, :posts, post)}
  end

  @impl true
  def handle_event("close_modal", _, socket) do
    {:noreply, push_patch(socket, to: ~p"/blog")}
  end

  attr :class, :any

  defp waiting_for_you(assigns) do
    ~H"""
    <svg
      xmlns="http://www.w3.org/2000/svg"
      data-name="Layer 1"
      width="1055.52347"
      height="852.05558"
      viewBox="0 0 1055.52347 852.05558"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      class={@class}
    >
      <title>Waiting for you</title>
      <ellipse cx="502.36284" cy="821.88969" rx="250.30368" ry="8.93942" fill="#3f3d56" /><circle
        cx="385.68162"
        cy="332.88807"
        r="89.56313"
        fill="#ff6584"
      /><ellipse cx="280.15859" cy="791.48141" rx="75.48575" ry="6.95811" fill="#3f3d56" /><path
        d="M489.91461,846.47337s62.65145-167.07054,28.11283-269.88318-61.84823-98.79652-61.84823-98.79652-46.587-12.04835-104.41908,23.29349-73.09336,45.78375-106.02554,31.32573-39.358-36.9483-119.68033-34.53863c0,0,42.57085-23.29348,84.33849-2.40967s69.88046,32.12895,112.45132,0S424.05026,421.568,481.07915,446.468c0,0-41.76764-40.96442-55.42244-40.96442s-77.9127-6.42579-98.79652-28.11283c0,0-29.71928-20.88381-82.732,30.52251s-40.16119,51.40632-87.55139,47.3902S72.23827,427.9938,72.23827,427.9938s55.42243,22.49026,89.15783,11.24513,62.65145-53.816,92.37073-68.274,47.3902-33.73539,87.55139-12.85158c0,0-16.8677-69.07724-48.99665-63.45467s15.26125-16.06448,15.26125-16.06448-8.83546-117.27066-25.70316-125.3029-6.42579-28.916-37.75151-32.93217c0,0,38.55474-4.81935,45.78375,21.687s57.02888,169.48021,57.02888,169.48021,30.52251,61.045,64.2579,46.587-12.85158-95.58362-12.85158-95.58362L340.515,236.02333s43.37409-1.60645,69.88047,23.29348,28.11283,105.22231,28.11283,105.22231,66.66757,9.63869,82.732,45.78376c0,0,10.4419-32.12895-9.63869-51.40632-5.02015-4.81934-10.2913-13.203-15.3742-23.06758-19.438-37.72422-26.0323-80.72643-19.91733-122.72121,2.99238-20.55024,4.834-40.64371,1.55613-45.37846-7.229-10.44191-37.75151-74.69981-40.16118-91.56751s2.40967-52.20954,2.40967-52.20954.80322,43.37408,10.44191,57.02888,36.94829,81.1256,36.94829,81.1256,0,85.14172,22.49026,126.90935,65.86435,88.35461,63.45468,118.87712v17.67092s48.19342-45.78375,44.98053-61.045-7.229-51.40632-37.75152-73.09336S530.0758,235.2201,551.76284,185.42023s21.687-58.63533,21.687-58.63533l-14.458,61.84823s-8.03224,55.42243,14.458,74.6998,52.20954,54.61922,52.20954,54.61922l-6.42579-115.66422,20.88382,45.78375s44.98053-57.83211,97.9933-77.10948S824.05569,84.214,824.05569,84.214l44.98053-35.34185L832.08792,93.0495s-11.24513,52.20954-74.6998,89.15784S652.969,273.77484,650.55936,307.51024,645.74,370.96491,645.74,370.96491s118.87711-4.01611,145.38349-56.22566,28.91606-155.8254,54.61922-172.6931,73.89658-37.75152,73.89658-37.75152l-53.816,44.17731s-11.24513,36.14507-8.83546,61.84823S839.31694,260.12,839.31694,260.12l-15.26125,44.98053L807.188,343.65531s69.07724,1.60644,105.22231,42.57086,126.10612,53.01276,126.10612,53.01276l-50.60309,13.65481S939.7199,436.82926,893.93615,407.11s-40.96441-56.22566-97.9933-26.50638-134.94158,54.61921-134.94158,54.61921S705.9818,414.339,787.10739,464.94209s-40.16118-8.83546-76.30625,10.44191-53.01277-4.81934-67.4708,43.37408-40.96441,179.92212-10.4419,253.8187l30.5225,73.89659Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#3f3d56"
      /><path
        d="M792.34193,314.73922c-21.53454,42.41677-104.03814,53.02354-134.23047,55.51024-.84665,10.88865-1.40489,17.7746-1.40489,17.7746S775.5837,384.0079,802.09,331.79836s28.9161-155.8254,54.61934-172.6931c2.72588-1.78887,5.7067-3.62311,8.85412-5.47266.88295-3.24187,1.47837-5.16067,1.47837-5.16067l53.816-44.1773s-48.19331,20.88379-73.89654,37.75149S818.84838,262.52969,792.34193,314.73922Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M653.82615,233.59276c-.17537,19.776-2.74209,31.538-2.74209,31.538s44.96169-57.80737,97.96-77.097c3.13225-1.98157,6.31-3.93256,9.56245-5.82645,63.45472-36.94834,74.6999-89.15788,74.6999-89.15788l36.94828-44.1773L825.27412,84.214s-32.93219,67.47081-85.9448,86.74816C703.19349,184.10241,670.79559,215.15179,653.82615,233.59276Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M584.41647,424.97233v17.67092s48.19345-45.78373,44.98044-61.045c-2.457-11.67126-5.39881-35.55224-20.29224-55.84417,6.71326,15.335,8.75655,30.29325,10.54416,38.785,2.304,10.94376-21.82519,37.58133-35.66283,51.77819A36.62966,36.62966,0,0,1,584.41647,424.97233Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M519.76941,303.70533c.38168.81408.77525,1.61514,1.19233,2.38987,12.31718,22.87472,30.88681,47.19025,44.78053,69.52548C554.5133,353.5018,534.97163,328.2085,519.76941,303.70533Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M558.92846,269.50927c-6.4772-15.87053-7.54443-34.755-.10308-56.88411a173.59159,173.59159,0,0,1,1.385-23.992l12.64754-54.10334c-2.33186,7.80221-7.66715,22.854-19.87664,50.8904C537.52467,220.91331,543.29325,248.64809,558.92846,269.50927Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M913.62886,386.22612c-25.06676-28.409-65.96552-37.88655-88.68555-41.02891l-6.78867,15.51718s69.07724,1.60651,105.2223,42.57086c20.044,22.71661,56.63273,36.53737,85.257,44.346l31.101-8.39227S949.77391,427.19055,913.62886,386.22612Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M422.16518,375.17244c33.73541-14.45805-12.85147-95.58362-12.85147-95.58362l-1.27443-.58412c11.61436,24.50481,28.45871,68.78824,4.37782,79.1086-22.5332,9.65713-43.63-14.36887-55.15808-31.39127.41856,1.20393.64837,1.86346.64837,1.86346S388.42978,389.63049,422.16518,375.17244Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M532.20693,427.382s10.1653-31.33364-8.93133-50.67956c4.6829,16.66623-.81675,33.62042-.81675,33.62042-12.4271-27.96079-55.12834-40.05739-73.83214-44.10292.72543,9.30906.84814,15.37833.84814,15.37833S516.14244,391.23693,532.20693,427.382Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M572.85788,134.5298c1.81006-6.05648,1.81051-7.74495,1.81051-7.74495Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M171.87524,456.45151c.162-.05213.32649-.09981.48743-.15343,33.7354-11.24518,62.65151-53.81605,92.37083-68.274s47.39009-33.73541,87.55122-12.85162c0,0-7.02606-28.7597-20.64148-47.65448a187.3908,187.3908,0,0,1,10.89341,30.59534c-40.16114-20.88378-57.83206-1.60643-87.55123,12.85162-29.71932,14.458-58.63542,57.02884-92.37083,68.274s-89.15781-11.24519-89.15781-11.24519,36.94827,23.29351,84.33851,27.3096C163.21641,455.76275,167.84205,456.16815,171.87524,456.45151Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M506.749,846.47337c14.71731-45.116,50.11121-169.87381,22.24509-252.8241-12.03412-35.82272-23.18857-58.66584-32.61819-73.23894,7.0518,13.7954,14.76164,32.043,22.87011,56.17981C553.7846,679.40278,491.1331,846.47337,491.1331,846.47337Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M193.06752,504.335a81.99255,81.99255,0,0,1,28.29182,8.18873c32.98678,16.49331,57.46154,26.9535,87.26282,14.80484,6.78881-3.58629,14.38234-8.02413,23.13948-13.30751.68274-.49822,1.35893-.97643,2.0491-1.49733,42.57079-32.129,101.20621-73.89662,158.2349-48.99668,0,0-11.52572-11.30089-24.44576-22.03179-52.27082-12.82268-104.6175,24.59591-143.53722,53.96933-42.57094,32.129-70.68367,20.88379-112.4514,0s-84.3385,2.40965-84.3385,2.40965C158.30727,496.94316,178.305,499.73114,193.06752,504.335Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M274.59744,145.49371a21.01179,21.01179,0,0,0,3.30316,4.22284A23.705,23.705,0,0,0,274.59744,145.49371Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M421.362,276.376a39.10828,39.10828,0,0,1,3.48224,3.75437c-3.51854-8.45832-7.86364-15.77227-13.23032-20.8135-26.50631-24.9-69.88046-23.29351-69.88046-23.29351l54.39563,24.93133A77.67982,77.67982,0,0,1,421.362,276.376Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M479.0847,167.74928c3.278,4.73474,1.43627,24.82825-1.55616,45.37848-6.11486,41.99478.4794,84.997,19.91739,122.72119a142.93383,142.93383,0,0,0,8.55858,14.69181c-18.50924-37.151-24.71543-79.23377-18.7279-120.35386,2.99243-20.55023,4.83418-40.64374,1.55616-45.37848-7.22895-10.44189-37.75149-74.69983-40.16114-91.56754a116.89363,116.89363,0,0,1-.5032-19.7093c-6.23935-17.70312-6.83522-49.55937-6.83522-49.55937s-4.8193,35.34191-2.40965,52.20954S471.85574,157.30739,479.0847,167.74928Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M554.43937,56.70657a22.98529,22.98529,0,0,0-1.45813-4.62147S553.59852,53.81863,554.43937,56.70657Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M270.73739,137.60166c7.079.86658,15.22185,3.03854,21.4331,8.07567-.40518-1.37038-.75859-2.59885-1.04-3.63121-7.2291-26.50638-45.7838-21.687-45.7838-21.687C262.77008,122.59288,267.27047,130.04517,270.73739,137.60166Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.1"
      /><path
        d="M470.37569,812.15391c5.72694,21.17024,25.343,34.2815,25.343,34.2815s10.33087-21.21248,4.60392-42.38271-25.343-34.2815-25.343-34.2815S464.64874,790.98367,470.37569,812.15391Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#3f3d56"
      /><path
        d="M478.791,807.6042c15.71483,15.29775,17.63428,38.81395,17.63428,38.81395s-23.55932-1.28635-39.27414-16.5841-17.63428-38.814-17.63428-38.814S463.07621,792.30645,478.791,807.6042Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M426.70525,233.61267C425.33909,255.50126,408.75,272.2792,408.75,272.2792s-14.37407-18.71048-13.0079-40.59908,17.95522-38.66652,17.95522-38.66652S428.07142,211.72408,426.70525,233.61267Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M253.779,95.23684c18.55193,11.69627,25.31859,34.29955,25.31859,34.29955s-23.312,3.63987-41.86388-8.0564-25.31859-34.29956-25.31859-34.29956S235.2271,83.54056,253.779,95.23684Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M212.3497,362.09045C230.90162,373.78673,237.66828,396.39,237.66828,396.39s-23.31195,3.63987-41.86388-8.05641-25.31859-34.29955-25.31859-34.29955S193.79777,350.39418,212.3497,362.09045Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M311.049,464.44526c18.55193,11.69628,25.31858,34.29956,25.31858,34.29956s-23.312,3.63987-41.86388-8.0564-25.31858-34.29956-25.31858-34.29956S292.49705,452.749,311.049,464.44526Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M589.50244,134.24869c-5.59474,21.20556-25.12864,34.439-25.12864,34.439s-10.463-21.14761-4.86828-42.35317,25.12865-34.439,25.12865-34.439S595.09719,113.04313,589.50244,134.24869Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M765.98589,150.78317c-6.57575,20.92215-26.70446,33.23195-26.70446,33.23195s-9.46731-21.61171-2.89155-42.53386,26.70445-33.23195,26.70445-33.23195S772.56164,129.861,765.98589,150.78317Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M956.73676,383.63692c-11.91153,18.41446-34.59208,24.91744-34.59208,24.91744s-3.36814-23.35276,8.54338-41.76723,34.59208-24.91743,34.59208-24.91743S968.64828,365.22246,956.73676,383.63692Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M657.27347,534.21122s-1.91331,7.01549,1.91332,9.56657,5.10217,14.66875,5.10217,14.66875L656.6357,579.493l-20.4087,3.82663-7.65326-14.66875V546.96665s2.55109-10.84212,1.91332-14.031S657.27347,534.21122,657.27347,534.21122Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#ffb9b9"
      /><path
        d="M657.27347,534.21122s-1.91331,7.01549,1.91332,9.56657,5.10217,14.66875,5.10217,14.66875L656.6357,579.493l-20.4087,3.82663-7.65326-14.66875V546.96665s2.55109-10.84212,1.91332-14.031S657.27347,534.21122,657.27347,534.21122Z"
        transform="translate(-72.23827 -23.97221)"
        opacity="0.2"
      /><path
        d="M677.68217,654.75007l16.58206,3.18886s1.27554,18.49538,0,22.95978-2.55109,9.56658-1.27554,10.84212-5.74,33.8019-5.10218,35.71521,1.91332,7.65326,1.27555,11.47989-2.55109,2.55109-1.27555,6.37772,2.55109,4.4644,1.27555,7.01549-7.65326,64.41494-9.56658,65.05271-19.13315,1.27555-19.13315,0,5.73994-10.84211,3.82663-13.3932.63777-45.91956.63777-47.83288,3.82663-5.10217,1.91332-6.37772-3.18886-2.55108-2.55109-4.4644,1.91331-2.55108.63777-4.4644-1.27554-40.17962-5.73994-41.45516c0,0-5.74,28.062-3.82663,35.07744s-.63778,9.56658-.63778,9.56658l8.92881,7.65326-12.11766,22.95978L638.77809,759.9824s0-3.82663-2.55109-4.4644-3.82663-1.27554-3.82663-3.18886-4.4644-14.031-4.4644-14.031-12.75543-40.17961-6.37771-60.58831,8.8347-19.73317,8.8347-19.73317Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#2f2e41"
      /><polygon
        points="615.648 762.797 622.664 770.45 620.113 791.496 614.373 791.496 605.444 771.725 615.648 762.797"
        fill="#ffb9b9"
      /><path
        d="M692.35091,800.79979s-2.55108,5.74-4.4644,4.4644-2.54752-3.18886-5.41927-3.18886-5.42285.63778-4.78507,1.91332,8.291,15.30652,8.291,15.30652a18.02808,18.02808,0,0,1,0,15.30652c-3.82663,8.291,10.20434,12.75543,12.75543,3.82663s3.18886-19.13315,2.55109-22.95978,3.18886-23.59755,3.18886-23.59755-10.3031-4.74977-13.44258-4.926C691.026,786.945,693.62646,798.2487,692.35091,800.79979Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#2f2e41"
      /><path
        d="M675.13108,814.83077l2.55109,7.65326s0,7.65326,2.55108,11.47989,5.10218,12.11766-5.10217,12.75543-14.66875-3.18886-14.66875-3.18886,1.91331-5.10217,0-7.01548,0-5.73995,0-5.73995l1.91331-15.30652Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#2f2e41"
      /><path
        d="M657.91124,751.0536h5.74l28.69972,37.62853s-5.10217,12.11766-12.75543,13.3932c0,0-1.27554-.63777-7.01549-4.4644s-21.04646-22.95978-21.04646-22.95978L649.62021,763.809Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#2f2e41"
      /><circle cx="571.642" cy="498.12135" r="20.40869" fill="#ffb9b9" /><path
        d="M668.75336,548.2422l-6.37772-2.55109s-.63777,22.322-12.75543,22.322-24.8731-3.18886-24.8731-9.56658-10.20434,11.47989-10.20434,11.47989,9.56657,63.77717,8.9288,64.41495,7.01549,34.43967,7.01549,34.43967,32.52636,1.27554,43.36847-3.18886,14.031-12.75544,14.031-12.75544l-4.4644-56.12391-8.9288-43.36847Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M657.91493,542.50225s2.5474-2.55109,6.374,0,27.42418,8.291,27.42418,10.20435v24.23532s.63777,3.82663-1.27554,6.37772-6.37772,12.11766-3.82663,16.58206,12.11766,56.12391,10.20435,62.50163-7.01549,12.11766-7.01549,12.11766-27.42419-58.03722-27.42419-74.61929.26616-36.92547.26616-36.92547Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#575a89"
      /><path
        d="M685.9732,553.34437h5.73994s21.68424,41.45516,21.68424,49.74619-14.031,54.2106-15.30652,54.84837-5.10217-7.01549-5.10217-7.01549l-3.18886-25.51087s6.37771-15.30652,4.4644-17.85761-11.47989-22.322-11.47989-22.322Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#575a89"
      /><path
        d="M629.51692,542.50225s-7.32089,5.10217-8.59644,7.01549-20.40869,12.75543-21.04646,15.30652,14.66875,24.23532,14.66875,24.23532,6.37771,35.71522,6.37771,38.26631,2.55109,45.91956,2.55109,45.91956,26.14864,7.65326,26.14864,3.18886.63777-68.24157-3.18886-82.27255S629.51692,542.50225,629.51692,542.50225Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#575a89"
      /><path
        d="M633.67592,674.521s21.68424,29.3375,10.20434,28.69973-19.13315-25.51087-19.13315-25.51087Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#ffb9b9"
      /><path
        d="M607.52728,562.91094l-7.65326,1.91332s-6.37772,56.12391-2.55109,63.77717,24.23533,57.39945,24.23533,55.48614,17.95311-5.32362,17.26758-7.12621-4.51215-9.45586-5.78769-12.00694-3.18886-5.74-2.55109-8.92881-7.01549-16.83817-7.01549-16.83817-1.91331-8.03492-2.55109-11.86155-6.37771-45.91957-6.37771-45.91957Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#575a89"
      /><path
        d="M663.8906,506.295a5.07147,5.07147,0,0,0-2.30937-2.83725,10.86663,10.86663,0,0,0-.4876-3.74431c-1.27037-3.535-4.27984-3.33236-7.11936-4.89523-1.232-.67808-1.15586-1.27739-1.79719-2.39112a9.154,9.154,0,0,0-3.633-3.48894,13.51113,13.51113,0,0,0-5.89982-2.35585c-4.529-.29055-8.00135,3.719-12.33005,5.00579-1.908.56716-3.95817.59054-5.87622,1.12674s-3.83167,1.81652-4.11753,3.68048c-.132.86053.10085,1.74332-.033,2.60361-.2661,1.70987-1.85988,2.90739-2.9268,4.31377-2.35935,3.11-2.08949,7.50359-.20272,10.88741a15.22051,15.22051,0,0,0,1.3605,2.00391,11.79459,11.79459,0,0,0,1.43647,4.57764c1.88677,3.38382,5.13345,5.9124,8.525,8.01316a17.89627,17.89627,0,0,1-.50306-3.417,2.22989,2.22989,0,0,1,.15437-1.13217c.37771-.74316,1.37574-.9408,2.1367-1.34781a4.98194,4.98194,0,0,0,2.23976-4.41443c.04562-1.70576-.33848-3.41434-.14861-5.11123a2.72956,2.72956,0,0,1,1.01575-2.05441,3.68529,3.68529,0,0,1,1.915-.42974c3.22931-.124,6.60342-.17985,9.45186-1.61723a14.36252,14.36252,0,0,1,2.94191-1.40075c2.42072-.59417,4.89483,1.00282,6.28995,2.95736s2.09069,4.28046,3.37787,6.29956,3.51089,3.81966,5.9996,3.58045a1.02434,1.02434,0,0,0,.74324-.31108.99817.99817,0,0,0,.13692-.596l.139-8.9542A12.77455,12.77455,0,0,0,663.8906,506.295Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#2f2e41"
      /><ellipse cx="930.04641" cy="843.11616" rx="125.47705" ry="8.93942" fill="#3f3d56" /><path
        d="M958.36552,846.137s-10.71231,9.0209-16.35036,8.45709-16.35037-.56381-11.83992,5.63806,23.116,9.58469,31.00931,10.1485,25.37126,1.12761,24.24365-2.25522-2.25523-19.1694-6.76567-20.297S958.36552,846.137,958.36552,846.137Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#575a89"
      /><path
        d="M1019.84842,837.28021s-7.89376,11.568-13.47591,12.5402-15.90033,3.85121-9.889,8.61268,24.84151,3.01947,32.59591,1.44116,24.74085-5.73258,22.74556-8.6879-7.32415-17.858-11.9717-17.73192S1019.84842,837.28021,1019.84842,837.28021Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#575a89"
      /><path
        d="M952.09041,671.84147s-7.25623,49.69448-5.56481,58.71537,2.819,23.116,2.819,23.116-1.12761,11.83992,0,17.478,7.89328,59.1996,7.89328,60.32722-1.12761,4.51044,0,7.32947,3.38283.56381,0,5.07425-1.12761,5.63806-.56381,5.63806,22.55223,2.25522,23.67984,0-.5638-2.819,0-5.63806,1.12762-3.38283,2.25523-3.94664,3.38283.56381,1.69141-2.25522-5.63805-6.20186-4.51044-7.89328-.56381-14.09514-.56381-14.09514-1.12761-27.62648-1.69141-44.54066,5.07425-34.39215,5.07425-34.39215a64.50785,64.50785,0,0,0,14.659,37.775c14.65894,17.478,21.98842,50.17871,21.98842,50.17871s3.38283,5.07425,1.69142,9.58469-6.76567,1.69142-1.69142,4.51045,22.55223-1.12761,24.24364-2.819-2.25522-3.94664-1.12761-8.45708,5.63806-3.38284,1.69142-5.63806-5.07425-1.69142-5.07425-3.94664-10.1485-51.30632-25.93506-65.40147l-1.12762-51.30632s14.09515-36.08356-.5638-39.4664S952.09041,671.84147,952.09041,671.84147Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#2f2e41"
      /><path
        d="M962.31216,538.29909s9.5847,23.67984,7.32948,27.06268,27.62648-11.27612,27.62648-11.27612-9.5847-23.67984-9.5847-26.49887S962.31216,538.29909,962.31216,538.29909Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#a0616a"
      /><circle cx="897.12147" cy="499.38603" r="20.86081" fill="#a0616a" /><path
        d="M997.26812,544.501s-14.659-1.69142-21.98843,7.32947-8.45708,11.27611-8.45708,11.27611-17.478,28.19029-14.09515,42.28543a40.78305,40.78305,0,0,1,2.25523,3.94664c1.12761,2.25523,1.12761,33.82835,0,38.9026s-2.25523,6.76567-1.12761,14.09514-9.0209,19.16939,2.819,21.42462,20.86082.5638,24.80746-4.51045.5638-8.45708,12.96753-8.45708a29.835,29.835,0,0,0,20.297-8.45709s-6.20186-6.76567-3.94664-7.89328,0-3.94664-1.12761-6.76567-1.12761-5.07425.56381-5.07425.5638-1.12761-.56381-3.94664,19.7332-51.30632,7.32948-65.40146-18.60559-21.98843-18.60559-21.98843S1000.651,542.80954,997.26812,544.501Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M965.13119,598.0625V622.87l-18.04178,59.76341s-11.83992,31.00931,0,31.00931S958.295,684.0455,958.295,684.0455l25.4418-44.82518s10.1485-35.51976,9.02089-38.33879S965.13119,598.0625,965.13119,598.0625Z"
        transform="translate(-72.23827 -23.97221)"
        fill="#a0616a"
      /><path
        d="M974.71589,561.41513s-16.91417,6.76567-12.40373,24.80745,0,17.478,0,17.478,23.116-2.25523,30.44551,2.819l1.69142-6.76566S1001.21476,560.85132,974.71589,561.41513Z"
        transform="translate(-72.23827 -23.97221)"
        fill=""
      /><path
        d="M962.23652,507.69756a8.3233,8.3233,0,0,1-7.81835-1.21664,6.29832,6.29832,0,0,1-1.77652-7.43529c1.07954-2.11867,3.38862-3.281,5.60284-4.14784A52.07494,52.07494,0,0,1,973.5051,491.452c2.49409-.17738,5.11352-.14928,7.33924.99008,1.10128.56374,2.0578,1.37737,3.14849,1.96134,2.00551,1.07378,4.33928,1.31252,6.50663,2.00363a16.286,16.286,0,0,1,9.95284,9.14876,19.62412,19.62412,0,0,1,1.226,4.54149,25.2961,25.2961,0,0,1-1.3156,12.66327c-2.40334,6.39891-7.444,11.99316-7.72846,18.82259-3.08523-2.23834-4.22927-6.27842-6.70163-9.17948a12.48588,12.48588,0,0,0-7.12621-4.13344,3.96909,3.96909,0,0,1-2.12417-.77452,3.41973,3.41973,0,0,1-.77575-1.80982l-2.111-9.96715c-.42962-2.02845-.97254-4.24-2.63979-5.47267a7.98792,7.98792,0,0,0-3.44066-1.21215,53.97319,53.97319,0,0,0-8.81411-.80365"
        transform="translate(-72.23827 -23.97221)"
        fill="#2f2e41"
      />
    </svg>
    """
  end
end
