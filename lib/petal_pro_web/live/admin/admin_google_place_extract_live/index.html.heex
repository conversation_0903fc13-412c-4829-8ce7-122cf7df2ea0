<.admin_layout current_page={:admin_google_place_extracts} current_user={@current_user}>
  <.page_header title="Listing Place Extracts">
    <.button
      link_type="live_patch"
      label="New Place Extract"
      to={~p"/admin/google/place_extracts/new"}
    />
  </.page_header>

  <%= if @live_action in [:new, :edit] do %>
    <.modal title={@page_title}>
      <.live_component
        module={PetalProWeb.AdminGooglePlaceExtractLive.FormComponent}
        id={@place_extract.id || :new}
        action={@live_action}
        place_extract={@place_extract}
        current_user={@current_user}
        return_to={current_index_path(@index_params)}
      />
    </.modal>
  <% end %>

  <.data_table :if={@index_params} meta={@meta} items={@place_extracts}>
    <:if_empty>No place extracts found</:if_empty>
    <:col :let={place_extract} field={:state_id} sortable label={gettext("Location")}>
      <.link
        navigate={~p"/admin/google/place_extracts/#{place_extract}"}
        class="font-bold underline"
      >
        {place_extract.state.name}, {place_extract.state.country_code}
      </.link>
    </:col>
    <:col :let={place_extract} field={:interval} sortable>
      {place_extract.interval} {Phoenix.Naming.humanize(place_extract.interval_type)}
    </:col>
    <:col field={:is_enabled} sortable renderer={:checkbox} label={gettext("Enabled?")} />
    <:col :let={place_extract} field={:status} sortable>
      {Phoenix.Naming.humanize(place_extract.status)}
    </:col>
    <:col :let={place_extract} label={gettext("Actions")}>
      <.place_extract_actions socket={@socket} place_extract={place_extract} />
    </:col>
  </.data_table>
</.admin_layout>
