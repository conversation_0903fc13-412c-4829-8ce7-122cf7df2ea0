defmodule PetalProWeb.AdminGooglePlaceExtractLive.FormComponent do
  @moduledoc false
  use PetalProWeb, :live_component

  alias AshPhoenix.Form
  alias Boring.Google
  alias Boring.Locations

  @model "Place Extract"

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_location_options()
      |> assign_interval_types()
      |> assign_form()

    {:ok, socket}
  end

  @impl true
  def handle_event("validate", %{"form" => form_data}, socket) do
    socket =
      update(socket, :form, fn form ->
        Form.validate(form, form_data)
      end)

    {:noreply, socket}
  end

  def handle_event("save", %{"form" => form_data}, socket) do
    case Form.submit(socket.assigns.form, params: form_data) do
      {:ok, _place_extract} ->
        socket =
          socket
          |> put_flash(
            :success,
            gettext("%{model} saved successfully", model: gettext(@model))
          )
          |> push_navigate(to: socket.assigns.return_to)

        {:noreply, socket}

      {:error, form} ->
        socket =
          socket
          |> put_flash(:error, gettext("Unable to save %{model}", model: gettext(@model)))
          |> assign(:form, form)

        {:noreply, socket}
    end
  end

  def handle_event("combo_box_search", payload, socket) do
    results =
      [actor: socket.assigns.current_user]
      |> Locations.list_states!()
      |> Enum.filter(fn state ->
        String.contains?(String.downcase(state.name), String.downcase(payload))
      end)
      |> to_location_options()

    {:reply, %{results: results}, socket}
  end

  defp assign_form(%{assigns: %{place_extract: place_extract}} = socket) do
    form =
      if place_extract.id do
        Google.form_to_update_place_extract(place_extract, actor: socket.assigns.current_user)
      else
        Google.form_to_create_place_extract(actor: socket.assigns.current_user)
      end

    assign(socket, form: to_form(form))
  end

  defp assign_interval_types(socket) do
    assign(socket, interval_types: Google.Types.ExtractIntervalType.values())
  end

  defp assign_location_options(socket) do
    states = Locations.list_states!(actor: socket.assigns.current_user)
    assign(socket, locations: to_location_options(states))
  end

  defp to_location_options(states) do
    Enum.map(
      states,
      &to_location_option/1
    )
  end

  defp to_location_option(%Locations.State{} = state) do
    {"#{state.name}, #{state.country_code}", state.id}
  end
end
