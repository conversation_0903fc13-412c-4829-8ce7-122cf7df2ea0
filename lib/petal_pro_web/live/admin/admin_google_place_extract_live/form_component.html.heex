<div>
  <.form
    for={@form}
    id="place_extract-form"
    phx-target={@myself}
    phx-change="validate"
    phx-submit="save"
  >
    <div class="md:grid md:grid-cols-2 md:gap-4">
      <.combo_box
        label={gettext("Location")}
        tom_select_plugins={%{remove_button: false}}
        max_items={1}
        remote_options_event_name="combo_box_search"
        remote_options_target={@myself}
        placeholder="Select a location.."
        options={@locations}
        field={@form[:state_id]}
        wrapper_class="col-span-2"
      />

      <.field type="number" field={@form[:interval]} />

      <.field
        type="select"
        field={@form[:interval_type]}
        options={@interval_types}
        prompt="Choose a an interval type"
      />

      <.field wrapper_class="col-span-2" type="text" field={@form[:last_actor_run_id]} />

      <.field
        wrapper_class="col-span-2"
        type="switch"
        field={@form[:is_enabled]}
        label={gettext("Enabled?")}
      />

      <div class="col-span-2 flex justify-end">
        <.button type="submit" phx-disable-with="Saving..." label={gettext("Save")} />
      </div>
    </div>
  </.form>
</div>
