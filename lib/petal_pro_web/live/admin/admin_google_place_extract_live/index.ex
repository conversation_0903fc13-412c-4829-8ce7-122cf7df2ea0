defmodule PetalProWeb.AdminGooglePlaceExtractLive.Index do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalProWeb.AdminLayoutComponent
  import PetalProWeb.DataTable
  import PetalProWeb.PageComponents

  alias Boring.Google
  alias Boring.Google.PlaceExtract

  require Logger

  @model "Place Extract"
  @model_plural "Place Extracts"

  @data_table_opts [
    default_limit: 10,
    default_order: %{
      order_by: [:id, :inserted_at],
      order_directions: [:asc, :desc]
    }
  ]

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign(socket, index_params: nil)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit #{@model}")
    |> assign(:place_extract, Google.get_place_extract!(id, actor: socket.assigns.current_user))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New #{@model}")
    |> assign(:place_extract, %PlaceExtract{})
  end

  defp apply_action(socket, :index, params) do
    socket
    |> assign(:page_title, "Listing #{@model_plural}")
    |> assign_place_extracts(params)
    |> assign(index_params: params)
  end

  defp current_index_path(index_params) do
    ~p"/admin/google/place_extracts?#{index_params || %{}}"
  end

  @impl true
  def handle_event("update_filters", params, socket) do
    query_params = PetalProWeb.DataTable.build_filter_params(socket.assigns.meta.flop, params)
    {:noreply, push_patch(socket, to: ~p"/admin/google/place_extracts?#{query_params}")}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    :ok = Google.delete_place_extract(id, actor: socket.assigns.current_user)

    socket =
      socket
      |> assign_place_extracts(socket.assigns.index_params)
      |> put_flash(:info, "#{@model} deleted")

    {:noreply, socket}
  end

  @impl true
  def handle_event("close_modal", _, socket) do
    {:noreply, push_patch(socket, to: current_index_path(socket.assigns.index_params))}
  end

  @impl true
  def handle_event("ingest", %{"id" => id}, socket) do
    extract = Boring.Google.get_place_extract!(id, actor: socket.assigns.current_user)
    Boring.Google.ingest_extract_data!(extract, actor: socket.assigns.current_user)
    {:noreply, put_flash(socket, :info, "Ingestion started")}
  end

  @impl true
  def handle_event("run", %{"id" => id}, socket) do
    Oban.insert(Boring.Workers.ApifyGooglePlaceIngestWorker.new(%{id: id}))
    {:noreply, put_flash(socket, :info, "Submitted run to Apify")}
  end

  @impl true
  def handle_event("cancel", %{"id" => id}, socket) do
    place_extract = Google.get_place_extract!(id)

    case Google.Apify.abort_actor_run(place_extract.last_actor_run_id) do
      {:ok, %Tesla.Env{}} ->
        {:noreply, put_flash(socket, :info, "Run cancelled")}

      {:error, %Tesla.Env{} = error} ->
        Logger.warning("Failed to cancel the actor run #{id}: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "Failed to cancel run")}
    end
  end

  defp assign_place_extracts(socket, params) do
    starting_query = PlaceExtract

    {place_extracts, meta} =
      PetalProWeb.DataTable.search(starting_query, params, @data_table_opts)

    assign(socket, place_extracts: Ash.load!(place_extracts, :state), meta: meta)
  end

  defp place_extract_actions(assigns) do
    ~H"""
    <div class="flex items-center" id={"place_extracts_actions_container_#{@place_extract.id}"}>
      <.dropdown
        class="dark:shadow-lg"
        options_container_id={"place_extracts_options_#{@place_extract.id}"}
        menu_items_wrapper_class="dark:border dark:border-gray-600"
      >
        <.dropdown_menu_item
          :if={not is_nil(@place_extract.last_actor_run_id)}
          phx-click="ingest"
          phx-value-id={@place_extract.id}
        >
          <.icon name="hero-x-circle" class="h-5 w-5" /> {gettext("Ingest")}
        </.dropdown_menu_item>
        <.dropdown_menu_item
          :if={@place_extract.status in [:not_started, :ingested, :error]}
          phx-click="run"
          phx-value-id={@place_extract.id}
        >
          <.icon name="hero-play" class="h-5 w-5" /> {gettext("Run")}
        </.dropdown_menu_item>
        <.dropdown_menu_item
          :if={@place_extract.status == :running}
          phx-click="cancel"
          phx-value-id={@place_extract.id}
        >
          <.icon name="hero-x-circle" class="h-5 w-5" /> {gettext("Cancel")}
        </.dropdown_menu_item>
        <.dropdown_menu_item
          link_type="live_patch"
          to={~p"/admin/google/place_extracts/#{@place_extract}/edit"}
        >
          <.icon name="hero-pencil" class="h-5 w-5" /> {gettext("Edit")}
        </.dropdown_menu_item>

        <.dropdown_menu_item
          link_type="a"
          to="#"
          phx-click="delete"
          phx-value-id={@place_extract.id}
          data-confirm={gettext("Are you sure?")}
        >
          <.icon name="hero-trash" class="h-5 w-5" /> {gettext("Delete")}
        </.dropdown_menu_item>
      </.dropdown>
    </div>
    """
  end
end
