defmodule PetalProWeb.AdminGooglePlaceExtractLive.Show do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalProWeb.AdminLayoutComponent

  alias Boring.Google

  @model "Place Extract"

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def handle_params(%{"id" => id}, _, socket) do
    place_extract =
      Google.get_place_extract!(id, actor: socket.assigns.current_user, load: [:state])

    {:noreply,
     socket
     |> assign(:page_title, page_title(socket.assigns.live_action, place_extract))
     |> assign(:place_extract, place_extract)}
  end

  @impl true
  def handle_event("close_modal", _, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/google/place_extracts/#{socket.assigns.place_extract}")}
  end

  defp page_title(:show, place_extract),
    do:
      gettext("%{model} - %{place_extract}",
        model: gettext(@model),
        place_extract: "#{place_extract.state.name}, #{place_extract.state.country_code}"
      )

  defp page_title(:edit, _place_extract), do: gettext("Edit %{model}", model: gettext(@model))
end
