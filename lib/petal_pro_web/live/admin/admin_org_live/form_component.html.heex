<div>
  <.form for={@form} id="org-form" phx-target={@myself} phx-submit="save">
    <.field
      type="text"
      field={@form[:name]}
      label={gettext("Name")}
      required
      {alpine_autofocus()}
      phx-debounce="500"
    />
    <.field disabled field={@form[:slug]} />

    <div class="flex justify-end gap-2">
      <.button phx-disable-with={gettext("Saving...")}>
        <.icon name="hero-check-circle" class="mr-2 h-5 w-5" />
        {gettext("Save")}
      </.button>
    </div>
  </.form>
</div>
