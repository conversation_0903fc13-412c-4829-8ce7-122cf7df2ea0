defmodule PetalProWeb.AdminOrgLive.FormComponent do
  @moduledoc false
  use PetalProWeb, :live_component

  alias AshPhoenix.Form

  @model "Organization"

  @impl Phoenix.LiveComponent
  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_form()}
  end

  @impl Phoenix.LiveComponent
  def handle_event("save", %{"form" => form_data}, socket) do
    case Form.submit(socket.assigns.form, params: form_data) do
      {:ok, _org} ->
        socket =
          socket
          |> put_flash(
            :success,
            gettext("%{model} saved successfully", model: gettext(@model))
          )
          |> push_navigate(to: socket.assigns.return_to)

        {:noreply, socket}

      {:error, form} ->
        socket =
          socket
          |> put_flash(:error, gettext("Unable to save %{model}", model: gettext(@model)))
          |> assign(:form, form)

        {:noreply, socket}
    end
  end

  defp assign_form(%{assigns: %{org: org}} = socket) do
    current_user = socket.assigns.current_user

    form =
      if org.id do
        ash_org =
          Boring.Orgs.get_org_by_id!(
            org.id,
            actor: current_user
          )

        Boring.Orgs.form_to_update_org(
          ash_org,
          actor: current_user,
          forms: [auto?: true]
        )
      else
        Boring.Orgs.form_to_create_org(actor: current_user, forms: [auto?: true])
      end

    assign(socket, form: to_form(form))
  end
end
