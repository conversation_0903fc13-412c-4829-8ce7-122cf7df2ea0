<div>
  <.form
    for={@form}
    id="membership-form"
    phx-target={@myself}
    phx-change="validate"
    phx-submit="save"
  >
    <.combo_box
      field={@form[:user_id]}
      label={gettext("User")}
      placeholder={gettext("Pick a user!")}
      options={Enum.map(@users, &{"#{user_name(&1)} (#{&1.email})", &1.id})}
      required
      disabled={@form.data.id}
      tom_select_plugins={%{remove_button: !@form.data.id}}
    />

    <.field
      type="select"
      field={@form[:role]}
      label={gettext("Role")}
      required
      options={@roles}
      placeholder={gettext("Pick a role!")}
    />

    <div class="flex justify-end gap-2">
      <.button phx-disable-with={gettext("Saving...")}>
        <.icon name="hero-check-circle" class="mr-2 h-5 w-5" />
        {gettext("Save")}
      </.button>
    </div>
  </.form>
</div>
