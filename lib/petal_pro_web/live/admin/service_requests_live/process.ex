defmodule PetalProWeb.ServiceRequestsLive.Process do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalProWeb.AdminLayoutComponent

  @impl true
  def handle_params(params, _url, socket) do
    opportunity =
      Boring.Acquisitions.get_opportunity!(params["opportunity_id"],
        load: [
          :display_phone_number,
          :latest_completed_skip_trace,
          :global_contacts,
          open_service_requests: :org
        ],
        actor: socket.assigns.current_user
      )

    socket =
      socket
      |> assign(:page_title, "Open Service Requests")
      |> assign(:opportunity, opportunity)
      |> assign(
        :skip_trace_form,
        Boring.Acquisitions.form_to_update_opportunity_global_contacts(opportunity,
          actor: socket.assigns.current_user
        )
      )

    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <.admin_layout current_page={:service_requests} current_user={@current_user}>
      <.page_header title={gettext("Open Service Requests")} />
      <.h4>{@opportunity.name}</.h4>
      <.p :if={@opportunity.full_address}>
        {@opportunity.full_address}
      </.p>
      <.p :if={@opportunity.display_phone_number}>
        {@opportunity.display_phone_number}
      </.p>
      <.p :if={@opportunity.website}>
        <.link href={@opportunity.website} class="text-primary-500 underline" target="_blank">
          {@opportunity.website}
        </.link>
      </.p>

      <.h4 class="mt-8">Requested By</.h4>
      <ul class="list-inside list-disc">
        <li :for={request <- @opportunity.open_service_requests}>
          {request.org.name} ({request.type})
        </li>
      </ul>

      <%= if Enum.any?(skip_trace_requests(@opportunity)) do %>
        <.h4 class="mt-8">Global Contacts</.h4>
        <.p>Last updated at: {show_date(@opportunity.latest_completed_skip_trace)}</.p>
        <.form :let={form} for={@skip_trace_form} phx-change="validate" phx-submit="save">
          <.p :if={Enum.empty?(AshPhoenix.Form.value(form, :global_contacts))} class="italic">
            (no global contacts to display)
          </.p>
          <div class="mb-8 grid grid-cols-3 gap-8">
            <.inputs_for :let={contact_form} field={form[:global_contacts]}>
              <.card>
                <.card_content>
                  <.field field={contact_form[:name]} label="Name" />
                  <.field field={contact_form[:email]} label="Email" />
                  <.field field={contact_form[:phone_number]} label="Phone Number" />
                  <.button
                    type="button"
                    color="danger"
                    variant="outline"
                    label="Remove"
                    phx-click="remove-contact"
                    phx-value-path={contact_form.name}
                  />
                </.card_content>
              </.card>
            </.inputs_for>
          </div>
          <.button type="button" variant="outline" label="Add Contact" phx-click="add-contact" /><br /><br />
          <.button label="Save and Process Skip Trace Requests" />
        </.form>
      <% end %>
    </.admin_layout>
    """
  end

  @impl true
  def handle_event("add-contact", _params, socket) do
    socket =
      update(socket, :skip_trace_form, fn form ->
        AshPhoenix.Form.add_form(form, :global_contacts)
      end)

    {:noreply, socket}
  end

  def handle_event("remove-contact", %{"path" => path}, socket) do
    socket =
      update(socket, :skip_trace_form, fn form ->
        AshPhoenix.Form.remove_form(form, path)
      end)

    {:noreply, socket}
  end

  def handle_event("validate", %{"form" => params}, socket) do
    socket =
      update(socket, :skip_trace_form, fn form ->
        AshPhoenix.Form.validate(form, params)
      end)

    {:noreply, socket}
  end

  def handle_event("save", params, socket) do
    case AshPhoenix.Form.submit(socket.assigns.skip_trace_form, params: Map.get(params, "form")) do
      {:ok, _opportunity} ->
        socket =
          socket
          |> put_flash(:info, "Skip trace requests processed successfully")
          |> push_navigate(to: ~p"/admin/service-requests/")

        {:noreply, socket}

      {:error, form} ->
        socket =
          socket
          |> assign(:skip_trace_form, form)
          |> put_flash(:error, "Could not process skip trace requests")

        {:noreply, socket}
    end
  end

  defp skip_trace_requests(opportunity) do
    Enum.filter(opportunity.open_service_requests, &(&1.type == :skiptrace))
  end

  defp show_date(nil), do: "never"

  defp show_date(%{updated_at: updated_at}) do
    format_date(updated_at, "{D} {Mshort} {YYYY}")
  end
end
