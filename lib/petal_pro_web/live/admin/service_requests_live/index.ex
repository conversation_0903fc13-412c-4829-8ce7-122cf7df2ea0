defmodule PetalProWeb.ServiceRequestsLive.Index do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalProWeb.AdminLayoutComponent

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:page_title, "Open Service Requests")
      |> assign(
        :opportunities,
        Boring.Acquisitions.list_opportunities_with_open_service_requests!(actor: socket.assigns.current_user)
      )

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <.admin_layout current_page={:service_requests} current_user={@current_user}>
      <.page_header title={gettext("Open Service Requests")} />

      <.table
        id="opportunities"
        rows={@opportunities}
        row_click={fn opp -> JS.navigate(~p"/admin/service-requests/#{opp.id}") end}
      >
        <:col :let={opp} label="Name">{opp.name}</:col>
        <:col :let={opp} label="Address">{opp.full_address}</:col>
        <:col :let={opp} label="Requests">
          <.badge :for={request <- opp.open_service_requests} label={request.type} />
        </:col>
        <:col :let={opp} label="Requested At">
          {format_date(hd(opp.open_service_requests).updated_at)}
        </:col>
      </.table>
    </.admin_layout>
    """
  end
end
