<.admin_layout current_page={:admin_dashboard} current_user={@current_user}>
  <.h2>Dashboard</.h2>
  <dl class="mt-5 grid grid-cols-1 gap-5 md:grid-cols-3">
    <.dashboard_stat
      label={"New Users in #{Timex.now() |> Timex.format!("{Mshort}")}"}
      stat={@user_total_for_this_month}
      percentage_change={@user_percentage_change}
      icon="hero-users"
      chart_event="users_data"
      empty={@user_empty?}
    />

    <.dashboard_stat
      label={"New Orgs in #{Timex.now() |> Timex.format!("{Mshort}")}"}
      stat={@org_total_for_this_month}
      percentage_change={@org_percentage_change}
      icon="hero-building-office"
      chart_event="orgs_data"
      empty={@org_empty?}
    />

    <.dashboard_stat
      label={"Newsletter Subscribers in #{Timex.now() |> Timex.format!("{<PERSON><PERSON><PERSON>}")}"}
      stat={@subscribed_total_for_this_month}
      percentage_change={@subscribed_percentage_change}
      icon="hero-envelope-open"
      chart_event="newsletter_subscribed_data"
      empty={@subscribed_empty?}
    />
  </dl>

  <div class="mt-5 grid grid-cols-1 gap-5 lg:grid-cols-2">
    <.dashboard_graph
      graph_type="line"
      heading="User Acquisition"
      chart_event="user_acquisitions"
      empty={@user_acquisitions_empty?}
    />

    <.dashboard_graph
      graph_type="line"
      heading="Newsletter Subscriber Acquisition"
      chart_event="newsletter_subscriber_acquisitions"
      empty={@newsletter_subscriber_acquisitions_empty?}
    />
  </div>

  <div class="mt-5 grid grid-cols-1 gap-5 md:grid-cols-6">
    <.dashboard_graph
      class="col-span-4"
      heading="Org Acquisition"
      chart_event="org_acquisitions"
      empty={@org_acquisitions_empty?}
    />

    <.dashboard_donut
      class="col-span-4 md:col-span-2"
      heading="Paying Subscriptions"
      chart_event="paying_subscriptions"
      empty={@paying_active_subscriptions_empty?}
    />
  </div>

  <.dashboard_panel heading="Recently Joined Users" class="mt-5 overflow-auto">
    <.table>
      <.tr>
        <.th>{gettext("User")}</.th>
        <.th>{gettext("Confirmed")}</.th>
        <.th>{gettext("Onboarded")}</.th>
        <.th></.th>
      </.tr>

      <%= for user <- @last_joined_users do %>
        <.tr>
          <.td>
            <.user_inner_td
              avatar_assigns={%{src: user.avatar}}
              label={user.name}
              sub_label={user.email}
            />
          </.td>
          <.td>
            <.icon
              :if={user.confirmed_at != nil}
              name="hero-check-circle"
              class="h-5 w-5 text-green-500"
            />
            <.icon
              :if={user.confirmed_at == nil}
              name="hero-x-circle"
              class="h-5 w-5 text-red-500"
            />
          </.td>
          <.td>
            <.icon
              :if={user.is_onboarded == true}
              name="hero-check-circle"
              class="h-5 w-5 text-green-500"
            />
            <.icon
              :if={user.is_onboarded != true}
              name="hero-x-circle"
              class="h-5 w-5 text-red-500"
            />
          </.td>
          <.td>
            <.link
              navigate={~p"/admin/users/#{user}"}
              class="text-primary-600 underline dark:text-primary-400"
            >
              View
            </.link>
          </.td>
        </.tr>
      <% end %>
    </.table>
  </.dashboard_panel>
</.admin_layout>
