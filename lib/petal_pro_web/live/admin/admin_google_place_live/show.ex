defmodule PetalProWeb.AdminGooglePlaceLive.Show do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalProWeb.AdminLayoutComponent

  alias Boring.Google

  @model "Place"

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def handle_params(%{"id" => id}, _, socket) do
    place =
      Google.get_place!(id, actor: socket.assigns.current_user)

    {:noreply,
     socket
     |> assign(:page_title, page_title(socket.assigns.live_action, place))
     |> assign(:place, place)}
  end

  @impl true
  def handle_event("close_modal", _, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/google/places/#{socket.assigns.place}")}
  end

  defp page_title(:show, place),
    do: gettext("%{model} - %{place}", model: gettext(@model), place: "#{place.state.name}, #{place.state.country_code}")

  defp page_title(:edit, _place), do: gettext("Edit %{model}", model: gettext(@model))
end
