<div>
  <.form
    for={@form}
    id="place-form"
    phx-target={@myself}
    phx-submit="save"
    class="md:grid md:grid-cols-2 md:gap-4"
  >
    <.field field={@form[:place_id]} label={gettext("Place ID")} required />
    <.field field={@form[:business_name]} required />

    <.combo_box
      label={gettext("Categories")}
      placeholder="Add categories..."
      field={@form[:categories]}
      options={Boring.Google.Place.Preparations.OpportunityCandidateFilter.storage_categories()}
      multiple
      create
      required
      wrapper_class="md:col-span-2"
    />

    <.field
      type="select"
      field={@form[:state_id]}
      options={@locations}
      prompt="Choose a location"
      required
    />

    <div class="flex justify-end">
      <.button type="submit" phx-disable-with="Saving..." label={gettext("Save")} />
    </div>
  </.form>
</div>
