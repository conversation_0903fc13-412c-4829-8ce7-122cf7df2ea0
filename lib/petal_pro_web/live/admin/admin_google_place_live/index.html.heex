<.admin_layout current_page={:admin_google_places} current_user={@current_user}>
  <.page_header title="Listing Places">
    <.button link_type="live_patch" label="New Place" to={~p"/admin/google/places/new"} />
  </.page_header>

  <%= if @live_action in [:new, :edit] do %>
    <.modal title={@page_title}>
      <.live_component
        module={PetalProWeb.AdminGooglePlaceLive.FormComponent}
        id={@place.id || :new}
        action={@live_action}
        place={@place}
        current_user={@current_user}
        return_to={current_index_path(@index_params)}
      />
    </.modal>
  <% end %>

  <.data_table :if={@index_params} meta={@meta} items={@places}>
    <:if_empty>No Places found</:if_empty>

    <:col field={:place_id} />
    <:col field={:business_name} />
    <:col :let={place} label={gettext("Location")}>
      {place.state.name}, {place.state.country_code}
    </:col>
    <:col :let={place} label={gettext("Actions")}>
      <.place_actions socket={@socket} place={place} />
    </:col>
  </.data_table>
</.admin_layout>
