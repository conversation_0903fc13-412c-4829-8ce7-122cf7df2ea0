defmodule PetalProWeb.AdminGooglePlaceLive.Index do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalProWeb.AdminLayoutComponent
  import PetalProWeb.DataTable
  import PetalProWeb.PageComponents

  alias Boring.Google
  alias Boring.Google.Place

  @model "Place"
  @model_plural "Places"

  @data_table_opts [
    default_limit: 10,
    default_order: %{
      order_by: [:id, :inserted_at],
      order_directions: [:asc, :desc]
    }
  ]

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign(socket, index_params: nil)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit #{@model}")
    |> assign(:place, Google.get_place!(id, actor: socket.assigns.current_user))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New #{@model}")
    |> assign(:place, %Place{})
  end

  defp apply_action(socket, :index, params) do
    socket
    |> assign(:page_title, "Listing #{@model_plural}")
    |> assign_places(params)
    |> assign(index_params: params)
  end

  defp current_index_path(index_params) do
    ~p"/admin/google/places?#{index_params || %{}}"
  end

  @impl true
  def handle_event("update_filters", params, socket) do
    query_params = PetalProWeb.DataTable.build_filter_params(socket.assigns.meta.flop, params)
    {:noreply, push_patch(socket, to: ~p"/admin/google/places?#{query_params}")}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    :ok = Google.delete_place(id, actor: socket.assigns.current_user)

    socket =
      socket
      |> assign_places(socket.assigns.index_params)
      |> put_flash(:info, "#{@model} deleted")

    {:noreply, socket}
  end

  @impl true
  def handle_event("close_modal", _, socket) do
    {:noreply, push_patch(socket, to: current_index_path(socket.assigns.index_params))}
  end

  defp assign_places(socket, params) do
    starting_query = Place

    {places, meta} =
      PetalProWeb.DataTable.search(starting_query, params, @data_table_opts)

    assign(socket, places: Ash.load!(places, :state), meta: meta)
  end

  defp place_actions(assigns) do
    ~H"""
    <div class="flex items-center" id={"places_actions_container_#{@place.id}"}>
      <.dropdown
        class="dark:shadow-lg"
        options_container_id={"places_options_#{@place.id}"}
        menu_items_wrapper_class="dark:border dark:border-gray-600"
      >
        <.dropdown_menu_item link_type="live_patch" to={~p"/admin/google/places/#{@place}/edit"}>
          <.icon name="hero-pencil" class="h-5 w-5" /> {gettext("Edit")}
        </.dropdown_menu_item>

        <.dropdown_menu_item
          link_type="a"
          to="#"
          phx-click="delete"
          phx-value-id={@place.id}
          data-confirm={gettext("Are you sure?")}
        >
          <.icon name="hero-trash" class="h-5 w-5" /> {gettext("Delete")}
        </.dropdown_menu_item>
      </.dropdown>
    </div>
    """
  end
end
