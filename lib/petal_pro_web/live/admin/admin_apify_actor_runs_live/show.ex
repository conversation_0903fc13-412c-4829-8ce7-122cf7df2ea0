defmodule PetalProWeb.AdminApifyActorRunsLive.Show do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalProWeb.AdminLayoutComponent

  alias Boring.Google
  alias Boring.Google.Apify
  alias Boring.Google.PlaceExtract
  alias Boring.Workers.ApifyGooglePlaceIngestWorker

  require Logger

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def handle_params(%{"id" => id}, _, socket) do
    {:ok, %Tesla.Env{body: %{"data" => actor_run}}} = Apify.get_actor_run(id)

    {:noreply,
     socket
     |> assign(:page_title, page_title(socket.assigns.live_action))
     |> assign(:apify_actor_run, actor_run)}
  end

  @impl true
  def handle_event("close_modal", _, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/apify-actor-runs/#{socket.assigns.apify_actor_run}")}
  end

  @impl true
  def handle_event("cancel", %{"id" => id}, socket) do
    case Apify.abort_actor_run(id) do
      {:ok, %Tesla.Env{}} ->
        {:noreply, put_flash(socket, :info, "You have cancelled the actor run")}

      {:error, %Tesla.Env{} = error} ->
        Logger.warning("Failed to cancel the actor run #{id}: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "Failed to cancel the actor run")}
    end
  end

  @impl true
  def handle_event("import", %{"id" => id}, socket) do
    case Google.get_place_extract_by_actor_run_id!(last_actor_run_id: id) do
      %PlaceExtract{} = pet ->
        Oban.insert(ApifyGooglePlaceIngestWorker.new(%{"id" => pet.id, "actor_run_id" => id}))
        {:noreply, put_flash(socket, :info, "Data import started")}

      nil ->
        {:noreply, put_flash(socket, :error, "Unable to find PlaceExtract for this run id")}
    end
  end

  defp page_title(:show), do: "Show Apify Actor Run"
end
