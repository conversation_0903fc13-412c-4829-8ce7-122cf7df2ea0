defmodule PetalProWeb.AdminApifyActorRunsLive.Index do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalProWeb.AdminLayoutComponent

  alias Boring.Google.Apify

  require Logger

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign(socket, index_params: nil, current_status: "RUNNING")}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, params) do
    socket
    |> assign(:page_title, "Listing Apify Actor Runs")
    |> assign_apify_actor_runs()
    |> assign(index_params: params)
  end

  defp current_index_path(index_params) do
    ~p"/admin/apify-actor-runs?#{index_params || %{}}"
  end

  @impl true
  def handle_event("close_modal", _, socket) do
    {:noreply, push_patch(socket, to: current_index_path(socket.assigns.index_params))}
  end

  @impl true
  def handle_event("update", %{"status" => status}, socket) do
    {:noreply,
     socket
     |> assign(:current_status, status)
     |> assign_apify_actor_runs()}
  end

  @impl true
  def handle_event("cancel", %{"id" => id}, socket) do
    case Apify.abort_actor_run(id) do
      {:ok, %Tesla.Env{}} ->
        {:noreply, put_flash(socket, :info, "You have cancelled the actor run")}

      {:error, %Tesla.Env{} = error} ->
        Logger.warning("Failed to cancel the actor run #{id}: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "Failed to cancel the actor run")}
    end
  end

  defp assign_apify_actor_runs(socket) do
    case Apify.get_actor_runs_by_status(socket.assigns.current_status) do
      {:ok, apify_actor_runs} ->
        assign(socket, apify_actor_runs: apify_actor_runs.body["data"]["items"])

      {:error, :timeout} ->
        assign(socket, put_flash(socket, :error, "Unable to get Apify actor runs"))
    end
  end
end
