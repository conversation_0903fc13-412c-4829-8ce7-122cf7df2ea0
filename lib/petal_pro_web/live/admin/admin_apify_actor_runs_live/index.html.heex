<.admin_layout current_page={:admin_apify_actor_runs} current_user={@current_user}>
  <.page_header title="Listing Apify Actor Runs"></.page_header>

  <.table>
    <.tr>
      <.th>Id</.th>
      <.th>Started At</.th>
      <.th>Finished At</.th>
      <.th class="text-right">Usage (USD)</.th>
      <.th :if={@current_status in ["READY", "RUNNING"]} class="text-right">Actions</.th>
    </.tr>

    <.form phx-change="update" for={%{}}>
      <.field
        type="select"
        name="status"
        label="Status"
        options={Boring.Google.Apify.get_status_types()}
        value={@current_status}
      />
    </.form>

    <%= for run <- @apify_actor_runs do %>
      <.tr>
        <.td>
          <.link navigate={~p"/admin/apify-actor-runs/#{run["id"]}"} class="font-bold underline">
            {run["id"]}
          </.link>
        </.td>
        <.td>{run["startedAt"]}</.td>
        <.td>{run["finishedAt"]}</.td>
        <.td class="text-right">
          {Money.new(run["usageTotalUsd"], :USD) |> Money.to_string!()}
        </.td>
        <.td :if={run["status"] in ["READY", "RUNNING"]}>
          <.button
            phx-click="cancel"
            phx-value-id={run["id"]}
            label="Cancel run"
            size="xs"
            color="danger"
          />
        </.td>
      </.tr>
    <% end %>
  </.table>
</.admin_layout>
