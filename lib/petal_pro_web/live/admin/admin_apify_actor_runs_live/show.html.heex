<.admin_layout current_page={:admin_apify_actor_runs} current_user={@current_user}>
  <.page_header title="Show Apify Actor Run">
    <.button
      :if={@apify_actor_run["status"] == "SUCCEEDED"}
      phx-click="import"
      phx-value-id={@apify_actor_run["id"]}
      label="Import data"
      color="primary"
    />

    <.button
      :if={@apify_actor_run["status"] in [nil, "READY", "RUNNING"]}
      phx-click="cancel"
      phx-value-id={@apify_actor_run["id"]}
      label="Cancel run"
      color="danger"
    />

    <.button
      link_type="live_redirect"
      label="Back"
      color="light"
      to={~p"/admin/apify-actor-runs"}
    />
  </.page_header>

  <div class="max-w-lg">
    <div class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
      <div class="sm:col-span-2">
        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
          Status
        </div>
        <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
          {@apify_actor_run["status"]}
        </div>
      </div>

      <div class="sm:col-span-2">
        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
          Status message
        </div>
        <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
          <.pretty_markdown content={@apify_actor_run["statusMessage"]} />
        </div>
      </div>

      <div class="sm:col-span-1">
        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
          Started at
        </div>
        <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
          {@apify_actor_run["startedAt"]}
        </div>
      </div>

      <div class="sm:col-span-1">
        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
          Finished at
        </div>
        <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
          {@apify_actor_run["finishedAt"]}
        </div>
      </div>

      <div class="sm:col-span-1">
        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
          Usage (USD)
        </div>
        <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
          {Money.new(@apify_actor_run["usageTotalUsd"], :USD) |> Money.to_string!()}
        </div>
      </div>
    </div>
  </div>
</.admin_layout>
