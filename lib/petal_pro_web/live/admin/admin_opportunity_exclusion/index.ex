defmodule PetalProWeb.AdminOpportunityExclusionLive.Index do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalProWeb.AdminLayoutComponent
  import PetalProWeb.DataTable
  import PetalProWeb.PageComponents

  alias Boring.Acquisitions
  alias Boring.Acquisitions.OpportunityExclusion

  @model "Opportunity Exclusion"
  @model_plural "Opportunity Exclusions"

  @data_table_opts [
    default_limit: 10,
    default_order: %{
      order_by: [:id, :name, :inserted_at],
      order_directions: [:asc, :desc]
    }
  ]

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign(socket, index_params: nil)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit #{@model}")
    |> assign(
      :opportunity_exclusion,
      Acquisitions.get_opportunity_exclusion!(id, actor: socket.assigns.current_user)
    )
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New #{@model}")
    |> assign(:opportunity_exclusion, %OpportunityExclusion{})
  end

  defp apply_action(socket, :index, params) do
    socket
    |> assign(:page_title, "Listing #{@model_plural}")
    |> assign_opportunity_exclusions(params)
    |> assign(index_params: params)
  end

  defp current_index_path(index_params) do
    ~p"/admin/opportunity_exclusions?#{index_params || %{}}"
  end

  @impl true
  def handle_event("update_filters", params, socket) do
    query_params = PetalProWeb.DataTable.build_filter_params(socket.assigns.meta.flop, params)
    {:noreply, push_patch(socket, to: ~p"/admin/opportunity_exclusions?#{query_params}")}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    :ok = Acquisitions.delete_opportunity_exclusion(id, actor: socket.assigns.current_user)

    socket =
      socket
      |> assign_opportunity_exclusions(socket.assigns.index_params)
      |> put_flash(:info, "#{@model} deleted")

    {:noreply, socket}
  end

  @impl true
  def handle_event("close_modal", _, socket) do
    {:noreply, push_patch(socket, to: current_index_path(socket.assigns.index_params))}
  end

  defp assign_opportunity_exclusions(socket, params) do
    starting_query = OpportunityExclusion

    {opportunity_exclusions, meta} =
      PetalProWeb.DataTable.search(starting_query, params, @data_table_opts)

    opportunity_exclusions =
      Ash.load!(opportunity_exclusions, [:matching_google_places, :matching_opportunities])

    assign(socket, opportunity_exclusions: opportunity_exclusions, meta: meta)
  end

  defp opportunity_exclusion_actions(assigns) do
    ~H"""
    <div
      class="flex items-center"
      id={"opportunity_exclusions_actions_container_#{@opportunity_exclusion.id}"}
    >
      <.dropdown
        class="dark:shadow-lg"
        options_container_id={"opportunity_exclusions_options_#{@opportunity_exclusion.id}"}
        menu_items_wrapper_class="dark:border dark:border-gray-600"
      >
        <.dropdown_menu_item
          link_type="live_patch"
          to={~p"/admin/opportunity_exclusions/#{@opportunity_exclusion}/edit"}
        >
          <.icon name="hero-pencil" class="h-5 w-5" /> {gettext("Edit")}
        </.dropdown_menu_item>

        <.dropdown_menu_item
          link_type="a"
          to="#"
          phx-click="delete"
          phx-value-id={@opportunity_exclusion.id}
          data-confirm={gettext("Are you sure?")}
        >
          <.icon name="hero-trash" class="h-5 w-5" /> {gettext("Delete")}
        </.dropdown_menu_item>
      </.dropdown>
    </div>
    """
  end
end
