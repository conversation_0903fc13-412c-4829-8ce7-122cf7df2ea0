defmodule PetalProWeb.AdminOpportunityExclusionLive.FormComponent do
  @moduledoc false
  use PetalProWeb, :live_component

  alias AshPhoenix.Form
  alias Boring.Acquisitions

  @model "Opportunity Exclusion"

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_form()

    {:ok, socket}
  end

  @impl true
  def handle_event("validate", %{"form" => form_data}, socket) do
    socket =
      update(socket, :form, fn form ->
        Form.validate(form, form_data)
      end)

    {:noreply, socket}
  end

  def handle_event("save", %{"form" => form_data}, socket) do
    case Form.submit(socket.assigns.form, params: form_data) do
      {:ok, _opportunity_exclusion} ->
        socket =
          socket
          |> put_flash(
            :success,
            gettext("%{model} saved successfully", model: gettext(@model))
          )
          |> push_navigate(to: socket.assigns.return_to)

        {:noreply, socket}

      {:error, form} ->
        socket =
          socket
          |> put_flash(:error, gettext("Unable to save %{model}", model: gettext(@model)))
          |> assign(:form, form)

        {:noreply, socket}
    end
  end

  defp assign_form(%{assigns: %{opportunity_exclusion: opportunity_exclusion}} = socket) do
    form =
      if opportunity_exclusion.id do
        Acquisitions.form_to_update_opportunity_exclusion(opportunity_exclusion,
          actor: socket.assigns.current_user
        )
      else
        Acquisitions.form_to_create_opportunity_exclusion(actor: socket.assigns.current_user)
      end

    assign(socket, form: to_form(form))
  end
end
