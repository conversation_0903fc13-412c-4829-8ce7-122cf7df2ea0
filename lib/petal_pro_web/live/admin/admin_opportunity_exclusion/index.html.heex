<.admin_layout current_page={:admin_opportunity_exclusions} current_user={@current_user}>
  <.page_header title="Listing Opportunity Exclusions">
    <.button
      link_type="live_patch"
      label="New Opportunity Exclusion"
      to={~p"/admin/opportunity_exclusions/new"}
    />
  </.page_header>

  <%= if @live_action in [:new, :edit] do %>
    <.modal title={@page_title}>
      <.live_component
        module={PetalProWeb.AdminOpportunityExclusionLive.FormComponent}
        id={@opportunity_exclusion.id || :new}
        action={@live_action}
        opportunity_exclusion={@opportunity_exclusion}
        current_user={@current_user}
        return_to={current_index_path(@index_params)}
      />
    </.modal>
  <% end %>

  <.data_table :if={@index_params} meta={@meta} items={@opportunity_exclusions}>
    <:if_empty>No Opportunity Exclusions found</:if_empty>

    <:col field={:name} sortable />
    <:col field={:matching_google_places} label={gettext("Google Places")} sortable />
    <:col field={:matching_opportunities} label={gettext("Opportunities")} sortable />

    <:col :let={opportunity_exclusion} label={gettext("Actions")}>
      <.opportunity_exclusion_actions
        socket={@socket}
        opportunity_exclusion={opportunity_exclusion}
      />
    </:col>
  </.data_table>
</.admin_layout>
