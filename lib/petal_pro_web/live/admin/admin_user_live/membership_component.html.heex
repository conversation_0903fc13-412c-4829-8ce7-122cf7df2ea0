<div>
  <.form
    for={@form}
    id="membership-form"
    phx-target={@myself}
    phx-change="validate"
    phx-submit="save"
  >
    <.combo_box
      field={@form[:org_id]}
      label={gettext("Organization")}
      placeholder={gettext("Pick an organization!")}
      options={Enum.map(@orgs, &{"#{&1.name} (#{&1.slug})", &1.id})}
      required
      disabled={@form.data.id}
      tom_select_plugins={%{remove_button: !@form.data.id}}
    />

    <.field
      type="select"
      field={@form[:role]}
      label={gettext("Role")}
      required
      options={@roles}
      placeholder={gettext("Pick a role!")}
    />

    <div class="flex justify-end gap-2">
      <.button phx-disable-with={gettext("Saving...")}>
        <.icon name="hero-check-circle" class="mr-2 h-5 w-5" />
        {gettext("Save")}
      </.button>
    </div>
  </.form>
</div>
