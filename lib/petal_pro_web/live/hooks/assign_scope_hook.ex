defmodule PetalProWeb.AssignScopeHook do
  @moduledoc """
  LiveView hook that sets a `:scope` in socket.private using Boring.Scope.new with the actor set as the current user
  and tenant set as the current organization when applicable.

  This is used alongside the existing `:current_user` and `:current_org` assigns.

  It can either create a new scope from the current_user and current_org assigns
  """

  def on_mount(:default, _params, _session, socket) do
    {:cont, set_scope(socket)}
  end

  # Fall back to using the assigns if session data isn't available
  defp set_scope(%{assigns: %{current_user: current_user, current_org: current_org}} = socket)
       when not is_nil(current_user) and not is_nil(current_org) do
    # Get Boring.Accounts.User and Boring.Orgs.Org records
    ash_user = Boring.Accounts.get_user_by_id!(current_user.id, authorize?: false)

    ash_org =
      Boring.Orgs.get_org_by_id!(current_org.id,
        load: [:active_subscriber?, :billing_product],
        authorize?: false
      )

    Phoenix.Component.assign(socket, :scope, %Boring.Scope{
      current_user: ash_user,
      current_tenant: ash_org
    })
  end

  defp set_scope(%{assigns: %{current_user: current_user}} = socket) when not is_nil(current_user) do
    # Get Boring.Accounts.User record
    ash_user = Boring.Accounts.get_user_by_id!(current_user.id, authorize?: false)

    Phoenix.Component.assign(socket, :scope, %Boring.Scope{current_user: ash_user})
  end

  defp set_scope(socket) do
    Phoenix.Component.assign(socket, :scope, %Boring.Scope{})
  end
end
