<.org_layout
  current_page={:org_dashboard}
  current_user={@current_user}
  current_org={@current_org}
  current_membership={@current_membership}
  socket={@socket}
>
  <.container max_width="full" class="px-10 pt-4 md:pt-10">
    <div class="flex flex-col gap-2 md:mb-10">
      <.quick_filters
        :if={@preset_filter_counts}
        current_filter={current_filter(@params)}
        filters={@preset_filter_counts}
        class="hidden pt-1.5 lg:inline-block"
      />
      <.control_bar
        params={@params}
        state_options={@state_options}
        status_options={@status_options}
        sort_options={@sort_options}
      />
    </div>

    <%!-- <.no_opportunities_screen :if={
      current_filter(@params) != "new" and
        @streams.org_opportunity_cards == [] and
        @scope.current_tenant.has_opportunities?
    } /> --%>
    <div :if={not @scope.current_tenant.has_opportunities?}>
      <.no_opportunities_screen org={@scope.current_tenant} />
    </div>

    <div
      :if={@scope.current_tenant.has_opportunities?}
      id="org_opportunity_cards"
      phx-update="stream"
      phx-viewport-top={not is_nil(@page.after) && "prev-page"}
      phx-viewport-bottom={@page.more? == true && "next-page"}
      phx-page-loading
      class="mt-5 grid grid-cols-1 gap-12 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4"
    >
      <.opportunity_card
        :for={{dom_id, opportunity_card} <- @streams.org_opportunity_cards}
        id={dom_id}
        opportunity_card={opportunity_card}
      />
    </div>
  </.container>

  <.slide_over
    :if={@selected_org_opportunity}
    origin="right"
    max_width="xl"
    title="Opportunity Details"
  >
    {live_render(@socket, PetalProWeb.OrgOpportunityLive,
      id: @selected_org_opportunity,
      session: %{
        "org_opportunity_id" => @selected_org_opportunity,
        "tenant_id" => @scope.current_tenant.id
      }
    )}
  </.slide_over>
</.org_layout>
