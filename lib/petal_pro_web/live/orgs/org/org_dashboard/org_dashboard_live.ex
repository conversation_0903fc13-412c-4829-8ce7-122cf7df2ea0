defmodule PetalProWeb.OrgDashboardLive do
  @moduledoc """
  Show a dashboard for a single org. Current user must be a member of the org.
  """
  use PetalProWeb, :live_view
  use BoringWeb.Components
  use PetalProWeb.OrgDashboard.Components

  import PetalProWeb.OrgLayoutComponent

  alias Ash.Page.Keyset
  alias Boring.Acquisitions
  alias Boring.Acquisitions.Types.OpportunityStatus
  alias Phoenix.LiveView.Socket

  require Logger

  @impl Phoenix.LiveView
  def mount(_params, _session, socket) do
    current_org =
      Ash.load!(socket.assigns.scope.current_tenant, :has_opportunities?, scope: socket.assigns.scope)

    socket =
      update(socket, :scope, fn scope ->
        Map.put(scope, :current_tenant, current_org)
      end)

    if connected?(socket) do
      PetalProWeb.Endpoint.subscribe(org_topic(socket))
    end

    case execute_mount_queries_async(socket) do
      {:ok, results} ->
        socket =
          socket
          |> assign(%{
            page_title: current_org.name,
            status_options: status_options(),
            state_options: results.state_options,
            sort_options: sort_options(),
            preset_filter_counts: results.preset_filter_counts
          })
          |> stream(:org_opportunity_cards, to_card_data(results.org_opportunities), reset: true)
          |> assign(
            params: %{},
            sort_key: "-updated_at",
            page: results.org_opportunities,
            selected_org_opportunity: nil,
            show_opportunity_details: false
          )

        {:ok, socket}

      {:error, error} ->
        Logger.error("Mount async operation failed: #{inspect(error)}")

        # Fallback to sequential execution
        socket = execute_mount_queries_sequential(socket)
        {:ok, socket}
    end
  end

  @impl Phoenix.LiveView
  def handle_params(params, _uri, socket) do
    {:noreply, stream_org_opportunities(socket, params)}
  end

  @impl Phoenix.LiveView
  def handle_info(%{event: "opportunity_assigned", payload: notification}, socket) do
    {:noreply, stream_insert_org_opportunity(socket, notification.data)}
  end

  @impl Phoenix.LiveView
  def handle_info(%{event: "opportunity_updated", payload: notification}, socket) do
    {:noreply, stream_insert_org_opportunity(socket, notification.data)}
  end

  def handle_info({:update_flash, type, msg}, socket) do
    socket =
      socket
      |> clear_flash()
      |> put_flash(type, msg)

    {:noreply, socket}
  end

  def handle_info(_event, socket), do: {:noreply, socket}

  @impl Phoenix.LiveView
  def handle_event("preset_filter", %{"filter" => filter}, socket) do
    {:noreply, push_patch(socket, to: current_route(socket, %{"preset_filter" => filter}))}
  end

  def handle_event("control_bar_change", params, socket) do
    {:noreply, push_patch(socket, to: current_route(socket, params))}
  end

  def handle_event("next-page", _, socket) do
    next_page = Ash.page!(socket.assigns.page, :next)

    socket =
      socket
      |> assign(:page, next_page)
      |> stream(:org_opportunity_cards, to_card_data(next_page))

    {:noreply, socket}
  end

  def handle_event("prev-page", _, socket) do
    if socket.assigns.page.after == nil do
      {:noreply, socket}
    else
      prev_page = Ash.page!(socket.assigns.page, :prev)

      socket =
        socket
        |> assign(:page, prev_page)
        |> stream(:org_opportunity_cards, to_card_data(prev_page))

      {:noreply, socket}
    end
  end

  def handle_event("show_opportunity_details", %{"id" => org_opportunity_id}, socket) do
    {:noreply, assign(socket, :selected_org_opportunity, org_opportunity_id)}
  end

  def handle_event("close_slide_over", _params, socket) do
    {:noreply, assign(socket, :selected_org_opportunity, nil)}
  end

  def handle_event("favorite", %{"id" => id}, socket) do
    favorited =
      Acquisitions.favorite_org_opportunity!(id, scope: socket.assigns.scope)

    {:noreply, stream_insert_org_opportunity(socket, favorited)}
  end

  def handle_event("unfavorite", %{"id" => id}, socket) do
    unfavorited =
      Acquisitions.unfavorite_org_opportunity!(id, scope: socket.assigns.scope)

    {:noreply, stream_insert_org_opportunity(socket, unfavorited)}
  end

  defp fetch_org_opportunities(params, sort_key, opts) do
    opts =
      Keyword.merge(
        [query: [sort_input: sort_key], page: [limit: 12]],
        opts
      )

    default_param = %{
      "statuses" => [],
      "states" => [],
      "search_text" => "",
      "preset_filter" => "all"
    }

    params = Map.merge(default_param, parse_params(params))

    %{
      statuses: params["statuses"],
      states: params["states"],
      search_text: params["search_text"],
      preset_filter: params["preset_filter"]
    }
    |> Boring.Acquisitions.read_org_opportunities_for_cards(opts)
    |> case do
      {:ok, org_opportunities} ->
        {:ok, org_opportunities}

      {:error, error} ->
        {:error, Ash.Error.to_error_class(error, error_context: "Getting org leads list")}
    end
  end

  def validate_sort_key(key) do
    valid_keys = Enum.map(sort_options(), &elem(&1, 1))

    if key in valid_keys do
      key
    else
      List.first(valid_keys)
    end
  end

  defp sort_options do
    [
      {"Date Updated | Newest-Oldest", "-updated_at"},
      {"Date Updated | Oldest-Newest", "updated_at"},
      {"Created At | Newest-Oldest", "-inserted_at"},
      {"Created At | Oldest-Newest", "inserted_at"},
      {"Name Descending | Z-A", "-opportunity.name"},
      {"Name Ascending | A-Z", "opportunity.name"}
    ]
  end

  defp status_options do
    Enum.map(
      OpportunityStatus.values(),
      &{OpportunityStatus.label(&1), &1}
    )
  end

  defp state_options(scope) do
    scope.current_tenant.id
    |> Boring.Locations.list_with_org_opportunities!(scope: scope)
    |> Enum.map(&{&1.id, &1.country_code, &1.name})
    |> Enum.group_by(
      fn {_id, country_code, _name} -> country_code end,
      fn {id, _country_code, name} -> {name, id} end
    )
  end

  @spec default_on_error(Socket.t(), String.t() | nil) ::
          Socket.t()
  defp default_on_error(socket, message) do
    route =
      if socket.assigns.current_org.slug do
        ~p"/app/org/#{socket.assigns.current_org.slug}"
      else
        ~p"/app/orgs"
      end

    socket
    |> push_navigate(to: route)
    |> put_flash(:error, message)
  end

  defp to_card_data(%Keyset{results: results}) do
    Enum.map(results, &to_card_data/1)
  end

  defp to_card_data(org_opportunity) do
    opportunity = org_opportunity.opportunity

    %{
      org_opportunity_id: org_opportunity.id,
      id: opportunity.id,
      status: org_opportunity.status,
      aerial_image_path: opportunity.aerial_image_path,
      name: opportunity.name,
      full_address: opportunity.full_address,
      street: opportunity.street,
      city: opportunity.city,
      state: opportunity.state.name,
      postal_code: opportunity.postal_code,
      display_phone_number: opportunity.display_phone_number,
      rating: opportunity.google_place.review_score,
      review_count: opportunity.google_place.review_count,
      follow_up: org_opportunity.follow_up,
      follow_up_status: org_opportunity.follow_up_status,
      display_approx_follow_up: org_opportunity.display_approx_follow_up,
      favorite?: org_opportunity.favorite?,
      business_claimed?: opportunity.google_place.is_business_claimed
    }
  end

  defp parse_params(params) do
    params
    |> allowed_params()
    |> remove_empty()
  end

  defp allowed_params(params) do
    Map.take(params, ["statuses", "states", "search_text", "order_by", "preset_filter"])
  end

  defp remove_empty(params) do
    params |> Enum.filter(fn {_key, val} -> val != "" end) |> Map.new()
  end

  def upload_bucket do
    PetalPro.config([:file_upload, :upload_bucket])
  end

  # Build the URL for the current page, including any sort/filter params and the
  # currently selected org opportunity ID.
  defp current_route(socket, params_overrides) do
    params =
      socket.assigns.params
      |> Map.merge(params_overrides)
      |> parse_params()

    encoded_params =
      if map_size(params) > 0 do
        "?#{Plug.Conn.Query.encode(params)}"
      end

    "/app/org/#{socket.assigns.current_org.slug}#{encoded_params}"
  end

  defp current_filter(%{"preset_filter" => filter}), do: filter
  defp current_filter(_), do: "all"

  defp assign_preset_filter_counts(socket) do
    assign(socket,
      preset_filter_counts: Acquisitions.get_org_opportunity_filter_counts!(scope: socket.assigns.scope)
    )
  end

  defp stream_org_opportunities(socket, params \\ %{}) do
    sort_key = validate_sort_key(params["order_by"])

    case fetch_org_opportunities(params, sort_key, scope: socket.assigns.scope) do
      {:ok, page} ->
        socket
        |> assign(
          params: params,
          sort_key: sort_key,
          page: page,
          selected_org_opportunity: nil
        )
        |> stream(:org_opportunity_cards, to_card_data(page), reset: true)

      {:error, error} ->
        Logger.error(error)

        default_on_error(socket, "Something went wrong. Redirecting to your default view")
    end
  end

  # Async task infrastructure for parallel query execution
  @mount_timeout 5_000

  defp execute_mount_queries_async(socket) do
    opts = [scope: socket.assigns.scope]

    Logger.debug("Starting async mount queries for org: #{socket.assigns.scope.current_tenant.id}")

    tasks = %{
      state_options:
        Task.async(fn ->
          state_options(socket.assigns.scope)
        end),
      preset_filter_counts:
        Task.async(fn ->
          Acquisitions.get_org_opportunity_filter_counts!(opts)
        end),
      org_opportunities:
        Task.async(fn ->
          case fetch_org_opportunities(%{}, "-updated_at", opts) do
            {:ok, page} -> page
            {:error, error} -> throw({:fetch_error, error})
          end
        end)
    }

    try do
      task_list = [
        tasks.state_options,
        tasks.preset_filter_counts,
        tasks.org_opportunities
      ]

      [state_options, preset_filter_counts, org_opportunities] =
        Task.await_many(task_list, @mount_timeout)

      results = %{
        state_options: state_options,
        preset_filter_counts: preset_filter_counts,
        org_opportunities: org_opportunities
      }

      Logger.debug("Async mount queries completed successfully")
      {:ok, results}
    catch
      :exit, {:timeout, _} ->
        Logger.warning("Mount async operations timed out after #{@mount_timeout}ms")
        # Ensure all tasks are shut down
        Enum.each(tasks, fn {_key, task} -> Task.shutdown(task, :brutal_kill) end)
        {:error, :timeout}

      {:fetch_error, error} ->
        Logger.warning("Mount async operation failed: #{inspect(error)}")
        # Ensure all tasks are shut down
        Enum.each(tasks, fn {_key, task} -> Task.shutdown(task, :brutal_kill) end)
        {:error, error}

      error ->
        Logger.warning("Unexpected error in mount async operations: #{inspect(error)}")
        # Ensure all tasks are shut down
        Enum.each(tasks, fn {_key, task} -> Task.shutdown(task, :brutal_kill) end)
        {:error, error}
    end
  end

  defp execute_mount_queries_sequential(socket) do
    Logger.debug("Executing mount queries sequentially as fallback")

    socket
    |> assign(%{
      page_title: socket.assigns.scope.current_tenant.name,
      state_options: state_options(socket.assigns.scope),
      status_options: status_options(),
      sort_options: sort_options()
    })
    |> assign_preset_filter_counts()
    |> stream_org_opportunities()
  end

  defp stream_insert_org_opportunity(socket, org_opportunity) do
    opts = Keyword.put([scope: socket.assigns.scope], :query, filter: [id: org_opportunity.id])

    [org_opportunity] =
      Acquisitions.read_org_opportunities_for_cards!(opts)

    socket
    |> assign_preset_filter_counts()
    |> stream_insert(:org_opportunity_cards, to_card_data(org_opportunity))
  end

  defp org_topic(socket), do: "orgs:#{socket.assigns.scope.current_tenant.id}"
end
