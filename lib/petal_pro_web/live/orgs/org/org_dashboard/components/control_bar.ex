defmodule PetalProWeb.OrgDashboard.Components.ControlBar do
  @moduledoc false
  use PetalProWeb, :component
  use PetalComponents

  attr :params, :map, required: true
  attr :state_options, :list, required: true
  attr :status_options, :list, required: true
  attr :sort_options, :list, required: true

  def control_bar(assigns) do
    ~H"""
    <.form
      for={%{}}
      phx-change="control_bar_change"
      class="relative z-0 mb-1.5 flex w-full flex-col gap-2 lg:justify-stretch lg:flex-row lg:space-x-2 xl:gap-0"
    >
      <.field
        id="control_bar_search_text"
        label=""
        name="search_text"
        placeholder="Search"
        value={@params["search_text"]}
        phx-debounce="500"
        wrapper_class="mb-0 w-full lg:1/4"
        class="text-sm md:text-md"
      />
      <.combo_box
        :if={@status_options}
        id="control_bar_statuses"
        multiple
        name="statuses"
        label=""
        placeholder="Status"
        options={@status_options}
        value={@params["statuses"]}
        wrapper_class="mb-0 w-full lg:1/4"
      />
      <.combo_box
        :if={@state_options}
        id="control_bar_states"
        multiple
        name="states"
        label=""
        placeholder="State"
        options={@state_options}
        value={@params["states"]}
        wrapper_class="mb-0 w-full lg:1/4"
      />
      <.combo_box
        :if={@sort_options}
        id="control_bar_order_by"
        label=""
        name="order_by"
        placeholder="Order By"
        options={@sort_options}
        value={@params["order_by"]}
        wrapper_class="mb-0 w-full lg:1/4"
      />
    </.form>
    """
  end
end
