defmodule PetalProWeb.OrgDashboard.OpportunityComponents do
  @moduledoc false
  use PetalProWeb, :component
  use PetalComponents
  use BoringWeb.Components

  import PetalProWeb.OrgOpportunity.OpportunityComponents

  attr :opportunity_card, :map, required: true
  attr :rest, :global

  def opportunity_card(assigns) do
    ~H"""
    <.card
      {@rest}
      phx-click="show_opportunity_details"
      phx-value-id={@opportunity_card.org_opportunity_id}
      class="cursor-pointer select-none shadow-xl transition-transform hover:scale-105"
      data-role="org-opportunity-card"
      data-id={@opportunity_card.org_opportunity_id}
    >
      <.card_media src={mapbox_image_url(@opportunity_card.aerial_image_path)} />
      <.image_not_found :if={!@opportunity_card.aerial_image_path} />
      <.card_content>
        <div class="flex w-full flex-col">
          <.h5 data-role="opportunity-name">{@opportunity_card.name}</.h5>
          <div class="text-sm">
            <.p :if={@opportunity_card.street} data-role="opportunity-street">
              {@opportunity_card.street}
            </.p>
            <.p
              :if={@opportunity_card.city || @opportunity_card.state || @opportunity_card.postal_code}
              data-role="opportunity-location"
            >
              {@opportunity_card.city}, {@opportunity_card.state} {@opportunity_card.postal_code}
            </.p>
            <.p :if={@opportunity_card.display_phone_number} class="w-full">
              {@opportunity_card.display_phone_number}
            </.p>
          </div>
        </div>
        <div class="mt-2 flex flex-row">
          <div class="flex w-full">
            <div data-role="rating" class="flex flex-row gap-1">
              <.rating rating={@opportunity_card.rating || 0} total={5} />
              <span class="text-sm text-gray-600 dark:text-gray-400">
                ({@opportunity_card.review_count || 0})
              </span>
            </div>
          </div>
          <div :if={not @opportunity_card.business_claimed?}>
            <.badge color="gray" variant="soft" label="Unclaimed on Google" size="sm" with_icon>
              <.icon name="hero-tag-solid" class="pb-[0.05rem] h-3 w-3" /> Unclaimed on Google
            </.badge>
          </div>
        </div>
      </.card_content>
      <.card_footer>
        <div class="mt-2 flex w-full flex-row">
          <.due_badge
            :if={@opportunity_card.follow_up}
            status={@opportunity_card.follow_up_status}
            due_in={@opportunity_card.display_approx_follow_up}
          />
          <.favorite_button
            item_id={@opportunity_card.org_opportunity_id}
            item_favorite?={@opportunity_card.favorite?}
          />
        </div>
      </.card_footer>
      <.badge
        class="rounded-none"
        color={status_badge_color(@opportunity_card.status)}
        variant="dark"
        label={Boring.Acquisitions.Types.OpportunityStatus.label(@opportunity_card.status)}
        size="lg"
        data-role="org-opportunity-card-status-badge"
        data-card-status-value={@opportunity_card.status}
        role="status"
      />
    </.card>
    """
  end

  def image_not_found(assigns) do
    ~H"""
    <div
      class="flex h-60 w-full flex-col items-center justify-center bg-gray-100"
      data-role="image-not-available"
    >
      <svg
        class="mb-2 h-12 w-12 text-gray-400"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <line x1="2" y1="2" x2="22" y2="22"></line>
        <path d="M10.41 10.41a2 2 0 1 1-2.83-2.83"></path>
        <line x1="13.5" y1="13.5" x2="6" y2="21"></line>
        <line x1="18" y1="12" x2="21" y2="15"></line>
        <path d="M3.59 3.59A1.99 1.99 0 0 0 3 5v14a2 2 0 0 0 2 2h14c.55 0 1.052-.22 1.41-.59"></path>
        <path d="M21 15V5a2 2 0 0 0-2-2H9"></path>
      </svg>
      <p class="text-sm text-gray-500">Image not available</p>
    </div>
    """
  end

  defp due_badge(assigns) do
    ~H"""
    <div class="flex items-center justify-center">
      <.badge color={due_badge_color(@status)} with_icon size="lg">
        <.icon name="hero-clock-solid" class="pb-[0.05rem] mr-1 h-4 w-4" />
        {due_badge_label(@status, @due_in)}
      </.badge>
    </div>
    """
  end

  defp due_badge_label(status, due_in) do
    case status do
      :due -> "Today"
      :overdue -> "Overdue"
      _ -> String.capitalize(due_in)
    end
  end

  defp due_badge_color(status) do
    case status do
      :overdue -> "danger"
      :due -> "danger"
      :due_soon -> "warning"
      :due_later -> "gray"
    end
  end

  defp status_badge_color(status) do
    case status do
      :new -> "info"
      :interested -> "secondary"
      :offer_made -> "primary"
      :offer_rejected -> "danger"
      :offer_accepted -> "success"
      :due_diligience_period -> "warning"
      :pending_close -> "secondary"
      :acquired -> "success"
      _ -> "gray"
    end
  end
end
