defmodule PetalProWeb.OrgDashboard.Components.NoOpportunitiesScreen do
  @moduledoc """
  Component displayed when an organization has no opportunities yet.
  Directs users to the Opportunity Search page.
  """
  use PetalProWeb, :component

  attr :org, :map, required: true

  def no_opportunities_screen(assigns) do
    ~H"""
    <div class="py-12 text-center">
      <div class="mx-auto max-w-md">
        <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          <.icon name="hero-magnifying-glass" class="h-6 w-6 text-blue-600" />
        </div>
        <h3 class="mt-6 text-lg font-semibold text-gray-900">
          {gettext("No opportunities yet")}
        </h3>
        <p class="mt-2 text-sm text-gray-500">
          {gettext(
            "Get started by searching for opportunities in your area and assigning them to your organization."
          )}
        </p>
        <div class="mt-6">
          <.button
            :if={@org.active_subscriber?}
            link_type="live_redirect"
            to={~p"/app/org/#{@org.slug}/search"}
            label={gettext("Search for Opportunities")}
            color="primary"
          />
        </div>
      </div>
    </div>
    """
  end
end
