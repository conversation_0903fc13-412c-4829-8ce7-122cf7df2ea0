defmodule PetalProWeb.OrgLayoutComponent do
  @moduledoc """
  A layout for any page scoped to an org. eg "Org dashboard", "Org settings", etc.
  """
  use PetalProWeb, :component

  alias PetalPro.Billing.Customers
  alias PetalPro.Billing.Subscriptions

  attr :socket, :map, required: true
  attr :current_user, :map, required: true
  attr :current_org, :map, required: true
  attr :current_membership, :map, required: true
  attr :current_page, :atom
  slot :inner_block

  def org_layout(assigns) do
    has_active_subscription = org_has_active_subscription?(assigns.current_org)
    is_admin = assigns.current_membership.role == :admin

    assigns =
      assigns
      |> assign(:has_active_subscription, has_active_subscription)
      |> assign(:is_admin, is_admin)

    ~H"""
    <.layout
      current_page={@current_page}
      current_user={@current_user}
      main_menu_items={build_menu(@current_membership, @current_org)}
      type="stacked"
      sidebar_title={@current_org.name}
      active_subscriber?={@has_active_subscription}
    >
      <.subscription_warning
        :if={not @has_active_subscription}
        org={@current_org}
        is_admin={@is_admin}
      />
      {render_slot(@inner_block)}
    </.layout>
    """
  end

  defp org_has_active_subscription?(org) do
    case Customers.get_customer_by_source(:org, org.id) do
      %{id: customer_id} ->
        case Subscriptions.get_active_subscription_by_customer_id(customer_id) do
          %{} -> true
          _ -> false
        end

      _ ->
        false
    end
  end

  attr :org, :map, required: true
  attr :is_admin, :boolean, required: true

  defp subscription_warning(assigns) do
    ~H"""
    <div class="mb-4 w-full">
      <div class="bg-warning-100 border-warning-500 flex rounded-none border-l-4 p-4 dark:bg-warning-900/30">
        <div class="mr-3 shrink-0">
          <.icon name="hero-exclamation-circle" class="text-warning-500 h-5 w-5" />
        </div>
        <div class="flex-1">
          <div class="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <p class="text-warning-800 text-sm font-medium dark:text-warning-200">
                {gettext("This organization does not have an active subscription")}
              </p>
              <p class="text-warning-700 mt-1 text-sm dark:text-warning-300">
                <%= if @is_admin do %>
                  {gettext(
                    "As an organization admin, you can update the subscription to access all features."
                  )}
                <% else %>
                  {gettext("Please contact your organization admin to update the subscription.")}
                <% end %>
              </p>
            </div>
            <div :if={@is_admin} class="flex justify-center sm:ml-4 sm:justify-end">
              <.button
                link_type="live_redirect"
                to={~p"/app/org/#{@org.slug}/subscribe"}
                label={gettext("Update Subscription")}
                color="warning"
                size="sm"
                variant="outline"
                class="whitespace-nowrap"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp build_menu(membership, org) do
    case membership.role do
      :member ->
        [
          get_link(:org_dashboard, org),
          get_link(:opportunity_search, org)
        ]

      :admin ->
        Enum.filter(
          [
            get_link(:org_dashboard, org),
            get_link(:opportunity_search, org),
            get_link(:org_settings, org)
          ],
          & &1
        )
    end
  end

  defp get_link(:org_dashboard, org) do
    %{
      name: :org_dashboard,
      path: ~p"/app/org/#{org.slug}",
      label: gettext("Deal Flow"),
      icon: "hero-square-3-stack-3d-solid"
    }
  end

  defp get_link(:org_settings, org) do
    %{
      name: :org_settings,
      path: ~p"/app/org/#{org.slug}/edit",
      label: gettext("Org Settings"),
      icon: "hero-cog"
    }
  end

  # defp get_link(:org_subscribe, org) do
  #   if Customers.entity() == :org do
  #     %{
  #       name: :org_subscribe,
  #       path: ~p"/app/org/#{org.slug}/subscribe",
  #       label: gettext("Subscribe"),
  #       icon: "hero-shopping-bag"
  #     }
  #   end
  # end

  defp get_link(:opportunity_search, org) do
    %{
      name: :opportunity_search,
      path: ~p"/app/org/#{org.slug}/search",
      label: gettext("Search"),
      icon: "hero-map-pin",
      subscriber_only?: true
    }
  end
end
