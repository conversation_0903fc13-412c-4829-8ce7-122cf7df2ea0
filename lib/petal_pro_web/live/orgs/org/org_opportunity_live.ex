defmodule PetalProWeb.OrgOpportunityLive do
  @moduledoc """
  View details for a single OrgOpportunity. Current user must be a member of the org.
  """
  use PetalProWeb, :live_view
  use BoringWeb.Components

  import PetalProWeb.OrgOpportunity.OpportunityComponents.Contacts
  import PetalProWeb.OrgOpportunity.OpportunityComponents.Files

  alias AshPhoenix.Form
  alias Boring.Acquisitions
  alias PetalPro.FileUploads.Tigris

  require Logger

  @impl Phoenix.LiveView
  def mount(_params, session, socket) do
    actor = Boring.Accounts.get_user_by_id!(socket.assigns.current_user.id, authorize?: false)

    tenant =
      Boring.Orgs.get_org_by_id!(session["tenant_id"],
        load: [:active_subscriber?, :billing_product],
        authorize?: false
      )

    socket = assign(socket, :scope, %Boring.Scope{current_user: actor, current_tenant: tenant})

    org_opportunity =
      Boring.Acquisitions.get_org_opportunity_for_slide_over!(
        session["org_opportunity_id"],
        scope: socket.assigns.scope
      )

    # There is a known issue when resetting the state of liveview file uploads if the upload component is reused for multiple uploads.
    # So each time we mount the upload component (even for the same org_opportunity) we need a new upload key/name.
    file_upload_key = "file-upload-key-" <> Ash.UUID.generate()

    if connected?(socket) do
      org_opportunity
      |> contacts_topic!(socket)
      |> PetalProWeb.Endpoint.subscribe()

      org_opportunity
      |> service_requests_topic!(socket)
      |> PetalProWeb.Endpoint.subscribe()
    end

    contact_options =
      [scope: socket.assigns.scope]
      |> Acquisitions.list_editable_contacts!()
      |> Enum.map(fn contact ->
        text = "#{contact.name} (#{contact.email}, #{contact.phone_number})"
        {text, contact.id}
      end)

    socket =
      socket
      |> stream_contacts(org_opportunity.contacts)
      |> assign(
        org_opportunity: org_opportunity,
        page_title: org_opportunity.opportunity.name,
        selected_tab: "contacts",
        skip_trace_in_progress_override: false,
        contact_form: nil,
        contact_options: contact_options
      )
      |> assign_forms(:all)
      |> assign(:file_upload_key, file_upload_key)
      |> allow_upload(file_upload_key,
        accept: ~w(.pdf .doc .docx .xls .xlsx .ppt .pptx .txt .csv .jpg .jpeg .png .gif .svg .webp),
        max_entries: 1,
        max_file_size: 20_000_000,
        external: &file_upload_preflight/2
      )

    {:ok, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("update_status", %{"status" => status}, socket) do
    case Boring.Acquisitions.update_org_opportunity_status(
           socket.assigns.org_opportunity,
           %{status: String.to_existing_atom(status)},
           scope: socket.assigns.scope
         ) do
      {:ok, updated_org_opportunity} ->
        socket =
          update(socket, :org_opportunity, fn org_opp ->
            Map.put(org_opp, :status, updated_org_opportunity.status)
          end)

        {:noreply, socket}

      {:error, error} ->
        error
        |> Ash.Error.to_error_class(error_context: "Updating OrgOpportunity status")
        |> Logger.error()

        {:noreply, socket}
    end
  end

  def handle_event("update_follow_up", %{"form" => form_params}, socket) do
    case AshPhoenix.Form.submit(socket.assigns.follow_up_form, params: form_params) do
      {:ok, updated_org_opportunity} ->
        socket =
          update(socket, :org_opportunity, fn org_opp ->
            Map.merge(
              org_opp,
              Map.take(updated_org_opportunity, [:follow_up, :display_follow_up_date])
            )
          end)

        {:noreply, socket}

      {:error, form} ->
        {:noreply, assign(socket, follow_up_form: form)}
    end
  end

  def handle_event("dismiss_follow_up", _params, socket) do
    case Boring.Acquisitions.clear_org_opportunity_follow_up(
           socket.assigns.org_opportunity,
           scope: socket.assigns.scope
         ) do
      {:ok, updated_org_opportunity} ->
        socket =
          update(socket, :org_opportunity, fn org_opp ->
            Map.put(org_opp, :follow_up, updated_org_opportunity.follow_up)
          end)

        {:noreply, socket}

      {:error, error} ->
        error
        |> Ash.Error.to_error_class(error_context: "Clearing follow up")
        |> Logger.error()

        {:noreply, socket}
    end
  end

  def handle_event("change_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :selected_tab, tab)}
  end

  def handle_event("remove_upload_file", %{"ref" => ref}, socket) do
    file_upload_key = socket.assigns.file_upload_key

    {:noreply, cancel_upload(socket, file_upload_key, ref)}
  end

  def handle_event("validate_upload_file", _params, socket) do
    {:noreply, socket}
  end

  def handle_event("upload_file", _params, socket) do
    file_upload_key = socket.assigns.file_upload_key

    on_consume = fn upload, _entry ->
      Acquisitions.cloud_file_upload_complete!(
        socket.assigns.cloud_file,
        %{
          remote_name: upload.fields["key"]
        },
        scope: socket.assigns.scope
      )

      {:ok, upload.fields["key"]}
    end

    Tigris.consume_uploaded_entries(socket, file_upload_key, on_consume)

    {:noreply, update(socket, :org_opportunity, &reload_processed_cloud_files/2)}
  end

  def handle_event("remove_stored_file", %{"id" => id}, socket) do
    socket =
      case Boring.Acquisitions.delete_cloud_file(id, scope: socket.assigns.scope) do
        :ok ->
          update(socket, :org_opportunity, &reload_processed_cloud_files/2)

        {:error, error} ->
          error
          |> Ash.Error.to_error_class(error_context: "Deleting cloud file")
          |> Logger.error()

          update_flash(socket, :error, "Something went wrong. Could not Delete file")
      end

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("update_notes", %{"notes_form" => form_data}, socket) do
    Form.submit!(socket.assigns.notes_form, params: form_data)
    {:noreply, socket}
  end

  def handle_event("add_contact", _params, socket) do
    contact_form =
      Acquisitions.form_to_create_opportunity_contact(
        socket.assigns.org_opportunity.opportunity_id,
        scope: socket.assigns.scope
      )

    {:noreply, assign(socket, contact_form: contact_form)}
  end

  def handle_event("skip_contact_lookup", _params, socket) do
    socket =
      update(socket, :contact_form, fn form ->
        AshPhoenix.Form.add_form(form, :contact)
      end)

    {:noreply, socket}
  end

  def handle_event("edit_contact", %{"id" => id}, socket) do
    contact_form =
      socket.assigns.org_opportunity.contacts
      |> Enum.find(&(&1.id == id))
      |> Acquisitions.form_to_update_opportunity_contact(scope: socket.assigns.scope)

    {:noreply, assign(socket, contact_form: contact_form)}
  end

  def handle_event("delete_contact", %{"id" => id}, socket) do
    opts = [scope: socket.assigns.scope]

    case Acquisitions.delete_opportunity_contact(id, opts) do
      :ok ->
        {:noreply, socket}

      {:error, error} ->
        Logger.error(error)

        socket =
          update_flash(socket, :error, "Something went wrong. Could not delete contact")

        {:noreply, socket}
    end
  end

  def handle_event("validate_contact_form", %{"form" => params}, socket) do
    socket =
      update(socket, :contact_form, fn form ->
        AshPhoenix.Form.validate(form, params)
      end)

    {:noreply, socket}
  end

  def handle_event("close_contact_form", _params, socket) do
    {:noreply, assign(socket, contact_form: nil)}
  end

  def handle_event("save_contact", %{"form" => params}, socket) do
    case AshPhoenix.Form.submit(socket.assigns.contact_form, params: params) do
      {:ok, _opp_contact} ->
        socket = assign(socket, contact_form: nil)

        {:noreply, socket}

      {:error, form} ->
        {:noreply, assign(socket, contact_form: form)}
    end
  end

  def handle_event("create_skip_trace_request", _params, socket) do
    case Boring.Services.create_service_request(
           socket.assigns.org_opportunity.opportunity_id,
           :skiptrace,
           scope: socket.assigns.scope
         ) do
      {:ok, _service_request} ->
        socket =
          socket
          |> assign(:skip_trace_in_progress_override, true)
          |> update_flash(:info, "Skip-trace request created")

        {:noreply, socket}

      {:error, _error} ->
        socket = update_flash(socket, :error, "Could not create skip-trace request")

        {:noreply, socket}
    end
  end

  @impl Phoenix.LiveView
  def handle_info(%{topic: "service_requests" <> _rest = received_topic} = event, socket) do
    subscribed_topic = service_requests_topic!(socket.assigns.org_opportunity, socket)
    %{event: event_name} = event

    if received_topic == subscribed_topic and event_name == "service_request_closed" do
      # A skip trace request for this org/opportunity has been processed
      # Contacts and the `skip_trace_in_progress?` flag need to be reloaded
      socket
      |> update(:org_opportunity, fn org_opportunity ->
        Map.update!(org_opportunity, :opportunity, fn opportunity ->
          Ash.load!(
            opportunity,
            [:skip_trace_in_progress?],
            scope: socket.assigns.scope
          )
        end)

        Map.put(org_opportunity, :latest_completed_skip_trace, DateTime.utc_now())
      end)
      |> assign(:skip_trace_in_progress_override, false)
      |> update_flash(:success, "Skiptrace completed")

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  def handle_info(%{topic: "opportunity_contacts" <> _rest = received_topic} = event, socket) do
    org_opportunity = socket.assigns.org_opportunity
    contacts_topic = contacts_topic!(org_opportunity, socket)

    if contacts_topic == received_topic do
      %{payload: %{data: contact}} = event

      contact =
        Ash.load!(
          contact,
          [
            :skip_traced?,
            :name,
            :email,
            :phone_number,
            contact: [
              address: [:formatted]
            ]
          ],
          scope: socket.assigns.scope
        )

      socket =
        case event do
          %{event: "create", payload: %{actor: actor}} ->
            socket =
              socket
              |> update(:org_opportunity, fn org_opportunity ->
                Map.put(org_opportunity, :total_contacts, org_opportunity.total_contacts + 1)
              end)
              |> stream_insert(:contacts, contact, at: 0)

            if not is_nil(actor) && actor.id == socket.assigns.scope.current_user.id do
              update_flash(socket, :success, "Contact saved")
            else
              socket
            end

          %{event: "update", payload: %{actor: actor}} ->
            socket = stream_insert(socket, :contacts, contact)

            if not is_nil(actor) && actor.id == socket.assigns.scope.current_user.id do
              update_flash(socket, :success, "Contact updated")
            else
              socket
            end

          %{event: "destroy", payload: %{actor: actor}} ->
            socket =
              socket
              |> update(:org_opportunity, fn org_opportunity ->
                Map.put(org_opportunity, :total_contacts, org_opportunity.total_contacts - 1)
              end)
              |> stream_delete(:contacts, contact)

            if not is_nil(actor) && actor.id == socket.assigns.scope.current_user.id do
              update_flash(socket, :info, "Contact deleted")
            else
              socket
            end

          _ ->
            socket
        end

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  def handle_info(_event, socket) do
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def terminate(_reason, socket) do
    socket.assigns.org_opportunity
    |> contacts_topic!(socket)
    |> PetalProWeb.Endpoint.unsubscribe()

    socket.assigns.org_opportunity
    |> service_requests_topic!(socket)
    |> PetalProWeb.Endpoint.unsubscribe()
  end

  def file_upload_preflight(entry, socket) do
    remote_filename = entry.uuid <> Path.extname(entry.client_name)

    # create a pending file object in the database
    cloud_file =
      Boring.Acquisitions.create_cloud_file!(
        %{
          remote_name: remote_filename,
          local_name: entry.client_name,
          opportunity_id: socket.assigns.org_opportunity.opportunity_id
        },
        scope: socket.assigns.scope
      )

    # we smuggle this into Tigris.presign_upload
    # if remote_filename exists it will use that as the S3/Tigris object key
    # otherwise it will generate a new UUID and use that
    client_meta =
      Map.put(entry.client_meta || %{}, :remote_filename, remote_filename)

    # when the file is fully uploaded, we use this to update the cloud file status
    # and set the remote name in the case that it has changed
    socket = assign(socket, cloud_file: cloud_file)

    entry
    |> Map.put(:client_meta, client_meta)
    |> Tigris.presign_upload(socket)
  end

  defp reload_processed_cloud_files(org_opportunity, assigns) do
    new_org_opportunity =
      Ash.load!(
        org_opportunity,
        [
          opportunity: [
            files: [
              :remote_name,
              :local_name,
              :status,
              :presigned_get_url
            ]
          ]
        ],
        scope: assigns.scope
      )

    Map.update!(org_opportunity, :opportunity, fn opportunity ->
      Map.put(opportunity, :files, new_org_opportunity.opportunity.files)
    end)
  end

  @forms [
    :follow_up,
    :notes
  ]

  defp assign_forms(socket, forms) do
    forms = List.wrap(forms)

    forms = if :all in forms, do: @forms, else: forms

    Enum.reduce(forms, socket, fn form, socket -> assign_form(socket, form) end)
  end

  defp assign_form(socket, :follow_up) do
    follow_up_form =
      Acquisitions.form_to_set_org_opportunity_follow_up(
        socket.assigns.org_opportunity,
        scope: socket.assigns.scope
      )

    assign(socket, :follow_up_form, to_form(follow_up_form))
  end

  defp assign_form(socket, :notes) do
    notes_form =
      Acquisitions.form_to_update_org_opportunity(
        socket.assigns.org_opportunity,
        scope: socket.assigns.scope,
        as: "notes_form"
      )

    assign(socket, :notes_form, to_form(notes_form))
  end

  defp assign_form(socket, _), do: socket

  defp update_flash(socket, type, message) do
    send(socket.parent_pid, {:update_flash, type, message})
    socket
  end

  defp stream_contacts(socket, opportunity_contacts) do
    socket
    |> stream_configure(:contacts, dom_id: &"contact-#{&1.id}")
    |> stream(:contacts, opportunity_contacts)
  end

  defp service_requests_topic!(org_opportunity, socket) do
    opts = [scope: socket.assigns.scope]
    Boring.Acquisitions.org_opportunity_service_requests_pubsub_topic!(org_opportunity, opts)
  end

  defp contacts_topic!(org_opportunity, socket) do
    opts = [scope: socket.assigns.scope]
    Boring.Acquisitions.org_opportunity_contacts_pubsub_topic!(org_opportunity, opts)
  end
end
