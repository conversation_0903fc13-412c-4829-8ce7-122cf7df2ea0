defmodule PetalProWeb.OrgFormComponent do
  @moduledoc false
  use PetalProWeb, :live_component

  alias AshPhoenix.Form

  @model "Organization"

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.form for={@form} phx-target={@myself} phx-submit="save" phx-change="validate">
        <.field field={@form[:name]} {alpine_autofocus()} phx-debounce="500" />
        <.field disabled field={@form[:slug]} />

        <div class="flex justify-end gap-2">
          <.button phx-disable-with={gettext("Saving...")}>{gettext("Save")}</.button>
        </div>
      </.form>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_form()}
  end

  @impl true
  def handle_event("validate", %{"form" => form_data}, socket) do
    socket =
      update(socket, :form, fn form ->
        Form.validate(form, form_data)
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("save", %{"form" => form_data}, socket) do
    case Form.submit(socket.assigns.form, params: form_data) do
      {:ok, org} ->
        socket =
          socket
          |> put_flash(
            :info,
            gettext("%{model} saved successfully", model: gettext(@model))
          )
          |> push_navigate(to: ~p"/app/org/#{org.slug}/edit")

        {:noreply, socket}

      {:error, form} ->
        socket =
          socket
          |> put_flash(:error, gettext("Unable to save %{model}", model: gettext(@model)))
          |> assign(:form, form)

        {:noreply, socket}
    end
  end

  defp assign_form(%{assigns: %{org: org}} = socket) do
    current_user = socket.assigns.current_user

    form =
      if org.id do
        ash_org =
          Boring.Orgs.get_org_by_id!(
            org.id,
            actor: current_user
          )

        Boring.Orgs.form_to_update_org(
          ash_org,
          actor: current_user,
          forms: [auto?: true]
        )
      else
        Boring.Orgs.form_to_create_org(
          actor: current_user,
          forms: [auto?: true]
        )
      end

    assign(socket, form: to_form(form))
  end
end
