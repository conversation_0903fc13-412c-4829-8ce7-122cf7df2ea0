defmodule PetalProWeb.OrgOpportunity.OpportunityComponents.Contacts do
  @moduledoc false
  use PetalProWeb, :component
  use PetalComponents

  alias <PERSON><PERSON>.Acquisitions

  attr :contacts_stream, :any, required: true
  attr :contact_form, :map, required: true
  attr :contact_options, :list, required: true
  attr :scope, :map, required: true
  attr :latest_completed_skip_trace, :string
  attr :skip_trace_in_progress_override, :boolean, default: false

  def contacts(assigns) do
    ~H"""
    <.container class="border-top-hidden overflow-y-scroll rounded-lg border border-gray-200 bg-white dark:shadow-3xl dark:border-gray-700 dark:bg-gray-800">
      <div class="mt-4 mb-0 flex flex-col gap-2 md:flex-row md:justify-end">
        <.button :if={!@contact_form} phx-click="add_contact" size="xs" with_icon color="primary">
          <.icon name="hero-plus-solid" class="h-4 w-4" />Add Contact
        </.button>
        <.button
          :if={
            @scope.current_tenant.billing_product in ["plus", "pro"] and
              @skip_trace_in_progress_override == false
          }
          color="light"
          phx-click="create_skip_trace_request"
          with_icon
          size="xs"
        >
          <.icon name="hero-identification" class="h-4 w-4" /> Run Skiptrace
        </.button>
        <.button
          :if={@skip_trace_in_progress_override == true}
          disabled
          color="light"
          loading
          size="sm"
          label="Skip-tracing..."
        />
      </div>

      <div class="overflow-y-scroll">
        <div :if={@contact_form}>
          <.contact_form form={@contact_form} contact_options={@contact_options} />
        </div>
        <div class="hidden only:block">
          <.container id="contacts" phx-update="stream" class="max-h-96 space-y-4 px-2 py-4">
            <div
              id="contacts-empty"
              class="min-h-44 hidden w-full place-items-center content-center only:block"
            >
              <div class="text-lg font-medium text-gray-500 dark:text-gray-400">
                <.icon name="hero-user" class="mr-2 h-6 w-6" /> No contacts added
              </div>
            </div>
            <.contact
              :for={{dom_id, contact} <- @contacts_stream}
              id={dom_id}
              opportunity_contact={contact}
              scope={@scope}
            />
          </.container>
        </div>
      </div>
    </.container>
    """
  end

  defp contact_form(assigns) do
    ~H"""
    <.card variant="outline" class="border-none">
      <.card_content heading={if @form.source.data.id, do: "Edit Contact", else: "Add Contact"}>
        <.form
          :let={form}
          id="contact_form"
          as={:form}
          for={@form}
          phx-change="validate_contact_form"
          phx-submit="save_contact"
          class="mt-4"
        >
          <span :if={!AshPhoenix.Form.value(form, :contact)}>
            <.combo_box
              options={@contact_options}
              label="Email or Phone Number"
              tom_select_options_global_variable="contactLookupTomSelectOptions"
              field={form[:contact_id]}
            />
          </span>
          <.inputs_for
            :let={contact_form}
            :if={AshPhoenix.Form.value(form, :contact)}
            field={form[:contact]}
          >
            <.field required field={contact_form[:name]} label="Name" phx-debounce="blur" />
            <.field required field={contact_form[:email]} label="Email" phx-debounce="blur" />
            <.field
              required
              field={contact_form[:phone_number]}
              label="Phone Number"
              phx-debounce="blur"
            />
          </.inputs_for>
          <.field
            required
            type="select"
            field={form[:role]}
            options={
              Enum.map(
                Acquisitions.Types.ContactRole.values(),
                &{Acquisitions.Types.ContactRole.label(&1), &1}
              )
            }
            label="Role"
          />
          <div class="mt-4 flex justify-end gap-2">
            <.button
              label="Cancel"
              type="button"
              color="gray"
              variant="outline"
              phx-click="close_contact_form"
            />
            <.button label="Save" />
          </div>
        </.form>
      </.card_content>
    </.card>
    """
  end

  attr :opportunity_contact, :map, required: true
  attr :scope, :map, required: true
  attr :rest, :global

  defp contact(assigns) do
    ~H"""
    <.card variant="outline" {@rest}>
      <.card_content>
        <div class="pc-card__heading flex flex-row justify-between">
          <div class="flex flex-col md:flex-row md:gap-3">
            <span>{@opportunity_contact.name}</span>
            <span :if={@opportunity_contact.role}>
              <.badge color="primary" variant="soft" class="mt-1 h-5">
                {Acquisitions.Types.ContactRole.label(@opportunity_contact.role)}
              </.badge>
            </span>
          </div>

          <.dropdown
            :if={
              Acquisitions.can_update_opportunity_contact?(
                @scope.current_user,
                @opportunity_contact,
                data: @opportunity_contact,
                tenant: @scope.current_tenant
              )
            }
            js_lib="live_view_js"
          >
            <.dropdown_menu_item
              :if={
                Acquisitions.can_update_opportunity_contact?(
                  @scope.current_user,
                  @opportunity_contact,
                  data: @opportunity_contact,
                  tenant: @scope.current_tenant
                )
              }
              label="Edit"
              phx-click="edit_contact"
              phx-value-id={@opportunity_contact.id}
            />
            <.dropdown_menu_item
              :if={
                Acquisitions.can_delete_opportunity_contact?(
                  @scope.current_user,
                  @opportunity_contact,
                  data: @opportunity_contact,
                  tenant: @scope.current_tenant
                )
              }
              label="Delete"
              phx-click="delete_contact"
              phx-value-id={@opportunity_contact.id}
              data-confirm="Are you sure you wish to delete this contact?"
            />
          </.dropdown>
        </div>
        <div class="flex flex-col md:flex-row">
          <div class="grow space-y-2">
            <p class="flex flex-row place-items-center text-sm text-gray-600">
              <.icon name="hero-envelope" class="size-4 mr-2 shrink-0" />
              {(@opportunity_contact.email && to_string(@opportunity_contact.email)) ||
                "Not found"}
            </p>
            <p class="flex flex-row place-items-center text-sm text-gray-600">
              <.icon name="hero-phone" class="size-4 mr-2 shrink-0" />
              {@opportunity_contact.phone_number || "Not found"}
            </p>
            <p class="flex flex-row place-items-center text-sm text-gray-600">
              <.icon name="hero-map-pin" class="size-4 mr-2 shrink-0" />
              {(@opportunity_contact.contact.address && @opportunity_contact.contact.address.formatted) ||
                "Not found"}
            </p>
          </div>
          <div class="shrink content-end pt-2 md:text-right">
            <div :if={@opportunity_contact.skip_traced?}>
              <.badge color="info" label="Skip-traced" />
            </div>
            <span class="text-sm">
              Last updated: {format_date(@opportunity_contact.updated_at, "{D} {Mshort} {YYYY}")}
            </span>
          </div>
        </div>
      </.card_content>
    </.card>
    """
  end
end
