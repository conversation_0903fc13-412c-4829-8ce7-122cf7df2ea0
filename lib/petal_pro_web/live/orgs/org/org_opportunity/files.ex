defmodule PetalProWeb.OrgOpportunity.OpportunityComponents.Files do
  @moduledoc false
  use PetalProWeb, :component
  use PetalComponents

  attr :files, :list, required: true
  attr :group_name, :string, default: "Files"
  attr :card_type, :atom, values: [:opp, :org_opp], required: true

  slot :sub_heading, required: false
  slot :inner_block, required: false

  def files(assigns) do
    ~H"""
    <.card variant="outline" class="mt-5">
      <.card_content heading={@group_name}>
        <.p class="pb-4 text-sm">
          {render_slot(@sub_heading)}
        </.p>
        <div :if={@files != []} class="grid grid-cols-1 gap-2">
          <.org_opp_file_card
            :for={
              %{id: cloud_file_id, local_name: filename, presigned_get_url: download_link} <- @files
            }
            :if={@card_type == :org_opp}
            filename={filename}
            download_link={download_link}
            cloud_file_id={cloud_file_id}
          />
          <.opp_file_card
            :for={%{local_name: filename, presigned_get_url: download_link} <- @files}
            :if={@card_type == :opp}
            filename={filename}
            download_link={download_link}
          />
        </div>

        <div :if={@files == []} class="outline-gray grid place-items-center p-8 outline-dotted">
          No files uploaded
        </div>
      </.card_content>

      <.card_footer>
        {render_slot(@inner_block)}
      </.card_footer>
    </.card>
    """
  end

  attr :filename, :string, required: true
  attr :download_link, :string, required: true

  defp opp_file_card(assigns) do
    ~H"""
    <.file_card filename={@filename}>
      <.icon_button link_type="a" tooltip="Download" to={@download_link} download target="_blank">
        <.icon name="hero-document-arrow-down" class="h-6 w-6" />
      </.icon_button>
    </.file_card>
    """
  end

  attr :filename, :string, required: true
  attr :download_link, :string, required: true
  attr :cloud_file_id, :string, required: true

  defp org_opp_file_card(assigns) do
    ~H"""
    <.file_card filename={@filename}>
      <.icon_button link_type="a" tooltip="Download" to={@download_link} download target="_blank">
        <.icon name="hero-document-arrow-down" class="h-6 w-6" />
      </.icon_button>

      <.icon_button
        link_type="button"
        tooltip="Remove"
        phx-click="remove_stored_file"
        phx-value-id={@cloud_file_id}
      >
        <.icon name="hero-x-mark-mini" class="h-6 w-6" />
      </.icon_button>
    </.file_card>
    """
  end

  attr :filename, :string, required: true

  slot :inner_block, required: false

  defp file_card(assigns) do
    ~H"""
    <div class="flex flex-row items-center justify-between gap-2 rounded-lg border border-gray-200 p-2 dark:border-gray-700">
      <div class="flex flex-row items-center gap-2">
        <.icon name="hero-document" class="h-6 w-6" />
        <span class="max-w-80 text-nowrap truncate">{@filename}</span>
      </div>

      <div class="min-w-20 flex flex-row items-center gap-2">
        {render_slot(@inner_block)}
      </div>
    </div>
    """
  end
end
