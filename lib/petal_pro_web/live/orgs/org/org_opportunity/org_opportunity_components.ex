defmodule PetalProWeb.OrgOpportunity.OpportunityComponents do
  @moduledoc false
  use PetalProWeb, :component

  attr :created_at, :string, required: true
  attr :updated_at, :string, required: true
  attr :class, :string, default: ""

  def created_updated(assigns) do
    ~H"""
    <span class={["text-xs text-gray-400 dark:text-gray-500", @class]}>
      Added {@created_at}
      {if @updated_at != @created_at do
        " · Updated #{@updated_at}"
      end}
    </span>
    """
  end

  # Fetching mapbox image could fail when converting google place to opportunity,
  # therefore cannot garantee that the image will be available.
  def mapbox_image_url(nil), do: ""

  def mapbox_image_url(filename) do
    file_io().read_path(filename)
  end

  defp file_io do
    Application.get_env(:boring, :mapbox)[:io_backend] || PetalProWeb.Mapbox.FileOps
  end
end
