<.container max_width="full">
  <div
    class="flex flex-col gap-4"
    data-role="org-opportunity-details"
    data-id={@org_opportunity.id}
  >
    <div class="w-full rounded-xl border border-gray-300 bg-gray-50 p-6 dark:border-gray-700 dark:bg-gray-800">
      <div class="flex flex-col gap-4 md:flex-row">
        <div class="w-full">
          <.h1 class="text-3xl font-bold">{@org_opportunity.opportunity.name}</.h1>
          <.p
            :if={@org_opportunity.opportunity.full_address}
            class="text-md font-medium text-gray-500 dark:text-gray-400"
          >
            {@org_opportunity.opportunity.full_address}
          </.p>
          <.p
            :if={@org_opportunity.opportunity.display_phone_number}
            class="text-sm font-medium text-gray-500 dark:text-gray-400"
          >
            {@org_opportunity.opportunity.display_phone_number}
          </.p>
          <div class="flex flex-wrap items-center gap-2">
            <.rating
              :if={@org_opportunity.opportunity.google_place.review_score}
              rating={@org_opportunity.opportunity.google_place.review_score}
              total={5}
            />
            <span class="hidden sm:block">
              •
            </span>
            <.p
              :if={@org_opportunity.opportunity.google_place.review_count}
              class="mt-2 text-sm text-gray-600 dark:text-gray-400 md:text-md"
            >
              {@org_opportunity.opportunity.google_place.review_count} reviews
            </.p>
          </div>
          <div class="mt-4 w-full sm:mt-2 sm:w-fit">
            <.button
              :if={@org_opportunity.opportunity.website}
              with_icon
              color="light"
              size="sm"
              target="_blank"
              link_type="a"
              label="Website"
              to={@org_opportunity.opportunity.website}
            >
              <.icon name="hero-globe-alt" class="h-4 w-4" /> Business Website
            </.button>
          </div>
        </div>
        <div class="flex flex-col gap-2 sm:w-fit md:place-self-start lg:flex-row-reverse">
          <.form
            for={%{}}
            phx-change="update_status"
            data-role="org-opportunity-details-update-status-form"
            class="min-w-48 w-full"
          >
            <.field
              data-role="org-opportunity-details-update-status-select"
              data-select-status-value={@org_opportunity.status}
              wrapper_class="md:mb-0"
              class="text-sm md:text-md"
              label=""
              name="status"
              type="select"
              options={
                [
                  @org_opportunity.status
                  | AshStateMachine.possible_next_states(@org_opportunity)
                ]
                |> Enum.map(&{Boring.Acquisitions.Types.OpportunityStatus.label(&1), &1})
              }
              value={@org_opportunity.status}
              label_class="hidden"
            />
          </.form>
          <div class="relative w-full">
            <div
              tabindex="0"
              class="min-w-56 pc-text-input flex flex-row items-center justify-between border border-gray-300 bg-white px-2 py-2 text-sm focus:ring-primary-500 focus:ring-1 dark:bg-auto md:text-md"
              phx-click={JS.toggle(to: "#followup-dropdown")}
            >
              <.icon name="hero-clock-solid" class="pb-[0.05rem] h-5 w-6" /> {if @org_opportunity.follow_up do
                "Due on #{@org_opportunity.display_follow_up_date}"
              else
                "Set Follow Up"
              end}
              <div class="flex flex-row-reverse">
                <.icon
                  :if={!@org_opportunity.follow_up}
                  name="hero-chevron-down-mini"
                  class="bg-gray-500"
                />
                <.icon
                  :if={@org_opportunity.follow_up}
                  class="bg-gray-500"
                  name="hero-x-mark-mini"
                  phx-click="dismiss_follow_up"
                  phx-value-id={@org_opportunity.id}
                />
              </div>
            </div>
            <div
              id="followup-dropdown"
              phx-click-away={JS.hide(to: "#followup-dropdown")}
              class="absolute hidden rounded-lg border border-gray-300 bg-white dark:border-gray-600 dark:bg-gray-800"
            >
              <.form for={@follow_up_form} phx-submit="update_follow_up">
                <div class="-mb-4 flex flex-row items-center gap-2 px-2">
                  <.field
                    label=""
                    value={1}
                    field={@follow_up_form[:time_value]}
                    type="text"
                    class="w-12"
                  />
                  <.field
                    label=""
                    value={:week}
                    field={@follow_up_form[:time_unit]}
                    type="select"
                    options={[{"Day(s)", :day}, {"Week(s)", :week}, {"Month(s)", :month}]}
                    class="w-32"
                  />
                  <.button :if={@org_opportunity.follow_up} class="mb-4">Reschedule</.button>
                  <.button :if={!@org_opportunity.follow_up} class="mb-4">Schedule</.button>
                </div>
              </.form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex flex-col gap-4 xl:flex-row">
      <div class="flex w-full flex-col gap-6">
        <div
          id="opportunity-details-tabs"
          class="rounded-lg border border-gray-200 px-6 pb-4 dark:border-gray-700"
        >
          <.tabs class="mt-2 mb-4 items-center gap-2" underline>
            <.tab
              underline
              is_active={@selected_tab == "contacts"}
              phx-click="change_tab"
              phx-value-tab="contacts"
            >
              <.icon name="hero-user-group" class="mr-2 h-5 w-5" />
              <span>Contacts</span>
              <span class="ml-2">
                <.badge
                  color="primary"
                  variant="dark"
                  class="size-5 rounded-full"
                  label={@org_opportunity.total_contacts}
                />
              </span>
            </.tab>
            <.tab
              underline
              is_active={@selected_tab == "files"}
              phx-click="change_tab"
              phx-value-tab="files"
            >
              <.icon name="hero-document-text" class="mr-2 h-5 w-5" /> Files
            </.tab>
          </.tabs>
          <.contacts
            :if={@selected_tab == "contacts"}
            contacts_stream={@streams.contacts}
            contact_form={@contact_form}
            contact_options={@contact_options}
            skip_trace_in_progress_override={@skip_trace_in_progress_override}
            scope={@scope}
          />
          <div :if={@selected_tab == "files"}>
            <.files files={@org_opportunity.opportunity.files} card_type={:org_opp}>
              <:sub_heading>
                This is a space for you to upload & download files related to this business.<br />
                All of these files are private to your organization. Only people in your organization can see them.
              </:sub_heading>

              <div class="mt-5">
                <.form for={%{}} phx-submit="upload_file" phx-change="validate_upload_file">
                  <PetalProWeb.FileUploadComponents.file_input
                    upload={@uploads[@file_upload_key]}
                    on_delete="remove_upload_file"
                  />
                </.form>
              </div>
            </.files>
          </div>
        </div>
        <div class="rounded-lg border border-gray-200 px-6 pt-4 dark:border-gray-700">
          <.h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 md:text-md lg:text-lg">
            Notes
          </.h3>
          <.form for={@notes_form} phx-change="update_notes">
            <.field
              field={@notes_form[:notes]}
              type="textarea"
              phx-debounce="250"
              class="h-40 w-full lg:h-80"
              label_class="hidden"
            />
          </.form>
        </div>
      </div>
    </div>
  </div>

  <div class="mx-auto max-w-3xl px-4 sm:px-6 md:flex md:justify-between md:space-x-5 lg:max-w-full lg:px-8">
    <div class="flex w-full flex-col gap-6 lg:flex-row">
      <div class="flex h-full w-full justify-end"></div>
    </div>
  </div>
  <div class="mx-auto my-10 max-w-3xl px-4 sm:px-6 md:flex md:justify-between md:space-x-5 lg:flex lg:max-w-full lg:flex-row-reverse lg:px-8">
  </div>
</.container>
