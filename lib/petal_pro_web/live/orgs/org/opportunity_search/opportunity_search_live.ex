defmodule PetalProWeb.OpportunitySearchLive do
  @moduledoc """
  Interactive map-based search interface for discovering self-storage investment opportunities.

  This LiveView provides a Google Maps interface where users can:
  - Place search circles to define geographic areas
  - Search for self-storage facilities using Google Places API
  - View detailed business information and contact data
  - Track quota usage against subscription entitlements
  - Navigate between map markers and list views

  ## Features

  - **Interactive Map**: Click to place search circles, drag to reposition
  - **Places Search**: Integration with Google Places API for business discovery
  - **Entitlement Tracking**: Progress indicators showing quota usage
  - **Responsive Design**: Synchronized map and list views
  - **Real-time Updates**: Async search with loading states

  ## Quota Management

  Organizations have opportunity assignment quotas based on their billing cycle.
  Quotas reset at the start of each billing period (monthly or annual subscriptions).
  When quotas are exhausted, users see a quota exhausted screen with upgrade options
  and information about when more assignments will be available at the next billing cycle.
  """
  use PetalProWeb, :live_view
  use BoringWeb.Components

  import PetalProWeb.Components.GoogleMap
  import PetalProWeb.Components.GoogleMapPlaceList
  import PetalProWeb.OrgLayoutComponent

  alias Boring.Api.Google, as: GoogleApi
  alias PetalProWeb.Components.QuotaExhaustedScreen
  alias Phoenix.LiveView.AsyncResult

  on_mount {PetalProWeb.SubscriptionPlugs, :subscribed_entity_only}

  @impl Phoenix.LiveView
  def mount(params, _session, socket) do
    if connected?(socket) do
      PetalProWeb.Endpoint.subscribe(org_topic(socket))
    end

    scope = socket.assigns.scope

    org_with_entitlement =
      Ash.load!(
        scope.current_tenant,
        [
          :remaining_opportunity_entitlement,
          :opportunities_assigned_this_interval,
          customer: [active_subscription: :current_period_end_at]
        ],
        scope: socket.assigns.scope
      )

    remaining = org_with_entitlement.remaining_opportunity_entitlement || 0
    assigned_this_month = org_with_entitlement.opportunities_assigned_this_interval || 0
    total_entitlement = remaining + assigned_this_month

    period_end_date =
      case org_with_entitlement.customer do
        %{active_subscription: %{current_period_end_at: end_date}} when not is_nil(end_date) ->
          end_date

        _ ->
          nil
      end

    socket =
      socket
      |> assign(:page_title, gettext("Opportunity Search"))
      |> assign(:markers, [])
      |> stream_configure(:places, dom_id: &"place-item-#{&1.id}")
      |> stream(:places, [])
      |> assign(:places_by_id, %{})
      |> assign(:selected_place, nil)
      |> assign(:search_result, %AsyncResult{})
      |> assign(
        entitlement_used: assigned_this_month,
        entitlement_total: total_entitlement,
        entitlement_remaining: remaining,
        period_end_date: period_end_date,
        assign_place_loading_id: nil,
        location: params["location"] || nil,
        location_center: params["location_center"] || nil,
        market_center: params["market_center"] || nil,
        market_radius: parse_radius(params["market_radius"]) || 5,
        market: nil,
        form: nil,
        current_zoom: 4,
        # Only allow circle dragging at zoom level 12 or less
        circle_draggable: true
      )

    {:ok, socket}
  end

  @impl Phoenix.LiveView
  def handle_params(params, _uri, socket) do
    form_params =
      params
      |> Map.take(["location", "location_center", "market_center", "market_radius"])
      |> Enum.reject(fn {_key, val} -> val == "" or is_nil(val) end)
      |> Map.new()

    socket =
      if is_nil(socket.assigns.form) do
        assign(socket, :form, to_form(form_params, as: :search))
      else
        socket
      end

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_info(%{event: "opportunity_assigned"}, socket) do
    socket =
      socket
      |> assign(:entitlement_used, socket.assigns.entitlement_used + 1)
      |> assign(:entitlement_remaining, socket.assigns.entitlement_remaining - 1)

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_info(%{event: "opportunity_unassigned"}, socket) do
    socket =
      socket
      |> assign(:entitlement_used, socket.assigns.entitlement_used - 1)
      |> assign(:entitlement_remaining, socket.assigns.entitlement_remaining + 1)

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_info(_event, socket) do
    {:noreply, socket}
  end

  # defp patch_params(socket) do
  #   params =
  #     socket.assigns.form.params
  #     |> Map.take(["location", "market_radius"])
  #     |> Map.put(:location_center, socket.assigns.location_center)
  #     |> Map.put(:market_center, socket.assigns.market_center)
  #     |> Enum.reject(fn {_key, val} -> val == "" or is_nil(val) end)
  #     |> Map.new()

  #   push_patch(socket, to: ~p"/app/org/#{socket.assigns.current_org.slug}/search?#{params}")
  # end

  @impl Phoenix.LiveView
  def handle_event("search_location", %{"search" => %{"location" => location_value}}, socket)
      when byte_size(location_value) > 5 do
    case GoogleApi.geocode(location_value) do
      {:ok, %{results: [first_match | _]}} ->
        %{geometry: %{location: matching_location}} = first_match

        new_location_center = map_to_point(matching_location)

        socket =
          socket
          |> set_map_center(matching_location, zoom: 11)
          |> assign(
            location: location_value,
            location_center: point_to_param(new_location_center)
          )

        # Automatically place a search circle at the geocoded location
        new_market =
          Boring.Geo.create_circle!(new_location_center, socket.assigns.market_radius, :miles)

        # Clear any existing shapes first
        socket =
          socket
          |> push_event("map_clear_shapes", %{target: "opportunity-map"})
          |> set_market(new_market)
          |> push_event("map_add_shapes", %{
            target: "opportunity-map",
            shapes: [market_to_map_circle(new_market, socket.assigns.circle_draggable)]
          })

        {:noreply, socket}

      {:ok, %{results: []}} ->
        {:noreply, socket}

      {:error, _} ->
        {:noreply, socket}
    end
  end

  @impl Phoenix.LiveView
  def handle_event("set_market_radius", %{"search" => %{"market_radius" => radius}}, socket) do
    new_radius = parse_radius(radius)

    socket = assign(socket, :market_radius, new_radius)

    socket =
      if socket.assigns.market do
        %{center: center} = socket.assigns.market.properties

        new_market = Boring.Geo.create_circle!(center, new_radius, :miles)

        socket
        |> set_market(new_market)
        |> push_event(
          "update_shape",
          %{
            target: "opportunity-map",
            shapeId: "*",
            updates: %{
              radius: Boring.Geo.get_circle_radius_in_units!(new_market, :meters),
              draggable: socket.assigns.circle_draggable
            }
          }
        )
      else
        socket
      end

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("search_places", _params, socket) do
    market = socket.assigns.market
    opts = [scope: socket.assigns.scope]

    socket =
      socket
      |> clear_search_results()
      |> assign(:search_result, AsyncResult.loading())
      |> assign(:selected_place, nil)
      |> start_async(:search_places, fn ->
        search_places(market, opts)
      end)

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("map_event", %{"type" => "click", "data" => %{"position" => center}}, socket) do
    # Create market circle
    center_point = map_to_point(%{lat: center["lat"], lng: center["lng"]})
    new_market = Boring.Geo.create_circle!(center_point, socket.assigns.market_radius, :miles)

    # # Update assigns and URL
    socket =
      socket
      |> set_market(new_market)
      |> push_event("map_add_shapes", %{
        target: "opportunity-map",
        shapes: [market_to_map_circle(new_market, socket.assigns.circle_draggable)]
      })

    {:noreply, socket}
  end

  def handle_event("map_event", %{"type" => "zoom_changed", "data" => data}, socket) do
    new_zoom = data["zoom"] || 4
    # Only allow circle dragging at zoom level 12 or less
    new_draggable = new_zoom <= 12

    socket =
      socket
      |> assign(:current_zoom, new_zoom)
      |> assign(:circle_draggable, new_draggable)

    # Update existing circle if present
    socket =
      if socket.assigns.market do
        push_event(socket, "update_shape", %{
          target: "opportunity-map",
          shapeId: "*",
          updates: %{
            draggable: new_draggable
          }
        })
      else
        socket
      end

    {:noreply, socket}
  end

  def handle_event("map_event", _params, socket) do
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("map_error", %{"error" => error}, socket) do
    socket = put_flash(socket, :error, "Map Error: #{error}")
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("shape_modified", %{"type" => "dragend", "geometry" => geometry}, socket) do
    [lng, lat] = geometry["geometry"]["coordinates"]

    new_center = map_to_point(%{lat: lat, lng: lng})
    new_market = Boring.Geo.create_circle!(new_center, socket.assigns.market_radius, :miles)

    socket = set_market(socket, new_market)

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("shape_modified", _params, socket) do
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("marker_event", %{"type" => "gmp-click", "markerId" => marker_id}, socket) do
    case Map.get(socket.assigns.places_by_id, marker_id) do
      nil ->
        {:noreply, socket}

      place ->
        socket =
          socket
          |> assign(:selected_place, place)
          |> set_map_center(%{lat: place.latitude, lng: place.longitude}, zoom: 16)
          |> push_event("select_marker", %{
            target: "opportunity-map",
            markerId: marker_id
          })
          |> push_event("scroll_into_view", %{
            target_id: "place-item-#{marker_id}"
          })

        {:noreply, socket}
    end
  end

  def handle_event("marker_event", _params, socket) do
    {:noreply, socket}
  end

  def handle_event("select_place_from_list", %{"place_id" => place_id}, socket) do
    case Map.get(socket.assigns.places_by_id, place_id) do
      nil ->
        {:noreply, socket}

      place ->
        socket =
          socket
          |> assign(:selected_place, place)
          |> set_map_center(%{lat: place.latitude, lng: place.longitude}, zoom: 16)
          |> push_event("select_marker", %{
            target: "opportunity-map",
            markerId: place_id
          })

        {:noreply, socket}
    end
  end

  @impl Phoenix.LiveView
  def handle_event("assign_place", %{"place_id" => place_id}, socket) do
    opts =
      [scope: socket.assigns.scope, load: [opportunity: [:assigned_to_org?, :google_place]]]

    case Boring.Acquisitions.assign_opportunity(%{opportunity_id: place_id}, opts) do
      {:ok, org_opportunity} ->
        socket =
          socket
          |> assign(:assign_place_loading_id, nil)
          |> stream_insert(:places, org_opportunity.opportunity)
          |> update(:places_by_id, fn places_by_id ->
            Map.put(places_by_id, org_opportunity.opportunity.id, org_opportunity.opportunity)
          end)
          |> update(:markers, fn markers ->
            Enum.map(markers, fn marker ->
              if marker.id == org_opportunity.opportunity.id do
                Map.put(marker, :color, %{
                  background: "#6B7280",
                  border: "#6B7280",
                  glyph: "#6B7280"
                })
              else
                marker
              end
            end)
          end)
          |> push_event("update_markers", %{
            target: "opportunity-map",
            markers: socket.assigns.markers
          })

        if socket.assigns.scope.current_tenant.billing_product in ["plus", "pro"] do
          Boring.Services.create_service_request(
            org_opportunity.opportunity_id,
            :skiptrace,
            scope: socket.assigns.scope
          )
        end

        {:noreply, socket}

      {:error, _error} ->
        socket =
          socket
          |> assign(:assign_place_loading_id, nil)
          |> put_flash(:error, "Failed to assign")

        {:noreply, socket}
    end
  end

  @impl Phoenix.LiveView
  def handle_event(_event, _params, socket), do: {:noreply, socket}

  @impl Phoenix.LiveView
  def handle_async(:search_places, {:ok, opportunities}, socket) do
    %{search_result: async_result} = socket.assigns

    opportunities_by_id =
      Map.new(opportunities, fn opportunity -> {opportunity.id, opportunity} end)

    markers = Enum.map(opportunities, &place_to_marker/1)

    opportunity_count = Enum.count(opportunities)

    opportunity_plural =
      if opportunity_count == 0 or opportunity_count > 1 do
        gettext("opportunities")
      else
        gettext("opportunity")
      end

    socket =
      socket
      |> assign(
        :search_result,
        AsyncResult.ok(async_result, opportunities)
      )
      |> stream(:places, opportunities, reset: true)
      |> assign(:places_by_id, opportunities_by_id)
      |> assign(:markers, markers)
      |> push_event("update_markers", %{
        target: "opportunity-map",
        markers: markers
      })
      |> put_flash(
        :success,
        "#{gettext("Found")} #{opportunity_count} #{opportunity_plural}"
      )

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_async(:search_places, {:exit, reason}, socket) do
    %{search_result: async_result} = socket.assigns

    socket =
      socket
      |> assign(
        :search_result,
        AsyncResult.failed(async_result, {:exit, reason})
      )
      |> put_flash(:error, gettext("Search failed"))

    {:noreply, socket}
  end

  defp set_market(socket, new_market) do
    socket
    |> assign(:market, new_market)
    |> assign(:market_center, point_to_param(new_market.properties.center))
    |> assign(:market_radius, new_market.properties.radius.value)
  end

  defp market_to_map_circle(market, draggable) do
    %{
      id: "circle-#{System.unique_integer([:positive])}",
      type: :circle,
      center: point_to_map(market.properties.center),
      radius: Boring.Geo.get_circle_radius_in_units!(market, :meters),
      strokeColor: "#0066CC",
      strokeOpacity: 1.0,
      strokeWeight: 2,
      fillColor: "#0066CC",
      fillOpacity: 0.1,
      editable: false,
      draggable: draggable
    }
  end

  # Helper functions

  defp clear_search_results(socket) do
    socket
    |> assign(:markers, [])
    |> stream(:places, [], reset: true)
    |> assign(:places_by_id, %{})
    |> assign(:selected_place, nil)
    |> push_event("map_clear_places", %{
      target: "opportunity-map"
    })
    |> push_event("deselect_markers", %{target: "opportunity-map"})
  end

  defp place_to_marker(place) do
    if place.latitude && place.longitude do
      marker = %{
        id: place.id,
        position: %{lat: place.latitude, lng: place.longitude},
        title: place.name,
        draggable: false
      }

      if place.assigned_to_org? do
        Map.put(marker, :color, %{
          background: "#6B7280",
          border: "#6B7280",
          glyph: "#6B7280"
        })
      else
        marker
      end
    end
  end

  defp set_map_center(socket, %{lat: lat, lng: lng}, opts) do
    socket =
      if opts[:zoom] do
        new_draggable = opts[:zoom] <= 12

        socket
        |> assign(:current_zoom, opts[:zoom])
        |> assign(:circle_draggable, new_draggable)
      else
        socket
      end

    push_event(socket, "map_center_on", %{
      target: "opportunity-map",
      center: %{"lng" => lng, "lat" => lat},
      zoom: opts[:zoom]
    })
  end

  defp search_places(market, opts) do
    {low_lng, low_lat, high_lng, high_lat} = Boring.Geo.get_circle_bbox!(market)

    location_restriction = %{
      rectangle: %{
        low: %{
          latitude: low_lat,
          longitude: low_lng
        },
        high: %{
          latitude: high_lat,
          longitude: high_lng
        }
      }
    }

    case GoogleApi.text_search("self storage", location_restriction: location_restriction) do
      {:ok, %GoogleApi.Places.TextSearchResponse{places: api_places}} ->
        api_places
        |> Enum.map(&to_google_place/1)
        |> Boring.Google.create_place_from_google(
          Keyword.merge(opts,
            load: [state: [:id]],
            bulk_options: [return_records?: true, return_errors?: false],
            authorize?: false
          )
        )
        |> then(fn
          %Ash.BulkResult{records: google_places} -> google_places
        end)
        |> Enum.map(&to_opportunity/1)
        |> Boring.Acquisitions.create_opportunity(
          Keyword.merge(opts,
            bulk_options: [return_records?: true, return_errors?: true],
            authorize?: false
          )
        )
        |> then(fn
          %Ash.BulkResult{records: opportunities} -> opportunities
        end)
        |> Enum.map(& &1.id)
        |> Boring.Acquisitions.list_off_market_candidates_by_ids!(opts)

      {:error, error} ->
        raise error
    end
  end

  defp to_opportunity(google_place) do
    %{
      name: google_place.business_name,
      full_address: google_place.full_address,
      city: google_place.city,
      street: google_place.street,
      postal_code: google_place.postal_code,
      latitude: google_place.latitude,
      longitude: google_place.longitude,
      website: google_place.website,
      phone_number: google_place.phone_number,
      google_place_id: google_place.id,
      state_id: google_place.state.id
    }
  end

  defp to_google_place(%Boring.Api.Google.Places.Place{} = api_place) do
    %{
      place_id: api_place.id,
      business_name: api_place.display_name && api_place.display_name.text,
      full_address: api_place.formatted_address,
      city: api_place.city,
      street: api_place.street,
      postal_code: api_place.postal_code,
      google_state: api_place.state,
      google_country: api_place.country,
      latitude: api_place.location && api_place.location.latitude,
      longitude: api_place.location && api_place.location.longitude,
      phone_number: api_place.national_phone_number,
      website: api_place.website_uri,
      review_score: api_place.rating || 0.0,
      review_count: api_place.user_rating_count || 0,
      categories: api_place.types || [],
      permanently_closed: api_place.business_status == "CLOSED_PERMANENTLY",
      temporarily_closed: api_place.business_status == "CLOSED_TEMPORARILY"
    }
  end

  # Coordinate parsing helpers using Geo WKB format
  # defp param_to_point(nil), do: nil
  # defp param_to_point(""), do: nil

  # defp param_to_point(wkb_str) when is_binary(wkb_str) do
  #   wkb_str
  #   |> String.split(",")
  #   |> List.to_tuple()
  #   |> then(&%Geo.Point{coordinates: &1})
  # rescue
  #   _ -> nil
  # end

  defp point_to_param(nil), do: nil

  defp point_to_param(%Geo.Point{} = point) do
    point.coordinates
    |> Tuple.to_list()
    |> Enum.join(",")
  end

  # Helper to convert between formats for backward compatibility
  defp point_to_map(%Geo.Point{coordinates: {lng, lat}}), do: %{lat: lat, lng: lng}
  defp map_to_point(%{lat: lat, lng: lng}), do: %Geo.Point{coordinates: {lng, lat}}

  # Helper to safely parse radius values
  defp parse_radius(radius_param, default \\ 5) do
    case radius_param do
      value when is_binary(value) ->
        {parsed, _} = Integer.parse(value)
        parsed

      value when is_integer(value) ->
        value

      _ ->
        default
    end
  end

  defp org_topic(socket), do: "orgs:#{socket.assigns.scope.current_tenant.id}"
end
