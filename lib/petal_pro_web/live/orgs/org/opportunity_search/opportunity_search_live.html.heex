<.org_layout
  current_page={:opportunity_search}
  current_user={@current_user}
  current_org={@current_org}
  current_membership={@current_membership}
  socket={@socket}
>
  <%= if @entitlement_remaining == 0 and @entitlement_total > 0 do %>
    <!-- Quota Exhausted Screen -->
    <QuotaExhaustedScreen.quota_exhausted_screen
      entitlement_used={@entitlement_used}
      entitlement_total={@entitlement_total}
      current_membership={@current_membership}
      current_org={@current_org}
      period_end_date={@period_end_date}
    />
  <% else %>
    <!-- Regular Search Interface -->
    <.container
      max_width="xl"
      class="h-[calc(100vh-6rem)] mt-4 flex flex-col sm:h-[calc(100vh-8rem)] sm:mt-6"
    >
      <.form for={@form} phx-submit="search_places">
        <div class="flex flex-col gap-3 lg:flex-row lg:items-start lg:gap-4">
          <!-- Search Controls -->
          <div class="flex flex-1 flex-col gap-2 sm:flex-row sm:items-center sm:gap-3 lg:gap-4">
            <div class="relative flex-1 lg:max-w-md">
              <.field
                field={@form[:location]}
                type="search"
                label={gettext("Location")}
                placeholder={gettext("Search for a city or area...")}
                wrapper_class="w-full"
                phx-change="search_location"
                phx-debounce="800"
              />
            </div>

            <.field
              field={@form[:market_radius]}
              type="select"
              label={gettext("Radius")}
              options={[
                {gettext("5 miles"), 5},
                {gettext("3 miles"), 3},
                {gettext("1 mile"), 1}
              ]}
              wrapper_class="w-full sm:w-auto lg:w-auto"
              phx-change="set_market_radius"
            />

            <div class="relative w-full sm:w-auto lg:w-auto">
              <.button
                label={gettext("Search")}
                loading={@search_result.loading}
                disabled={is_nil(@market)}
                class="w-full"
                title={if is_nil(@market), do: gettext("Place a search area on the map first")}
              />
            </div>
          </div>
          
<!-- Entitlement Progress Indicator - Desktop only -->
          <div class="hidden lg:ml-4 lg:block lg:w-full lg:max-w-sm">
            <div class="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:bg-gray-800/50 dark:border-gray-700">
              <div class="mb-1 flex items-center justify-between">
                <.p class="text-xs font-medium text-gray-600 dark:text-gray-400">
                  {gettext("Opportunity Quota")}
                </.p>
                <.p class="text-xs text-gray-600 dark:text-gray-400">
                  {"#{@entitlement_used} #{gettext("of")} #{@entitlement_total} #{gettext("assigned")}"}
                </.p>
              </div>
              <.progress
                size="xl"
                value={@entitlement_used}
                max={@entitlement_total}
                class="w-full"
              />
            </div>
          </div>
        </div>
      </.form>
      
<!-- Map and Places List - Takes remaining space -->
      <div class="mt-4 flex min-h-0 flex-1 flex-col sm:mt-5 lg:mt-6 lg:flex-row">
        <!-- Map Section -->
        <div class="min-h-[300px] relative flex-1 overflow-hidden rounded-lg lg:min-h-0 lg:rounded-r-none lg:rounded-l-lg">
          <.google_map
            id="opportunity-map"
            height="100%"
            width="100%"
            map_options={
              %{
                center: %{lat: 39.8283, lng: -98.5795},
                zoom: 4,
                gestureHandling: "greedy",
                mapTypeId: "hybrid"
              }
            }
            map_controls={
              %{
                mapTypeControl: true,
                mapTypeControlOptions: %{
                  style: "DROPDOWN_MENU"
                },
                streetViewControl: true,
                zoomControl: true
              }
            }
            libraries={
              %{
                maps: %{
                  eventSubscriptions: ["click", "zoom_changed"]
                },
                drawing: %{
                  drawingControl: false,
                  eventSubscriptions: ["overlaycomplete", "dragend"]
                },
                marker: %{
                  eventSubscriptions: ["gmp-click"]
                }
              }
            }
          />
          
<!-- Instructional Overlay - Shows instructions based on state -->
          <!-- Show when no location is entered -->
          <%= if !@market && !@location_center do %>
            <div class="pointer-events-none absolute inset-x-0 top-4 flex justify-center px-4">
              <div class="bg-gray-900/90 max-w-sm rounded-lg px-6 py-4 text-center text-white shadow-xl">
                <.icon name="hero-magnifying-glass" class="mx-auto mb-2 h-8 w-8" />
                <p class="mb-1 text-lg font-semibold">{gettext("Search for a location")}</p>
                <p class="text-sm opacity-90">
                  {gettext("Enter a city or area above to get started")}
                </p>
              </div>
            </div>
          <% end %>
          
<!-- Show when location is entered and circle is placed, but search hasn't been performed -->
          <%= if @market && @location_center && !@search_result.loading && !@search_result.result do %>
            <div class="pointer-events-none absolute inset-x-0 bottom-4 flex justify-center px-4">
              <div class="bg-gray-900/90 max-w-md rounded-lg px-4 py-2 text-center shadow-xl">
                <%= if @circle_draggable do %>
                  <.p class="text-sm text-white">
                    {gettext("Drag the circle or update the radius")}
                  </.p>
                  <.p class="text-sm text-white">{gettext("Click Search when ready")}</.p>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
        
<!-- Mobile Quota - Between Map and List -->
        <%= if @entitlement_total > 0 do %>
          <div class="mt-3 px-1 py-3 lg:hidden">
            <div class="flex items-baseline gap-3">
              <.p class="whitespace-nowrap text-xs font-medium text-gray-600 dark:text-gray-400">
                {gettext("Opportunity Quota")}
              </.p>
              <div class="flex-1">
                <.progress
                  size="sm"
                  value={@entitlement_used}
                  max={@entitlement_total}
                  class="w-full"
                />
              </div>
              <.p class="whitespace-nowrap text-xs text-gray-600 dark:text-gray-400">
                {"#{@entitlement_used} #{gettext("of")} #{@entitlement_total}"}
              </.p>
            </div>
          </div>
        <% end %>
        
<!-- Places List Panel -->
        <.google_map_place_list
          id="places-list-container"
          places_stream={@streams.places}
          selected_place={@selected_place}
          loading={@search_result.loading}
          assign_place_loading_id={@assign_place_loading_id}
          class="w-full rounded-lg lg:mt-0 lg:w-auto lg:rounded-l-none"
        />
      </div>
    </.container>
  <% end %>
</.org_layout>
