defmodule PetalProWeb.OrgsLive do
  @moduledoc """
  List all orgs for the current_user.
  """
  use PetalProWeb, :live_view

  alias PetalPro.Orgs

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign_invitations(socket)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, gettext("New organization"))
    |> assign(:org, %Boring.Orgs.Org{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, gettext("Organizations"))
    |> assign(:org, nil)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <.layout current_page={:orgs} current_user={@current_user} type="stacked">
      <.container class="py-16">
        <.h2 class="mb-8">{gettext("My Organizations")}</.h2>

        <%= if @current_user.confirmed_at do %>
          <%= if @invitations != [] do %>
            <.alert with_icon class="max-w-md" color="warning" heading={gettext("New invitations")}>
              <div class="mb-2">
                {gettext("You have pending invitations.")}
              </div>

              <.button link_type="live_redirect" color="success" to={~p"/app/users/org-invitations"}>
                {gettext("View invitations")}
              </.button>
            </.alert>
          <% end %>

          <%= if Enum.all?([@orgs, @invitations], &Enum.empty?/1) do %>
            <.alert
              with_icon
              class="mb-5 max-w-sm"
              heading={gettext("You don't belong to any organizations")}
            >
              {gettext("Create your first organization by clicking the button below.")}
            </.alert>
          <% end %>

          <div class="grid grid-cols-1 gap-8 md:grid-cols-2 xl:grid-cols-4">
            <%= if @orgs != [] do %>
              <%= for org <- @orgs do %>
                <.link
                  navigate={~p"/app/org/#{org.slug}"}
                  class="relative block w-full rounded-lg border border-gray-200 bg-gray-100 p-12 text-center text-gray-700 shadow-sm hover:bg-gray-200 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:shadow-lg dark:hover:bg-gray-700 dark:group-hover:text-gray-100"
                >
                  <.icon name="hero-building-office" class="mx-auto h-12 w-12" />

                  <span class="mt-2 block text-sm font-medium">
                    {org.name}
                  </span>
                </.link>
              <% end %>
            <% end %>

            <.link
              navigate={~p"/app/orgs/new"}
              class="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center text-gray-500 hover:border-gray-400 hover:text-gray-900 dark:border-gray-700 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:text-gray-300"
            >
              <.icon name="hero-plus" class="mx-auto h-12 w-12" />

              <span class="mt-2 block text-sm font-medium">
                {gettext("Create a new organization")}
              </span>
            </.link>
          </div>
        <% else %>
          <.alert color="warning" class="my-5" heading={gettext("Unconfirmed account")}>
            <%= if Util.present?(@invitations) do %>
              <div class="mb-2">
                {gettext(
                  "You have been invited to join %{org_names}. To create an organization or accept any invitations, please confirm your account first.",
                  org_names: Enum.join(Enum.map(@invitations, & &1.org.name), ", ")
                )}
              </div>
            <% else %>
              {gettext("Please confirm your account to create an organization.")}
            <% end %>
          </.alert>
        <% end %>
      </.container>

      <%= if @live_action == :new do %>
        <.modal max_width="lg" title={@page_title}>
          <.live_component
            module={PetalProWeb.OrgFormComponent}
            id={:new}
            action={@live_action}
            org={@org}
            return_to={~p"/app/orgs"}
            current_user={@current_user}
          />
        </.modal>
      <% end %>
    </.layout>
    """
  end

  @impl true
  def handle_event("close_modal", _, socket) do
    {:noreply, push_patch(socket, to: ~p"/app/orgs")}
  end

  defp assign_invitations(socket) do
    invitations = Orgs.list_invitations_by_user(socket.assigns.current_user)

    assign(socket, :invitations, invitations)
  end
end
