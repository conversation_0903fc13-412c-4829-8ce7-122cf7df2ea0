defmodule PetalProWeb.JoinWaitlistLive do
  @moduledoc false
  use PetalProWeb, :live_view

  alias AshPhoenix.Form

  @impl true
  def mount(_params, _session, socket) do
    form =
      Boring.Accounts.form_to_create_waitlist_request(authorize?: false)

    {:ok, assign(socket, form: to_form(form), interest_reason: nil)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <.modal title="Join our waitlist" max_width="lg">
      <.form for={@form} phx-submit="submit">
        <.field required field={@form[:name]} {alpine_autofocus()} />
        <.field required field={@form[:email]} type="email" />
        <.field
          required
          id="interest_reason"
          name="interest_reason"
          value={@interest_reason}
          label="What made you interested in taking part of our beta test?"
          type="radio-group"
          options={
            Enum.map(
              [
                "Actively looking for self storage facility lead sources",
                "Looked like something I would be interested in",
                "Other"
              ],
              &{&1, &1}
            )
          }
          group_layout="col"
          phx-change="set_interest_reason"
        />
        <%= if @interest_reason == "Other" do %>
          <.field label="Reason" required field={@form[:interest_reason]} />
        <% end %>
        <.field
          required
          field={@form[:testimonial?]}
          type="radio-group"
          options={[{"Yes", "true"}, {"No", "false"}]}
          label="Would you be okay with providing a public testimonial after testing?"
        />
        <.field
          required
          field={@form[:lead_contacts_per_week]}
          label="How many leads are you contacting per week?"
          type="radio-card"
          size="sm"
          variant="outline"
          group_layout="row"
          options={
            Enum.map(Boring.Accounts.Types.LeadContactsPerWeek.values(), &%{label: &1, value: &1})
          }
        />
        <.field
          required
          field={@form[:report_feedback?]}
          type="radio-group"
          options={[{"Yes", "true"}, {"No", "false"}]}
          label="Are you willing to give feedback and report any <NAME_EMAIL>?"
        />

        <.field
          required
          field={@form[:preferred_regions]}
          type="checkbox-group"
          group_layout="col"
          options={
            Enum.map(
              Boring.Accounts.Types.PreferredRegion.values(),
              &{Phoenix.Naming.humanize(&1), &1}
            )
          }
          label="What area of the country are you currently looking for self storage facilities?"
        />

        <div class="pt-4">
          <.button class="w-full" phx-disable-with={gettext("Submitting...")}>
            {gettext("Submit")}
          </.button>
        </div>
      </.form>
    </.modal>
    """
  end

  @impl true
  def handle_event("close_modal", _, socket) do
    {:noreply, redirect(socket, to: ~p"/")}
  end

  @impl true
  def handle_event("set_interest_reason", %{"interest_reason" => interest_reason}, socket) do
    {:noreply, assign(socket, interest_reason: interest_reason)}
  end

  @impl true
  def handle_event("submit", %{"form" => form_data}, socket) do
    form_data =
      if socket.assigns.interest_reason == "Other" do
        form_data
      else
        Map.put(form_data, "interest_reason", socket.assigns.interest_reason)
      end

    case Form.submit(socket.assigns.form, params: form_data) do
      {:ok, _} ->
        socket =
          socket
          |> put_flash(
            :success,
            gettext("Waitlist request submitted!")
          )
          |> redirect(to: ~p"/")

        {:noreply, socket}

      {:error, form} ->
        socket =
          socket
          |> put_flash(:error, gettext("Unable to submit your request"))
          |> assign(:form, form)

        {:noreply, socket}
    end
  end
end
