<%= if @live_action == :sign_in do %>
  <.auth_layout title={gettext("Continue with passwordless")}>
    <:logo>
      <.logo_icon class="h-20 w-20" />
    </:logo>
    <.form for={@form} phx-submit="submit_email">
      <.field
        type="email"
        field={@form[:email]}
        placeholder={gettext("eg. <EMAIL>")}
        {alpine_autofocus()}
      />

      <.p class="text-sm">
        {gettext(
          "Enter the email with to register or sign in with and we'll email you a pin code."
        )}
      </.p>

      <.alert color="warning" label={@error_message} class="mt-5" />

      <div class="mt-6 flex justify-between">
        <.button to={~p"/auth/sign-in"} link_type="live_redirect" type="button" color="white">
          <.icon name="hero-arrow-small-left" class="mr-1 h-4 w-4" />
          {gettext("Cancel")}
        </.button>
        <.button phx-disable-with={gettext("Sending...")} label={gettext("Get pin code")} />
      </div>
    </.form>
  </.auth_layout>
<% end %>

<%= if @live_action == :sign_in_code do %>
  <.auth_layout title={gettext("Check your email")}>
    <:logo>
      <.logo_icon class="h-20 w-20" />
    </:logo>
    <:top_links>
      <.p>{gettext("We've sent a 6 digit sign in pin code to")}:</.p>
      <.p class="font-semibold">{@auth_user.email}</.p>
      <.p>{gettext("Can't find it? Check your spam folder.")}</.p>
    </:top_links>
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <%= if @loading do %>
        <div class="h-[140px] flex items-center justify-center gap-3">
          <.spinner show={true} class="text-primary-600 dark:text-primary-400" size="md" />
          <.h5 no_margin>{gettext("Signing in...")}</.h5>
        </div>
      <% end %>

      <.form
        for={@token_form}
        action={~p"/auth/sign-in/passwordless"}
        phx-trigger-action={@trigger_submit}
        phx-change="validate_pin"
        class={if @loading, do: "hidden", else: ""}
      >
        <.form_label>{gettext("Your sign in pin code")}</.form_label>

        <input
          type="number"
          name={@token_form[:pin].name}
          value={@token_form[:pin].value}
          class="font-mono shadow-xs block w-full rounded-md border-gray-300 text-center focus:border-primary-500 focus:ring-primary-500 focus:outline-hidden dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:focus:border-primary-500 md:text-2xl"
          min="0"
          max="10000000"
          inputmode="numeric"
          pattern="[0-9]*"
          onkeypress="{if(this.value.length==6) return false;}"
          autofill="off"
          autocomplete="off"
          {alpine_autofocus()}
        />

        <.input type="hidden" field={@token_form[:sign_in_token]} />
        <.input type="hidden" field={@token_form[:user_return_to]} />

        <.alert color="warning" class="mt-5" label={@error_message} />

        <div class="mt-6 flex justify-between">
          <.button
            to={~p"/auth/sign-in/passwordless"}
            link_type="live_patch"
            type="button"
            color="white"
          >
            <.icon name="hero-arrow-small-left-solid" class="mr-1 h-4 w-4" />
            {gettext("Cancel")}
          </.button>

          <%= if @enable_resend? do %>
            <.button
              color="white"
              type="button"
              phx-disable-with={gettext("Resending new pin code...")}
              phx-click="resend"
            >
              <.icon name="hero-arrow-path-solid" class="mr-1 h-4 w-4" />
              {gettext("Resend pin code")}
            </.button>
          <% end %>
        </div>
      </.form>
    </div>
  </.auth_layout>
<% end %>
