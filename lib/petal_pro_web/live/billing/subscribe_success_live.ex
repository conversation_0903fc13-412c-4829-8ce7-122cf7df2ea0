defmodule PetalProWeb.SubscribeSuccessLive do
  @moduledoc false
  use PetalProWeb, :live_view

  import PetalProWeb.OrgLayoutComponent

  alias PetalPro.Billing.Subscriptions

  @impl true
  def mount(%{"customer_id" => customer_id}, _session, socket) do
    socket =
      socket
      |> assign(:page_title, gettext("Subscribed"))
      |> assign(:subscription_status, :loading)
      |> push_event("confetti", %{})
      |> assign(:subscription, nil)
      |> assign(:customer_id, customer_id)
      |> assign(:source, socket.assigns.live_action)
      |> assign(:pings, 0)
      |> assign_subscription()

    {:ok, socket}
  end

  @impl true
  def handle_info(:check_subscription, socket) do
    {:noreply, assign_subscription(socket)}
  end

  defp assign_subscription(%{assigns: %{pings: 5}} = socket) do
    assign(socket, :subscription_status, :failed)
  end

  defp assign_subscription(socket) do
    subscription =
      Subscriptions.get_subscription_by(%{
        status: "active",
        billing_customer_id: socket.assigns.customer_id
      }) ||
        Subscriptions.get_subscription_by(%{
          status: "trialing",
          billing_customer_id: socket.assigns.customer_id
        })

    case subscription do
      nil ->
        schedule_membership_check()
        assign(socket, :pings, socket.assigns.pings + 1)

      subscription ->
        socket
        |> assign(:subscription, subscription)
        |> assign(:subscription_status, :success)
    end
  end

  defp schedule_membership_check do
    Process.send_after(self(), :check_subscription, 1500)
  end

  defp opportunity_search_path(assigns) do
    case assigns[:current_org] do
      %{slug: slug} -> ~p"/app/org/#{slug}/search"
      _ -> PetalProWeb.Helpers.home_path(assigns[:current_user])
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <%= case @source do %>
      <% :user -> %>
        <.layout current_page={:subscribe} current_user={@current_user}>
          <.subscription_status
            subscription_status={@subscription_status}
            subscription={@subscription}
            current_user={@current_user}
          />
        </.layout>
      <% :org -> %>
        <.org_layout
          current_page={:org_subscribe}
          current_user={@current_user}
          current_org={@current_org}
          current_membership={@current_membership}
          socket={@socket}
        >
          <.subscription_status
            current_org={@current_org}
            subscription_status={@subscription_status}
            subscription={@subscription}
            current_user={@current_user}
          />
        </.org_layout>
    <% end %>
    """
  end

  def subscription_status(assigns) do
    ~H"""
    <.container class="my-12" id="subscription-status">
      <div class="flex h-full w-full justify-center">
        <div class="flex w-full max-w-lg flex-col flex-wrap overflow-hidden rounded-lg bg-gray-50 dark:bg-gray-800 lg:max-w-3xl">
          <div class="flex justify-center rounded-lg border border-gray-200 p-6 shadow-lg dark:border-none dark:shadow-2xl">
            <div
              :if={@subscription_status == :success}
              phx-hook="ConfettiHook"
              id="ConfettiHook"
              class="max-w-lg flex-1 pt-10 text-center"
            >
              <div class="text-7xl">✨</div>

              <p class="mt-10 mb-5 text-center text-3xl font-medium text-gray-900 dark:text-gray-100">
                {gettext("Welcome to GoFish!")}
              </p>
              <p class="text-base font-normal text-gray-500 dark:text-gray-400">
                {gettext("You are ready to start fishing for off-market deals")}
              </p>

              <.button
                to={opportunity_search_path(assigns)}
                label="Start Fishing!"
                link_type="live_patch"
                class="my-8"
              />
            </div>
            <div :if={@subscription_status == :failed} class="max-w-lg flex-1 pt-10 text-center">
              <div class="text-7xl">❌</div>
              <p class="mt-10 mb-5 text-center text-3xl font-medium text-gray-900 dark:text-gray-100">
                {gettext("Subscription failed")}
              </p>
              <p class="text-base font-normal text-gray-500 dark:text-gray-400">
                {gettext("Something has gone wrong. Please contact support on")}
                <a
                  href={"mailto:#{PetalPro.config(:support_email)}?subject=Subscription failed"}
                  class="text-blue-500"
                >
                  {PetalPro.config(:support_email)}
                </a>
              </p>
              <.button
                to={PetalProWeb.Helpers.home_path(@current_user)}
                label="Go back"
                link_type="live_patch"
                class="my-8"
              />
            </div>
            <div
              :if={@subscription_status == :loading}
              class="flex max-w-lg items-center gap-2 text-center"
            >
              <.spinner show={@subscription_status == :loading} size="sm" />
              {gettext("Please wait...")}
            </div>
          </div>
        </div>
      </div>
    </.container>
    """
  end
end
