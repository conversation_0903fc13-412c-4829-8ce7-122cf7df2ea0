<.layout current_page={:user_ai_chat} current_user={@current_user} type="sidebar">
  <.container max_width="xl" class="my-10">
    <.page_header title={gettext("User AI Chat")} />
    <div class="flex h-full flex-auto flex-col">
      <div class="mb-4 flex flex-auto shrink-0 flex-col rounded-2xl bg-gray-100 p-4 dark:bg-gray-800 sm:mb-6 md:mb-8 lg:mb-12">
        <div class="mb-4 flex h-full flex-col overflow-x-auto">
          <div class="flex h-full flex-col">
            <div class="grid grid-cols-12 gap-y-2">
              <%= for message <- @messages do %>
                <%= if message.role == :assistant do %>
                  <div class="col-start-1 col-end-13 rounded-lg p-3">
                    <div class="flex flex-row items-center">
                      <.avatar name="IA" size="md" random_color class="shrink-0" />
                      <div class="relative ml-3 rounded-xl bg-white px-4 py-2 text-sm shadow-sm dark:bg-gray-700">
                        <.unsafe_markdown
                          content={message.content}
                          class="text-black dark:text-white"
                        />
                      </div>
                    </div>
                  </div>
                <% else %>
                  <div class="col-start-1 col-end-13 rounded-lg p-3">
                    <div class="flex flex-row-reverse items-center justify-start">
                      <.avatar name={@current_user.name} size="md" random_color class="shrink-0" />
                      <div class="relative mr-3 rounded-xl bg-indigo-100 px-4 py-2 text-sm shadow-sm">
                        <.pretty_markdown content={message.content} class="text-black" />
                      </div>
                    </div>
                  </div>
                <% end %>
              <% end %>
              <div :if={@loading} class="col-start-1 col-end-13 rounded-lg p-3">
                <div class="flex flex-row items-center">
                  <.avatar name="IA" size="md" random_color class="shrink-0" />
                  <div class="relative ml-3 flex rounded-xl bg-white px-4 py-2 text-sm shadow-sm dark:bg-gray-700">
                    <%= if @response in [nil, ""] do %>
                      <.pretty_markdown
                        content="."
                        class="[animation-delay:-0.3s] animate-bounce text-black"
                      />
                      <.pretty_markdown
                        content="."
                        class="[animation-delay:-0.15s] animate-bounce text-black"
                      />
                      <.pretty_markdown content="." class="animate-bounce text-black" />
                    <% end %>
                    <.pretty_markdown content={@response} class="text-black dark:text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <.form
          for={@form}
          class="flex h-auto w-full grow flex-row items-center px-4"
          phx-change="validate"
          phx-submit="submit"
        >
          <div class="mr-4">
            <.button
              type="button"
              id="microphone"
              phx-hook="MicrophoneHook"
              class="group rounded-full bg-white p-2 text-sm text-gray-600 shadow-sm ring-1 ring-gray-300 hover:bg-gray-100 focus:bg-white active:animate-pulse active:bg-red-400 active:ring-4 active:ring-blue-300 dark:bg-gray-700 dark:text-white dark:ring-gray-500 dark:hover:bg-gray-600 dark:focus:bg-gray-700 dark:active:bg-red-500"
            >
              <.icon name="hero-microphone-solid" class="h-4 w-4" />
            </.button>
          </div>
          <div class="grow" id="chat-box" phx-hook="FocusBySelectorHook">
            <div class="relative w-full">
              <.live_file_input upload={@uploads.audio} class="hidden" />
              <.input
                id="chat-message"
                field={@form[:content]}
                type="textarea"
                disabled={@loading}
                rows="1"
                phx-hook="ResizeTextareaHook"
                class="min-h-10 flex w-full rounded-xl border pl-4 focus:border-indigo-300 focus:outline-hidden"
                {alpine_autofocus()}
              />
            </div>
          </div>
          <div class="ml-4">
            <.button
              type="submit"
              id="submit-button"
              class={"#{submit_button_class(@form.source.valid?)} group rounded-full p-2 text-sm text-gray-600 shadow-sm ring-1 ring-gray-300 hover:bg-gray-100 focus:bg-white dark:text-white dark:ring-gray-300 dark:hover:bg-gray-400"}
              disabled={@loading == true or @form.source.valid? == false}
            >
              <.icon
                id="icon"
                name="hero-paper-airplane-solid"
                class={["h-4 w-4", submit_icon_class(@form.source.valid?)]}
              />
            </.button>
          </div>
        </.form>
      </div>
    </div>
  </.container>
</.layout>
