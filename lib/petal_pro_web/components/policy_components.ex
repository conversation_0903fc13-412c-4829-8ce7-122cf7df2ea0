defmodule PetalProWeb.PolicyComponents do
  @moduledoc """
  A set of components for use in a landing page.
  """
  use Phoenix.Component
  use PetalComponents

  attr :policy_id, :string, required: true

  def policy(assigns) do
    ~H"""
    <div name="termly-embed" data-id={@policy_id}></div>
    <script defer type="text/javascript">
      (function(d, s, id) {
          var js, tjs = d.getElementsByTagName(s)[0];
          if (d.getElementById(id)) return;
          js = d.createElement(s); js.id = id;
          js.src = "https://app.termly.io/embed-policy.min.js";
          tjs.parentNode.insertBefore(js, tjs);
        }(document, 'script', 'termly-jssdk'));
    </script>
    """
  end
end
