defmodule PetalProWeb.LanguageSelect do
  @moduledoc false
  use Phoenix.Component

  import PetalComponents.Dropdown
  import PetalComponents.Icon

  @doc """
  Usage:
  <.language_select
    current_locale={Gettext.get_locale(YourAppWeb.Gettext)}
    language_options={YourApp.config(:language_options)}
  />
  """

  attr :current_path, :string
  attr :current_locale, :string
  attr :language_options, :list, doc: "list of maps with keys :locale, :flag (emoji), :label"

  def language_select(assigns) do
    assigns = assign_new(assigns, :current_path, fn -> "" end)

    ~H"""
    <.dropdown>
      <:trigger_element>
        <div class="inline-flex w-full items-center justify-center gap-1 align-middle focus:outline-hidden">
          <div class="text-2xl">
            {Enum.find(@language_options, &(&1.locale == @current_locale)).flag}
          </div>
          <.icon name="hero-chevron-down-mini" class="h-4 w-4 text-gray-400 dark:text-gray-100" />
        </div>
      </:trigger_element>
      <%= for language <- @language_options do %>
        <.dropdown_menu_item link_type="a" to={@current_path <> "?locale=#{language.locale}"}>
          <div class="mr-2 text-2xl leading-none">{language.flag}</div>
          <div>{language.label}</div>
        </.dropdown_menu_item>
      <% end %>
    </.dropdown>
    """
  end
end
