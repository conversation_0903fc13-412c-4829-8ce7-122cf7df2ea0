defmodule PetalProWeb.PageComponents do
  @moduledoc false
  use Phoenix.Component
  use PetalComponents

  @doc """
  Allows you to have a heading on the left side, and some action buttons on the right (default slot)
  """

  attr :icon, :string, default: nil
  attr :class, :string, default: ""
  attr :title, :string, required: true
  slot(:inner_block)

  def page_header(assigns) do
    assigns = assign_new(assigns, :inner_block, fn -> nil end)

    ~H"""
    <div class={["mb-8 sm:flex sm:items-center sm:justify-between", @class]}>
      <div class="mb-4 flex items-center gap-2 sm:mb-0">
        <.icon :if={@icon} name={@icon} class="h-10 w-10" />
        <.h2 class="!mb-0">
          {@title}
        </.h2>
      </div>

      <div class="flex items-center gap-2">
        <%= if @inner_block do %>
          {render_slot(@inner_block)}
        <% end %>
      </div>
    </div>
    """
  end

  @doc "Gives you a white background with shadow."
  attr :class, :string, default: ""
  attr :padded, :boolean, default: false
  attr :rest, :global
  slot(:inner_block)

  def box(assigns) do
    ~H"""
    <div
      {@rest}
      class={[
        "overflow-hidden rounded-lg bg-white shadow-sm dark:border dark:border-gray-700 dark:bg-gray-800",
        @class,
        if(@padded, do: "px-4 py-8 sm:px-10", else: "")
      ]}
    >
      {render_slot(@inner_block)}
    </div>
    """
  end

  @doc """
  Provides a container with a sidebar on the left and main content on the right. Useful for things like user settings.

  ---------------------------------
  | Sidebar | Main                |
  |         |                     |
  |         |                     |
  |         |                     |
  ---------------------------------
  """

  attr :current_page, :atom

  attr :menu_items, :list,
    required: true,
    doc: "list of maps with keys :name, :path, :label, :icon (heroicon class)"

  slot(:inner_block)

  def sidebar_tabs_container(assigns) do
    ~H"""
    <.box class="flex flex-col divide-y divide-gray-200 border border-gray-200 dark:divide-gray-700 dark:border-none md:flex-row md:divide-x md:divide-y-0">
      <div class="w-full shrink-0 py-6 md:w-72">
        <%= for menu_item <- @menu_items do %>
          <.sidebar_menu_item current={@current_page} {menu_item} />
        <% end %>
      </div>

      <div class="grow px-4 py-6 sm:p-6 lg:pb-8">
        {render_slot(@inner_block)}
      </div>
    </.box>
    """
  end

  attr :current, :atom
  attr :name, :string
  attr :path, :string
  attr :label, :string
  attr :icon, :string

  def sidebar_menu_item(assigns) do
    assigns = assign(assigns, :is_active?, assigns.current == assigns.name)

    ~H"""
    <.link
      navigate={@path}
      class={[
        menu_item_classes(@is_active?),
        "group flex items-center border-transparent px-3 py-2 text-sm font-medium"
      ]}
    >
      <.icon name={@icon} class={menu_item_icon_classes(@is_active?) <> " mx-3 h-6 w-6 shrink-0"} />
      <div>
        {@label}
      </div>
    </.link>
    """
  end

  defp menu_item_classes(true),
    do: "bg-gray-100 border-gray-500 text-gray-700 dark:bg-gray-700 dark:text-gray-100 dark:hover:text-white"

  defp menu_item_classes(false),
    do:
      "text-gray-900 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700/70 dark:hover:text-gray-50"

  defp menu_item_icon_classes(true),
    do: "text-gray-500 group-hover:text-gray-500 dark:text-gray-100 dark:group-hover:text-white"

  defp menu_item_icon_classes(false),
    do: "text-gray-500 group-hover:text-gray-500 dark:text-gray-400 dark:group-hover:text-gray-400"
end
