defmodule PetalProWeb.BillingComponents do
  @moduledoc false
  use Phoenix.Component
  use PetalProWeb, :verified_routes
  use PetalComponents
  use Gettext, backend: PetalProWeb.Gettext

  @doc """
  Builds the features list for a given tier using incremental display.
  Shows base features for first tier, additional features for higher tiers.
  """
  def build_features_for_tier(product_id) do
    billing_products = Application.get_env(:boring, :billing_products, [])
    tier_order = Enum.map(billing_products, & &1.id)

    case Enum.find_index(tier_order, &(&1 == product_id)) do
      0 ->
        build_base_features(product_id)

      index when index > 0 ->
        previous_tier = Enum.at(tier_order, index - 1)
        build_incremental_features(previous_tier, product_id)

      _ ->
        []
    end
  end

  defp build_base_features(product_id) do
    billing_features = Application.get_env(:boring, :billing_features, [])

    billing_features
    |> Enum.filter(&(product_id in &1.tiers))
    |> Enum.flat_map(&build_feature_benefits(&1, product_id))
  end

  defp build_incremental_features(base_tier, current_tier) do
    billing_features = Application.get_env(:boring, :billing_features, [])

    # Get new features for this tier
    new_features =
      billing_features
      |> Enum.filter(fn feature ->
        current_tier in feature.tiers and base_tier not in feature.tiers
      end)
      |> Enum.flat_map(&build_feature_benefits(&1, current_tier))

    # Get upgraded features (like opportunity count increases)
    upgraded_features =
      billing_features
      |> Enum.filter(&(current_tier in &1.tiers and base_tier in &1.tiers))
      |> Enum.flat_map(&build_upgraded_benefits(&1, base_tier, current_tier))

    upgraded_features ++ new_features
  end

  defp build_feature_benefits(feature, product_id) do
    feature.benefits
    |> Enum.filter(&(product_id in &1.tiers))
    |> Enum.map(&build_benefit_for_tier(&1, product_id))
  end

  defp build_upgraded_benefits(feature, base_tier, current_tier) do
    feature.benefits
    |> Enum.filter(&(current_tier in &1.tiers and base_tier in &1.tiers))
    |> Enum.map(&build_upgrade_benefit(&1, base_tier, current_tier))
    |> Enum.filter(&(!is_nil(&1)))
  end

  defp build_benefit_for_tier(benefit, product_id) do
    text = get_benefit_text(benefit, product_id)

    %{
      name: text,
      description: Map.get(benefit, :description)
    }
  end

  defp build_upgrade_benefit(benefit, base_tier, current_tier) do
    # Only show upgrade for opportunity counts
    if benefit.id == "opportunities_per_month" do
      base_count = get_tier_opportunity_count(benefit, base_tier)
      current_count = get_tier_opportunity_count(benefit, current_tier)

      if base_count && current_count && base_count != current_count do
        %{
          name: "opportunities per month",
          description: Map.get(benefit, :description, "Enhanced opportunity discovery"),
          upgrade: %{previous: base_count, new: current_count}
        }
      end
    end
  end

  defp get_tier_opportunity_count(benefit, product_id) do
    if benefit.tier_values && benefit.tier_values[product_id] do
      to_string(benefit.tier_values[product_id][:count])
    end
  end

  defp get_benefit_text(benefit, product_id) do
    cond do
      Map.has_key?(benefit, :text_template) && Map.has_key?(benefit, :tier_values) ->
        template = benefit.text_template
        values = benefit.tier_values[product_id] || %{}
        interpolate_template(template, values)

      Map.has_key?(benefit, :text) ->
        benefit.text

      true ->
        "Unknown benefit"
    end
  end

  defp interpolate_template(template, values) do
    Enum.reduce(values, template, fn {key, value}, acc ->
      String.replace(acc, "{#{key}}", to_string(value))
    end)
  end

  @doc """
  Gets all billing products with their features built dynamically.
  """
  def get_billing_products_with_features do
    billing_products = Application.get_env(:boring, :billing_products, [])

    Enum.map(billing_products, fn product ->
      features = build_features_for_tier(product.id)
      Map.put(product, :features, features)
    end)
  end

  attr :panels, :integer, default: 3
  attr :interval_selector, :boolean, default: false
  attr :rest, :global
  slot :default

  def pricing_panels_container(assigns) do
    ~H"""
    <div x-data="{ interval: 'month' }">
      <div :if={@interval_selector} class="flex justify-center">
        <div class="grid grid-cols-2 gap-x-1 rounded-full bg-gray-100 p-1 text-center text-xs font-semibold leading-5 text-black dark:bg-white/20 dark:text-white">
          <label
            class="cursor-pointer rounded-full px-4 py-2"
            @click="interval = 'month'"
            x-bind:class="{ 'bg-primary-500 text-white': interval == 'month' }"
          >
            <input type="radio" name="frequency" value="monthly" class="sr-only" />
            <span>Monthly</span>
          </label>
          <label
            class="cursor-pointer rounded-full px-4 py-2"
            @click="interval = 'year'"
            x-bind:class="{ 'bg-primary-500 text-white': interval == 'year' }"
          >
            <input type="radio" name="frequency" value="annually" class="sr-only" />
            <span>Annually</span>
          </label>
        </div>
      </div>

      <div
        {@rest}
        class={[
          "isolate mx-auto mt-10 grid max-w-md grid-cols-1 gap-8 lg:mx-0 lg:max-w-none",
          pricing_panels_container_css(@panels)
        ]}
      >
        {render_slot(@inner_block)}
      </div>
    </div>
    """
  end

  attr :class, :string, default: nil, doc: "Outer div class"
  attr :label, :string
  attr :description, :string
  attr :feature_blurb, :string, default: "Includes:"
  attr :features, :list, default: []
  attr :product_id, :string, default: nil, doc: "Product ID to build features dynamically"
  attr :most_popular, :boolean, default: false
  attr :rest, :global
  attr :is_current_plan, :boolean, default: false

  slot :default

  def pricing_panel(assigns) do
    # Build features dynamically if product_id is provided and features are empty
    assigns =
      if assigns.product_id && Enum.empty?(assigns.features) do
        features = build_features_for_tier(assigns.product_id)
        Map.put(assigns, :features, features)
      else
        assigns
      end

    ~H"""
    <div class={"#{if @most_popular || @is_current_plan, do: ring_style(true), else: ring_style(false)} #{@class} rounded-3xl p-8 shadow-xl xl:p-10"}>
      <div style="background: linear-gradient(270deg, rgba(153, 238, 255, 0) 0%, rgb(255, 255, 255) 49.5495%, rgba(255, 255, 255, 0) 100%); flex: 0 0 auto; height: 1px; left: calc(14.8579%); opacity: 0.5; overflow: hidden; position: absolute; top: 0px; user-select: none; width: 70%; z-index: 1; margin-top: -1px;">
      </div>
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold leading-8 text-black dark:text-white">
          {@label}
        </h3>
        <p
          :if={@most_popular}
          class="bg-primary-600/10 text-primary-600 rounded-full px-2.5 py-1 text-xs font-semibold leading-5 dark:bg-primary-400/10 dark:text-primary-400"
        >
          {gettext("Most popular")}
        </p>
        <p
          :if={@is_current_plan}
          class="bg-green-600/10 rounded-full px-2.5 py-1 text-xs font-semibold leading-5 text-green-600 dark:bg-green-400/10 dark:text-green-400"
        >
          {gettext("Current plan")}
        </p>
      </div>

      {render_slot(@inner_block)}
      <p class="mt-6 text-sm font-medium text-gray-900 dark:text-gray-100">
        {@feature_blurb}
      </p>
      <ul class="mt-4 space-y-4 text-sm leading-6 xl:mt-6">
        <%= for feature <- @features do %>
          <li class="flex gap-x-3">
            <svg
              class="mt-0.5 h-5 w-5 flex-none text-green-600 dark:text-green-400"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fill-rule="evenodd"
                d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                clip-rule="evenodd"
              />
            </svg>
            <div class="flex flex-col">
              <div class="flex items-center gap-2">
                <%= if is_map(feature) && Map.get(feature, :upgrade) do %>
                  <span class="font-medium text-gray-400 line-through dark:text-gray-500">
                    {feature.upgrade.previous}
                  </span>
                  <span class="font-medium text-gray-900 dark:text-gray-100">
                    {feature.upgrade.new} {feature.name}
                  </span>
                <% else %>
                  <span class="font-medium text-gray-900 dark:text-gray-100">
                    {if is_map(feature), do: feature.name, else: feature}
                  </span>
                <% end %>
              </div>
              <%= if is_map(feature) && Map.get(feature, :description) do %>
                <span class="mt-1 text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  {feature.description}
                </span>
              <% end %>
            </div>
          </li>
        <% end %>
      </ul>
    </div>
    """
  end

  defp ring_style(true) do
    "ring-2 bg-gray-400/5 dark:shadow-none ring-primary-400 dark:ring-primary-400 shadow-gray-400/5"
  end

  defp ring_style(false) do
    "ring-1 bg-gray-400/5 dark:shadow-none ring-gray-950/5 dark:ring-gray-200/20 shadow-gray-400/5"
  end

  attr :id, :string
  attr :key, :string, required: false
  attr :interval, :atom
  attr :amount, :integer
  attr :button_label, :string, default: "Subscribe"
  attr :button_props, :map, default: %{}
  attr :is_public, :boolean, default: false
  attr :is_current_plan, :boolean, default: false
  attr :billing_path, :string, default: "/app/billing"

  def item_price(assigns) do
    ~H"""
    <div id={@id} x-bind:class={"{ 'hidden': interval != '#{@interval}' }"}>
      <p class="mt-6 flex items-baseline gap-x-1">
        <span class="text-4xl font-bold tracking-tight text-black dark:text-white">
          <%= case @interval do %>
            <% :month -> %>
              {@amount |> Util.format_money()}
            <% :year -> %>
              {(@amount / 12) |> ceil() |> Util.format_money()}
          <% end %>
        </span>
        <span class="text-sm font-semibold leading-6 text-gray-700 dark:text-gray-300">
          /
          <%= case @interval do %>
            <% :month -> %>
              {gettext("month")}
            <% :year -> %>
              {gettext("month (paid yearly)")}
          <% end %>
        </span>
      </p>

      <%= if @is_public do %>
        <.button
          color="light"
          class="mt-6 w-full rounded-md border-none bg-gray-200 px-3 py-2 text-center text-sm font-semibold leading-6 text-black hover:bg-gray-300 dark:bg-white/20 dark:text-white dark:hover:bg-white/30"
          label={@button_label}
          link_type="live_redirect"
          to={~p"/auth/register?plan=#{@key}"}
        />
      <% else %>
        <%= if @is_current_plan do %>
          <.button
            to={@billing_path}
            link_type="live_redirect"
            label={@button_label}
            class="mt-6 w-full"
            {@button_props}
          />
        <% else %>
          <.button label={@button_label} class="mt-6 w-full" {@button_props} />
        <% end %>
      <% end %>
    </div>
    """
  end

  defp pricing_panels_container_css(1), do: ""
  defp pricing_panels_container_css(n), do: "lg:grid-cols-#{n}"
end
