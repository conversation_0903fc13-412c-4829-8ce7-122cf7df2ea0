defmodule PetalProWeb.Components.GoogleMap do
  @moduledoc """
  An improved Google Maps Phoenix.Component that provides full access to the Google Maps SDK
  and its libraries with flexible configuration options.

  ## Features
  - **Container Filling**: Automatically fills its container by default (height: 100%, width: 100%)
  - **Clean UI by Default**: No Google Maps controls shown by default for a minimal appearance
  - **Dynamic Library Loading**: Only loads the Google Maps libraries you specify
  - **Modular JavaScript**: Library-specific functionality is loaded on-demand to keep bundle size small
  - **Full SDK Access**: Complete access to Google Maps SDK options and features
  - **Event-Driven**: All map events sent directly to parent LiveView via hook
  - **Stateless Component**: No internal state management, pure function component
  - **Performance Optimized**: No re-render issues, hook manages all map state
  - **Configurable JSON**: Supports built-in JSON, Jason, Poison, and other JSON libraries

  ## Usage

  ### Basic Usage (Clean UI)
  By default, the component displays a clean map without any Google Maps UI controls and fills its container:

      <.google_map
        id="my-map"
        map_options={%{
          center: %{lat: 37.7749, lng: -122.4194},
          zoom: 12,
          mapTypeId: "roadmap"
        }}
      />

  ### Fixed Size Usage
  You can specify explicit dimensions when needed:

      <.google_map
        id="my-map"
        height="400px"
        width="600px"
        map_options={%{
          center: %{lat: 37.7749, lng: -122.4194},
          zoom: 12,
          mapTypeId: "roadmap"
        }}
      />

  ### With Controls Enabled
  Enable specific controls as needed:

      <.google_map
        id="my-map"
        api_key={@google_maps_api_key}
        loader_options={%{version: "weekly", language: "en"}}
        map_options={%{
          center: %{lat: 37.7749, lng: -122.4194},
          zoom: 12,
          mapTypeId: "roadmap"
        }}
        map_controls={%{
          zoomControl: true,
          mapTypeControl: true,
          fullscreenControl: true
        }}
        libraries={%{
          places: %{
            placeFields: "free",
            eventSubscriptions: ["place_changed", "places_changed"]
          },
          drawing: %{
            drawingControl: true,
            eventSubscriptions: ["overlaycomplete", "drawingmode_changed"]
          },
          maps: %{
            eventSubscriptions: ["click", "zoom_changed", "center_changed"]
          }
        }}
        json_library={Jason}
      />

  ### With Markers
  Display markers on the map:

      <.live_component
        module={PetalProWeb.Components.GoogleMap}
        id="my-map"
        map_options={%{
          center: %{lat: 37.7749, lng: -122.4194},
          zoom: 12,
          mapTypeId: "roadmap"
        }}
        markers={[
          %{
            id: "marker-1",
            position: %{lat: 37.7749, lng: -122.4194},
            title: "San Francisco",
            content: "<div class='bg-blue-500 text-white p-2 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold'>SF</div>",
            draggable: true
          },
          %{
            id: "marker-2",
            position: %{lat: 37.7849, lng: -122.4094},
            title: "Another Location"
            # Uses default marker appearance
          }
        ]}
        libraries={%{
          marker: %{
            eventHandlers: ["click", "dragstart", "drag", "dragend", "position_changed"]
          },
          maps: %{
            eventHandlers: ["click", "zoom_changed", "center_changed"]
          }
        }}
      />

  ## JSON Library Configuration

  You can specify which JSON library to use:

      # Use Jason explicitly
      <.live_component module={PetalProWeb.Components.GoogleMap} id="map" json_library={Jason} />

      # Use built-in JSON (Elixir 1.18+)
      <.live_component module={PetalProWeb.Components.GoogleMap} id="map" json_library={JSON} />

      # Use Poison
      <.live_component module={PetalProWeb.Components.GoogleMap} id="map" json_library={Poison} />

      # Auto-detect (default behavior)
      <.live_component module={PetalProWeb.Components.GoogleMap} id="map" />

  ## Library Configuration

  Configure specific Google Maps libraries with custom settings. Libraries are automatically
  loaded based on the keys present in `libraries`:

      <.live_component
        module={PetalProWeb.Components.GoogleMap}
        id="map"
        libraries={%{
          places: %{
            placeFields: "free",      # Use free tier fields only
            maxResults: 5,            # Limit results to reduce costs
            maxPhotos: 1,             # Limit photos
            includeReviews: false,    # Exclude expensive review data
            eventHandlers: ["place_changed", "places_changed"]  # Places events
          },
          drawing: %{
            drawingMode: "polygon",   # Default drawing mode
            drawingControl: true,     # Show drawing controls
            polygonOptions: %{
              fillColor: "#ff0000",
              fillOpacity: 0.3
            },
            eventHandlers: ["overlaycomplete", "drawingmode_changed"]  # Drawing events
          },
          marker: %{
            eventHandlers: ["click", "dragstart", "drag", "dragend", "position_changed"]  # Marker events
          },
          maps: %{
            eventHandlers: ["click", "zoom_changed", "bounds_changed"]  # Core map events
          }
        }}
      />

  In this example, `places`, `drawing`, and `marker` libraries will be automatically loaded.

  ## Marker Configuration

  The component supports Google Maps AdvancedMarker for maximum flexibility:

      <.live_component
        module={PetalProWeb.Components.GoogleMap}
        id="marker-map"
        map_options={%{
          center: %{lat: 37.7749, lng: -122.4194},
          zoom: 12
        }}
        markers={[
          # Simple marker with default appearance
          %{
            id: "simple-marker",
            position: %{lat: 37.7749, lng: -122.4194},
            title: "Simple Marker"
          },
          # Custom styled marker with HTML content
          %{
            id: "custom-marker",
            position: %{lat: 37.7849, lng: -122.4094},
            title: "Custom Marker",
            content: "<div class='bg-blue-500 text-white p-2 rounded-full w-10 h-10 flex items-center justify-center font-bold shadow-lg'>42</div>",
            draggable: true,
            zIndex: 100
          },
          # Business location marker
          %{
            id: "business-marker",
            position: %{lat: 37.7649, lng: -122.4294},
            title: "Business Location",
            content: "<div class='bg-green-600 text-white p-3 rounded-lg shadow-lg border-2 border-white'><div class='text-xs font-bold'>OPEN</div><div class='text-sm'>Store #123</div></div>"
          }
        ]}
        libraries={%{
          marker: %{
            eventHandlers: ["click", "dragend", "position_changed"]
          }
        }}
      />

  ### Marker Events

  Handle marker events in your LiveView:

      def handle_info({:marker_event, %{event_name: "click", params: %{"markerId" => marker_id}}}, socket) do
        # Handle marker click
        {:noreply, assign(socket, :selected_marker, marker_id)}
      end

      def handle_info({:marker_event, %{event_name: "dragend", params: %{"markerId" => marker_id, "data" => data}}}, socket) do
        # Handle marker drag end - data contains new position
        new_position = data["position"]
        # Update your data store with new marker position
        {:noreply, socket}
      end

  ## Map Types

  Google Maps supports different map types that you can switch between:

      map_options={%{
        mapTypeId: "roadmap"    # Default street map
        # mapTypeId: "satellite"  # Satellite imagery without labels
        # mapTypeId: "hybrid"     # Satellite imagery WITH street labels
        # mapTypeId: "terrain"    # Terrain map with elevation
      }}

  **Pro Tip**: Use `"hybrid"` instead of `"satellite"` to maintain street labels over satellite imagery.

  ## Map Controls

  By default, all Google Maps UI controls are disabled for a clean appearance. Enable them as needed:

      <.live_component
        module={PetalProWeb.Components.GoogleMap}
        id="map"
        map_controls={%{
          zoomControl: true,           # +/- zoom buttons
          mapTypeControl: true,        # Map/Satellite toggle
          streetViewControl: true,     # Street View pegman
          fullscreenControl: true,     # Fullscreen button
          scaleControl: true,          # Distance scale
          rotateControl: true,         # Rotation controls (for 45° imagery)
          panControl: false            # Pan control (deprecated in newer versions)
        }}
      />

  ### Control Positioning
  You can also control where UI elements appear:

      map_controls={%{
        zoomControl: true,
        zoomControlOptions: %{
          position: "TOP_RIGHT"  # or "TOP_LEFT", "BOTTOM_RIGHT", etc.
        },
        mapTypeControl: true,
        mapTypeControlOptions: %{
          position: "TOP_LEFT",
          style: "HORIZONTAL_BAR"  # or "DROPDOWN_MENU", "DEFAULT"
        }
      }}

  ### Places Library Field Sets

  - **"free"** - Free tier fields (no charge)
  - **"basic"** - Basic data ($0.017 per request)
  - **"contact"** - Contact information ($0.003 per request)
  - **"atmosphere"** - Ratings and reviews ($0.005 per request)
  - **"essential"** - Basic + contact (commonly used)
  - **"complete"** - All fields (most expensive)
  - **Custom array** - Specify exact fields: `["place_id", "name", "geometry"]`

  ## Events

  The component forwards all specified Google Maps events to your parent LiveView through
  normalized message handlers. Handle them with:

      # Map interaction events (normalized)
      def handle_info({:map_interaction, data}, socket) do
        case data.interaction do
          :click ->
            # Handle map click - data.data contains "position" with lat/lng coordinates
            position = data.data["position"]
            # Use position for placement logic, etc.
            {:noreply, socket}

          _ ->
            # Handle other map interactions
            {:noreply, socket}
        end
      end

      # Marker interaction events (normalized)
      def handle_info({:marker_interaction, data}, socket) do
        case data.interaction do
          :click ->
            # Handle marker click - data contains marker_id and position
            {:noreply, assign(socket, :selected_marker, data.marker_id)}

          :dragend ->
            # Handle marker drag end - update marker position in your data
            new_position = data.position
            # Update your markers list with new position
            {:noreply, socket}

          _ ->
            {:noreply, socket}
        end
      end

      # Raw events (fallback for unhandled events)
      def handle_info({:map_raw_event, data}, socket) do
        # Handle any events that don't have specific normalizers
        {:noreply, socket}
      end

  ### Event Data Structure

  - **Map interactions**: `{:map_interaction, %{component_id: id, interaction: :click, data: %{}}}`
  - **Marker interactions**: `{:marker_interaction, %{component_id: id, interaction: :click, marker_id: id, position: %{}, data: %{}}}`
  - **Raw events**: `{:map_raw_event, %{component_id: id, event_name: "event", params: %{}}}`

  ## Updating the Component

  You can update the component's configuration using send_update:

      # Update map options
      send_update(PetalProWeb.Components.GoogleMap,
        id: "my-map",
        map_options: %{mapTypeId: "satellite", zoom: 15}
      )

      # Update library configurations
      send_update(PetalProWeb.Components.GoogleMap,
        id: "my-map",
        libraries: %{
          drawing: %{
            drawingControl: true,
            eventHandlers: ["overlaycomplete", "drawingmode_changed"]
          },
          places: %{
            placeFields: "basic",
            eventHandlers: ["place_changed"]
          }
        }
      )

  ## Troubleshooting

  ### Map Disappears After Loading
  If the map loads initially but then disappears:
  - **Root Cause**: LiveView DOM diffing removes Google Maps DOM elements that weren't in the original render
  - **Solution**: The component uses `phx-update="ignore"` to prevent LiveView from managing the map container's DOM
  - **Technical Details**: When LiveView connects after initial page load, it compares current DOM with expected DOM and removes "unexpected" elements added by Google Maps

  ### Map Not Loading (Infinite Loading Spinner)
  If the map shows a loading spinner indefinitely:
  - **Check API Key**: Verify your Google Maps API key is valid and set in environment variables
  - **Enable APIs**: Ensure Maps JavaScript API is enabled in Google Cloud Console
  - **Check Console**: Look for JavaScript errors in browser developer console
  - **Verify Container**: Ensure the map container has explicit height/width dimensions
  - **Network Issues**: Check browser network tab for failed API requests

  ### Common Setup Issues
  - **Missing API Key**: Set `GOOGLE_MAPS_API_KEY` environment variable or pass via `api_key` attribute
  - **API Restrictions**: Check if your API key has domain/IP restrictions that might block requests
  - **Billing**: Ensure Google Cloud billing is enabled for your project
  - **Library Loading**: Check console logs for library loading failures

  ### Debug Mode
  The component includes detailed console logging. Check browser console for initialization steps:
  - `[GoogleMaps] Starting initialization for map: {id}`
  - `[GoogleMaps] Loading libraries for map: {id}`
  - `[GoogleMaps] Creating map instance for: {id}`
  - `[GoogleMaps] Map initialization complete for: {id}`

  ## Performance Optimization

  ### Dynamic JavaScript Loading
  The component uses a modular architecture that only loads JavaScript code for the libraries you actually use:

  - **Core Map**: Always loaded (required for basic map functionality)
  - **Drawing Tools**: Only loaded when `libraries` contains a `drawing` key
  - **Places Search**: Only loaded when `libraries` contains a `places` key
  - **Geometry Utils**: Only loaded when `libraries` contains a `geometry` key
  - **Visualization**: Only loaded when `libraries` contains a `visualization` key

  This keeps your JavaScript bundle size minimal and improves page load performance.

  ### Automatic Library Detection
  Libraries are automatically detected from your configuration:

      # Only loads core map functionality
      <.live_component module={PetalProWeb.Components.GoogleMap} id="basic-map" />

      # Automatically loads places library
      <.live_component
        module={PetalProWeb.Components.GoogleMap}
        id="places-map"
        libraries={%{places: %{placeFields: "free"}}}
      />

      # Automatically loads both drawing and geometry libraries
      <.live_component
        module={PetalProWeb.Components.GoogleMap}
        id="advanced-map"
        libraries={%{
          drawing: %{drawingControl: true},
          geometry: %{}  # Empty config still loads the library
        }}
      />

  ### Event Handler Optimization
  Only the event handlers you specify are attached to the map, reducing memory usage and improving performance.

  """
  use Phoenix.Component

  import PetalComponents.Loading

  attr :id, :string,
    required: true,
    doc: "Unique identifier for the map. Also used as Google Maps mapId for AdvancedMarker support."

  attr :api_key, :string, default: nil, doc: "Google Maps API key (defaults to env var)"
  attr :class, :string, default: "", doc: "CSS classes for the map container"
  attr :style, :string, default: "", doc: "Inline styles for the map container"
  attr :height, :string, default: "100%", doc: "Map height"
  attr :width, :string, default: "100%", doc: "Map width"

  # Google Maps Loader Options
  attr :loader_options, :map, default: %{}, doc: "Additional Google Maps Loader options"

  # Google Maps Configuration
  attr :map_options, :map, default: %{}, doc: "Google Maps map options"
  attr :map_controls, :map, default: %{}, doc: "Google Maps control options"

  attr :json_library, :atom,
    default: nil,
    doc: "JSON library module to use for encoding (e.g., JSON, Jason, Poison). If nil, uses built-in JSON (Elixir 1.18+)"

  attr :libraries, :map,
    default: %{},
    doc: """
    Configuration for specific Google Maps libraries. Libraries are automatically loaded based on the keys present.
    Each library can specify its own event handlers along with other configuration.
    Example:
    %{
      places: %{
        placeFields: "basic",  # or "free", "contact", "atmosphere", "essential", "complete", or custom array
        maxResults: 10,
        maxPhotos: 3,
        includeReviews: false,
        eventHandlers: ["place_changed", "places_changed"]  # Places-specific events
      },
      drawing: %{
        drawingMode: "polygon",
        drawingControl: true,
        eventHandlers: ["overlaycomplete", "drawingmode_changed"]  # Drawing-specific events
      },
      geometry: %{
        eventHandlers: []  # No events needed, just load the library
      },
      visualization: %{
        heatmapRadius: 20,
        eventHandlers: ["heatmap_created", "heatmap_removed"]  # Specific visualization events
      },
      marker: %{
        eventHandlers: ["click", "dragstart", "drag", "dragend", "position_changed"]  # Marker events (click auto-maps to gmp-click for AdvancedMarkers)
      },
      maps: %{
        eventHandlers: ["click", "zoom_changed", "center_changed", "bounds_changed"]  # Core map events
      }
    }

    ### Explicit Event Configuration

    Events must be explicitly configured in `libraries`. No events are forwarded by default.
    This gives you complete control over which events your LiveView receives.

    You must specify exactly which events you want for each library - there are no shortcuts or "all" options.
    This ensures your application only receives the events it actually needs.
    }
    """

  attr :markers, :list,
    default: [],
    doc: """
    List of markers to display on the map. Each marker should be a map with the following structure:
    %{
      id: "unique-marker-id",           # Required: Unique identifier for the marker
      position: %{lat: 37.7749, lng: -122.4194},  # Required: Marker position
      title: "Marker Title",            # Optional: Tooltip text
      content: "<div>Custom HTML</div>", # Optional: Custom HTML content for AdvancedMarker
      draggable: true,                  # Optional: Whether marker can be dragged (default: false)
      zIndex: 100,                      # Optional: Z-index for marker layering
      # Any other AdvancedMarker options can be included
    }

    Example:
    [
      %{
        id: "marker-1",
        position: %{lat: 37.7749, lng: -122.4194},
        title: "San Francisco",
        content: "<div class='bg-red-500 text-white p-2 rounded'>SF</div>",
        draggable: true
      },
      %{
        id: "marker-2",
        position: %{lat: 37.7849, lng: -122.4094},
        title: "Another Location"
        # Uses default marker appearance when no content specified
      }
    ]

    All markers are displayed simultaneously and use Google Maps AdvancedMarker for maximum flexibility.
    Markers support any valid HTML content for custom styling and appearance.
    """

  attr :shapes, :list,
    default: [],
    doc: """
    List of shapes to display on the map. Supports all Google Maps shape types: circles, polygons, polylines, and rectangles.
    Each shape should be a map with a `type` field and type-specific configuration:

    ## Circle
    %{
      id: "unique-circle-id",           # Required: Unique identifier
      type: :circle,                    # Required: Shape type
      center: %{lat: 37.7749, lng: -122.4194},  # Required: Circle center
      radius: 1609.34,                  # Required: Radius in meters (convert miles * 1609.344)
      strokeColor: "#FF0000",           # Optional: Border color
      strokeOpacity: 0.8,               # Optional: Border opacity (0.0-1.0)
      strokeWeight: 2,                  # Optional: Border width in pixels
      fillColor: "#FF0000",             # Optional: Fill color
      fillOpacity: 0.35,                # Optional: Fill opacity (0.0-1.0)
      draggable: false,                 # Optional: Whether shape can be dragged
      editable: false,                  # Optional: Whether shape can be edited
      clickable: true,                  # Optional: Whether shape responds to clicks
      zIndex: 1,                        # Optional: Z-index for layering
      visible: true                     # Optional: Whether shape is visible
    }

    ## Polygon
    %{
      id: "unique-polygon-id",
      type: :polygon,
      paths: [                          # Required: Array of coordinate arrays
        [
          %{lat: 37.7749, lng: -122.4194},
          %{lat: 37.7849, lng: -122.4094},
          %{lat: 37.7649, lng: -122.4094}
        ]
      ],
      strokeColor: "#FF0000",
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: "#FF0000",
      fillOpacity: 0.35,
      draggable: false,
      editable: false,
      clickable: true,
      zIndex: 1,
      visible: true
    }

    ## Polyline
    %{
      id: "unique-polyline-id",
      type: :polyline,
      path: [                           # Required: Array of coordinates
        %{lat: 37.7749, lng: -122.4194},
        %{lat: 37.7849, lng: -122.4094},
        %{lat: 37.7649, lng: -122.4094}
      ],
      strokeColor: "#FF0000",
      strokeOpacity: 1.0,
      strokeWeight: 2,
      draggable: false,
      editable: false,
      clickable: true,
      zIndex: 1,
      visible: true
    }

    ## Rectangle
    %{
      id: "unique-rectangle-id",
      type: :rectangle,
      bounds: %{                        # Required: Rectangle bounds
        north: 37.7849,
        south: 37.7649,
        east: -122.4094,
        west: -122.4294
      },
      strokeColor: "#FF0000",
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: "#FF0000",
      fillOpacity: 0.35,
      draggable: false,
      editable: false,
      clickable: true,
      zIndex: 1,
      visible: true
    }

    Example with multiple shape types:
    [
      # Circle with 2-mile radius (converted to meters: 2 * 1609.344 = 3218.688)
      %{
        id: "search-area",
        type: :circle,
        center: %{lat: 37.7749, lng: -122.4194},
        radius: 3218.688,
        strokeColor: "#0000FF",
        fillColor: "#0000FF",
        fillOpacity: 0.2,
        editable: true
      },
      # Polygon area
      %{
        id: "restricted-zone",
        type: :polygon,
        paths: [
          [
            %{lat: 37.7849, lng: -122.4294},
            %{lat: 37.7949, lng: -122.4194},
            %{lat: 37.7849, lng: -122.4094},
            %{lat: 37.7749, lng: -122.4194}
          ]
        ],
        strokeColor: "#FF0000",
        fillColor: "#FF0000",
        fillOpacity: 0.3
      },
      # Route polyline
      %{
        id: "route-1",
        type: :polyline,
        path: [
          %{lat: 37.7749, lng: -122.4194},
          %{lat: 37.7849, lng: -122.4094},
          %{lat: 37.7949, lng: -122.3994}
        ],
        strokeColor: "#00FF00",
        strokeWeight: 4
      }
    ]

    All shapes support event handling and can be made interactive with draggable and editable options.
    For circles, radius must be specified in meters. Convert miles to meters by multiplying by 1609.344.
    """

  def google_map(assigns) do
    # Apply attribute defaults for any missing values
    assigns =
      assigns
      |> assign_new(:loader_options, fn -> %{} end)
      |> assign_new(:map_options, fn -> %{} end)
      |> assign_new(:map_controls, fn -> %{} end)
      |> assign_new(:json_library, fn -> nil end)
      |> assign_new(:libraries, fn -> %{} end)
      |> assign_new(:markers, fn -> [] end)
      |> assign_new(:shapes, fn -> [] end)
      |> assign_new(:api_key, fn -> nil end)
      |> assign_new(:class, fn -> "" end)
      |> assign_new(:style, fn -> "" end)
      |> assign_new(:height, fn -> "100%" end)
      |> assign_new(:width, fn -> "100%" end)
      |> assign_computed_options()

    # Determine if we should use CSS classes for container filling
    assigns =
      assigns
      |> assign(:container_classes, build_container_classes(assigns))
      |> assign(:container_style, build_container_style(assigns))

    ~H"""
    <div class={@container_classes} style={@container_style}>
      <div
        id={@id}
        phx-hook="GoogleMapHook"
        data-api-key={@computed_api_key}
        data-config={@computed_config}
        class="size-full"
      >
        <!-- Loading state -->
        <div class="flex h-full items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div class="p-8 text-center">
            <.spinner size="lg" class="text-primary-600 mx-auto mb-4 dark:text-primary-400" />
            <h3 class="mb-1 text-lg font-semibold text-gray-900 dark:text-gray-100">Loading Map</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Please wait while the map initializes...
            </p>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp build_container_classes(assigns) do
    base_classes = ["relative"]

    # Add user-provided classes
    classes = if Map.get(assigns, :class, "") == "", do: [], else: [assigns.class]

    # Add container-filling classes when height/width are 100%
    container_classes =
      if Map.get(assigns, :height) == "100%" and Map.get(assigns, :width) == "100%" do
        ["h-full", "w-full", "flex-1", "min-h-0"]
      else
        []
      end

    base_classes ++ classes ++ container_classes
  end

  defp build_container_style(assigns) do
    height = Map.get(assigns, :height, "100%")
    width = Map.get(assigns, :width, "100%")
    style = Map.get(assigns, :style, "")

    # Only use inline styles when not using 100% dimensions
    if height != "100%" or width != "100%" do
      height_style = if height, do: "height: #{height};", else: ""
      width_style = if width, do: "width: #{width};", else: ""
      custom_style = if style == "", do: "", else: " #{style}"

      "#{height_style} #{width_style}#{custom_style}"
    else
      # For 100% dimensions, only include custom styles
      if style == "", do: "", else: style
    end
  end

  defp assign_computed_options(assigns) do
    # Get API key from assigns or environment
    api_key = Map.get(assigns, :api_key) || System.get_env("GOOGLE_MAPS_API_KEY")

    # Determine libraries to load from libraries keys
    libraries = Map.get(assigns, :libraries, %{})
    config_libraries = libraries |> Map.keys() |> Enum.map(&to_string/1)

    # Automatically add marker library if markers are present
    markers = Map.get(assigns, :markers, [])
    shapes = Map.get(assigns, :shapes, [])

    config_libraries =
      if length(markers) > 0 and "marker" not in config_libraries do
        ["marker" | config_libraries]
      else
        config_libraries
      end

    # Pass shapes directly without processing (conversions handled at LiveView level)
    processed_shapes = shapes

    # Ensure maps library is always included
    libraries_list = Enum.uniq(["maps" | config_libraries])

    loader_options =
      assigns
      |> Map.get(:loader_options, %{})
      |> Map.put(:libraries, libraries_list)
      |> ensure_default_loader_options()

    # Build default map options
    default_map_options = %{
      center: %{lat: 0, lng: 0},
      zoom: 8,
      mapTypeId: "roadmap",
      # Use the component ID as the Map ID for AdvancedMarkers
      mapId: Map.get(assigns, :id)
    }

    # Merge map options with defaults
    map_options = Map.merge(default_map_options, Map.get(assigns, :map_options, %{}))

    # Build default control options - all disabled by default for clean UI
    default_controls = %{
      zoomControl: false,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
      scaleControl: false,
      rotateControl: false,
      panControl: false,
      # Additional controls that might show up
      keyboardShortcuts: false,
      # This disables all default UI controls
      disableDefaultUI: true
    }

    # Merge control options with defaults
    map_controls = Map.merge(default_controls, Map.get(assigns, :map_controls, %{}))

    # Extract event subscriptions from library configs - explicit configuration required
    library_configs = Map.get(assigns, :libraries, %{})

    event_subscriptions =
      library_configs
      |> Enum.flat_map(fn {_library, config} ->
        Map.get(config, :eventSubscriptions, [])
      end)
      |> Enum.uniq()

    # Build complete configuration
    config = %{
      loaderOptions: loader_options,
      mapOptions: Map.merge(map_options, map_controls),
      eventSubscriptions: event_subscriptions,
      libraryConfigs: library_configs,
      markers: markers,
      shapes: processed_shapes
    }

    # Determine which JSON library to use
    json_library = get_json_library(assigns)

    assigns
    |> Map.put(:computed_api_key, api_key)
    |> Map.put(:computed_config, json_library.encode!(config))
  end

  defp get_json_library(assigns) do
    case Map.get(assigns, :json_library) do
      nil ->
        detect_json_library()

      library when is_atom(library) ->
        validate_json_library(library)
    end
  end

  defp validate_json_library(library) do
    if Code.ensure_loaded?(library) do
      # Verify the library has the encode!/1 function
      if function_exported?(library, :encode!, 1) do
        library
      else
        raise "JSON library #{inspect(library)} does not export encode!/1 function"
      end
    else
      raise "JSON library #{inspect(library)} is not available. Please ensure it's installed and loaded."
    end
  end

  defp detect_json_library do
    if Code.ensure_loaded?(JSON) do
      JSON
    else
      raise "No JSON library available. Please upgrade to Elixir 1.18+ for built-in JSON support or specify a JSON library explicitly with json_library={Jason}."
    end
  end

  defp ensure_default_loader_options(loader_options) do
    defaults = %{
      version: "weekly",
      language: "en",
      region: "US"
    }

    Map.merge(defaults, loader_options)
  end
end
