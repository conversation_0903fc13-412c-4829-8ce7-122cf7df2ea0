defmodule PetalProWeb.FeaturebaseComponents do
  @moduledoc """
  Components for Featurebase feedback and changelog widgets using Phoenix LiveView hooks
  """
  use Phoenix.Component

  @doc """
  Renders a container for Featurebase widgets that will be initialized via a LiveView hook.

  ## Examples

      <.featurebase_widgets user={@current_user} />

      <.featurebase_widgets user={@current_user} org_id={@current_org.id} />
  """
  attr :user, :map, default: nil
  attr :theme, :string, default: "light", values: ["light", "dark", ""]
  attr :locale, :string, default: "en"

  def featurebase_widgets(assigns) do
    user = Map.get(assigns, :user)
    user_hash = user_hash(user)

    config = PetalPro.config(:featurebase)

    widget_config = %{}

    widget_config =
      if user_hash do
        widget_config
        |> Map.put(:identify, %{
          "organization" => config[:organization],
          "userId" => user.id,
          "email" => user.email,
          "name" => user.name,
          "userHash" => user_hash
        })
        |> Map.put(:changelog, %{
          "organization" => config[:organization],
          "dropdown" => %{
            "enabled" => true,
            "placement" => "auto"
          },
          "popup" => %{
            "enabled" => true,
            "autoOpenForNewUpdates" => true
          },
          "theme" => assigns.theme,
          "locale" => assigns.locale
        })
      else
        widget_config
      end

    widget_config =
      if not messenger_enabled?() && user_hash do
        Map.put(widget_config, :feedback, %{
          "organization" => config[:organization],
          "theme" => assigns.theme,
          "placement" => "bottom-right",
          "defaultBoard" => "ideas",
          "locale" => assigns.locale,
          "metadata" => nil,
          "userHash" => user_hash
        })
      else
        widget_config
      end

    widget_config =
      if messenger_enabled?() do
        if user_hash do
          Map.put(widget_config, :messenger, %{
            "appId" => config[:app_id],
            "alignment" => "right",
            "theme" => assigns.theme,
            "language" => assigns.locale,
            "userHash" => user_hash
          })
        else
          Map.put(widget_config, :messenger, %{
            "appId" => config[:app_id],
            "alignment" => "right",
            "theme" => assigns.theme,
            "language" => assigns.locale
          })
        end
      else
        widget_config
      end

    assigns =
      if Enum.empty?(widget_config) do
        assign(assigns, :widget_config, nil)
      else
        assign(assigns, :widget_config, JSON.encode!(widget_config))
      end

    ~H"""
    <div
      :if={@widget_config}
      id="featurebase-widgets"
      phx-hook="FeaturebaseHook"
      data-config={@widget_config}
    >
    </div>
    """
  end

  defp user_hash(user) when not is_nil(user) do
    identify_verification_key = PetalPro.config([:featurebase, :identity_verification_key])

    :hmac
    |> :crypto.mac(:sha256, identify_verification_key, user.id)
    |> Base.encode16()
    |> String.downcase()
  end

  defp user_hash(_), do: nil

  defp messenger_enabled? do
    case System.get_env("FEATUREBASE_MESSENGER_ENABLED") do
      nil -> false
      value -> String.to_existing_atom(value)
    end
  end
end
