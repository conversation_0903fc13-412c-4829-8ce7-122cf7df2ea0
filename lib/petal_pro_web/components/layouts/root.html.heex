<!DOCTYPE html>
<html lang="en" class="h-full scroll-smooth">
  <.head conn={@conn}>
    <%= if Application.get_env(:live_debugger, :browser_features?) do %>
      <script id="live-debugger-scripts" src={Application.get_env(:live_debugger, :assets_url)}>
      </script>
    <% end %>
  </.head>

  <body class="font-sans h-full overflow-x-hidden bg-white text-gray-600 antialiased dark:bg-gray-950 dark:text-gray-400">
    {@inner_content}
  </body>
  <.featurebase_widgets :if={PetalPro.config([:featurebase, :enabled?])} user={@current_user} />
</html>
