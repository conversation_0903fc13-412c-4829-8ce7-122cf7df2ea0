defmodule PetalProWeb.Components.GoogleMapPlaceList do
  @moduledoc """
  A component for displaying a list of Google Places with their details.
  """
  use PetalProWeb, :component

  @doc """
  Renders a scrollable list of Google Places with details.

  ## Examples

      <.google_map_place_list
        places_stream={@streams.places}
        selected_place={@selected_place}
        loading={@search_result.loading}
      />

  """
  attr :places_stream, :any, required: true, doc: "The stream of places to display"
  attr :selected_place, :any, default: nil, doc: "The currently selected place"
  attr :loading, :boolean, default: false, doc: "Whether the search is loading"

  attr :assign_place_loading_id, :string,
    default: nil,
    doc: "The place ID currently being assigned"

  attr :id, :string, default: "places-list-container", doc: "Container ID"
  attr :class, :string, default: "", doc: "Additional CSS classes"

  def google_map_place_list(assigns) do
    ~H"""
    <div
      id={@id}
      class={[
        "relative flex w-full flex-col overflow-y-scroll py-1 pr-1 pl-2 lg:max-w-sm",
        "rounded-lg border border-gray-200 dark:border-gray-700",
        "bg-white dark:bg-gray-800",
        @class
      ]}
      phx-hook="ScrollIntoViewHook"
    >
      <%= if @loading do %>
        <div class="bg-white/80 backdrop-blur-xs absolute inset-0 z-10 flex items-center justify-center rounded-lg dark:bg-gray-800/80">
          <.spinner show={true} class="text-primary-600 dark:text-primary-400" />
        </div>
      <% end %>

      <div id="places-stream" phx-update="stream" class="space-y-1 sm:space-y-2">
        <div
          id="places-empty"
          class="hidden p-4 text-center text-gray-500 only:block dark:text-gray-400 sm:p-8"
        >
          <.icon
            name="hero-map-pin"
            class="mx-auto mb-3 h-12 w-12 text-gray-300 dark:text-gray-600 sm:mb-4 sm:h-16 sm:w-16"
          />
          <.p class="mb-2 text-sm font-medium text-gray-600 dark:text-gray-400 sm:mb-3">
            No places found
          </.p>
          <.p class="mx-auto max-w-xs text-xs leading-relaxed text-gray-400 dark:text-gray-500">
            Click anywhere on the map to place a search circle, then click "Search" to find self storage facilities
          </.p>
        </div>

        <.card
          :for={{dom_id, place} <- @places_stream}
          id={dom_id}
          class={[
            "w-full border border-gray-200 shadow-none dark:border-gray-700",
            if(place.assigned_to_org?, do: "opacity-50"),
            if(@selected_place && @selected_place.id == place.id,
              do: "ring-primary-300 ring-2",
              else: "hover:ring-primary-300 hover:ring-2"
            )
          ]}
          phx-click="select_place_from_list"
          phx-value-place_id={place.id}
        >
          <.card_content heading={place.name}>
            <div class="space-y-2">
              <%= if place.full_address do %>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  <.icon name="hero-map-pin" class="mr-1 inline h-4 w-4" />
                  <span class="leading-relaxed">
                    {place.full_address}
                  </span>
                </div>
              <% end %>

              <%= if place.phone_number do %>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  <.icon name="hero-phone" class="mr-1 inline h-4 w-4" />
                  <a
                    href={"tel:#{place.phone_number}"}
                    class="hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    {place.phone_number}
                  </a>
                </div>
              <% end %>

              <%= if place.website do %>
                <div class="text-sm">
                  <.icon name="hero-globe-alt" class="mr-1 inline h-4 w-4" />
                  <.link
                    href={place.website}
                    target="_blank"
                    class="font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    Visit Website
                  </.link>
                </div>
              <% end %>

              <div class="flex items-center gap-2 text-sm">
                <.rating rating={place.google_place.review_score || 0} total={5} />
                <span class="text-xs text-gray-500">
                  ({place.google_place.review_count || 0} reviews)
                </span>
              </div>
              
    <!-- Business Status and Additional Info -->
              <div class="flex items-center gap-2 text-xs">
                <%= if not place.google_place.is_business_claimed do %>
                  <.badge color="gray" label="Unclaimed on Google" />
                <% end %>

                <%= if place.google_place.permanently_closed do %>
                  <.badge color="danger" label="Permanently Closed" />
                <% end %>

                <%= if place.google_place.temporarily_closed do %>
                  <.badge color="warning" label="Temporarily Closed" />
                <% end %>
              </div>

              <%= if place.google_place.description do %>
                <div class="line-clamp-2 text-xs text-gray-500 dark:text-gray-400">
                  {place.google_place.description}
                </div>
              <% end %>
            </div>
          </.card_content>
          <.card_footer class="flex justify-end">
            <% is_loading = @assign_place_loading_id == place.id %>
            <%= if place.assigned_to_org? do %>
              <.badge color="success" label="Assigned" />
            <% else %>
              <.button
                type="button"
                color="primary"
                size="sm"
                label={if is_loading, do: "Assigning...", else: "Assign"}
                disabled={is_loading}
                phx-click="assign_place"
                phx-value-place_id={place.id}
              />
            <% end %>
          </.card_footer>
        </.card>
      </div>
    </div>
    """
  end
end
