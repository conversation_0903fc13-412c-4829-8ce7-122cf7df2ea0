defmodule PetalProWeb.Components.QuotaExhaustedScreen do
  @moduledoc """
  A component for displaying a quota exhausted screen with upgrade options.
  """
  use PetalProWeb, :component

  @doc """
  Renders a quota exhausted screen with progress indicator and upgrade options.

  ## Examples

      <.quota_exhausted_screen
        entitlement_used={15}
        entitlement_total={15}
        current_membership={@current_membership}
        current_org={@current_org}
      />

  """
  attr :entitlement_used, :integer, required: true, doc: "Number of opportunities used"
  attr :entitlement_total, :integer, required: true, doc: "Total opportunity entitlement"
  attr :current_membership, :map, required: true, doc: "Current user's membership"
  attr :current_org, :map, required: true, doc: "Current organization"
  attr :period_end_date, :any, required: true, doc: "When the current billing period ends"

  def quota_exhausted_screen(assigns) do
    ~H"""
    <.container max_width="md" class="mt-8 sm:mt-12">
      <div class="py-12 text-center">
        <!-- Icon -->
        <div class="mx-auto mb-6 flex h-24 w-24 items-center justify-center rounded-full bg-amber-100 dark:bg-amber-900/30">
          <.icon name="hero-chart-bar" class="h-12 w-12 text-amber-600 dark:text-amber-400" />
        </div>
        
    <!-- Heading -->
        <h1 class="mb-3 text-2xl font-bold text-gray-900 dark:text-gray-100">
          Quota Reached
        </h1>
        
    <!-- Description -->
        <p class="mx-auto mb-8 max-w-lg text-lg text-gray-600 dark:text-gray-400">
          You've assigned all {@entitlement_total} opportunities for this billing period.
          More assignments will be available on <strong><%= @period_end_date && Boring.Cldr.DateTime.to_string!(@period_end_date, format: :yMEd) %></strong>,
          or you can upgrade your plan for more assignments now.
        </p>
        
    <!-- Progress Indicator -->
        <div class="mx-auto mb-8 max-w-md">
          <.progress size="xl" value={@entitlement_used} max={@entitlement_total} class="w-full" />
          <div class="mt-2">
            <.p class="text-sm text-gray-600 dark:text-gray-400">
              {@entitlement_used} of {@entitlement_total} opportunities assigned
            </.p>
          </div>
        </div>
        
    <!-- Action Buttons -->
        <div class="flex flex-col justify-center gap-3 sm:flex-row">
          <%= if @current_membership.role == :admin do %>
            <.button
              link_type="live_redirect"
              to={~p"/app/org/#{@current_org.slug}/subscribe"}
              label="Upgrade Plan"
              color="primary"
              size="lg"
              class="px-8"
            />
          <% end %>

          <.button
            link_type="live_redirect"
            to={~p"/app/org/#{@current_org.slug}"}
            label="Back to Dashboard"
            color="gray"
            variant="outline"
            size="lg"
            class="px-8"
          />
        </div>
        
    <!-- Additional Info for Non-Admins -->
        <%= if @current_membership.role != :admin do %>
          <div class="mx-auto mt-8 max-w-lg rounded-lg border border-blue-200 bg-blue-50 p-4 dark:bg-blue-900/20 dark:border-blue-700">
            <.p class="text-sm text-blue-800 dark:text-blue-200">
              Contact your organization admin to upgrade your plan for more opportunity assignments.
            </.p>
          </div>
        <% end %>
      </div>
    </.container>
    """
  end
end
