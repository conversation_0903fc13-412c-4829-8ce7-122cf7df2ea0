defmodule PetalProWeb.AssignScopePlug do
  @moduledoc """
  Sets a `:scope` assign using Boring.Scope.new with the actor set as the current user
  and tenant set as the current organization when applicable.

  This is used alongside the existing `:current_user` and `:current_org` assigns.
  """
  @behaviour Plug

  def init(options), do: options

  def call(%{assigns: %{current_user: current_user, current_org: current_org}} = conn, _opts)
      when not is_nil(current_user) and not is_nil(current_org) do
    # Get Boring.Accounts.User and Boring.Orgs.Org records
    ash_user = Boring.Accounts.get_user_by_id!(current_user.id, authorize?: false)

    ash_org =
      Boring.Orgs.get_org_by_id!(current_org.id,
        load: [:active_subscriber?, :billing_product],
        authorize?: false
      )

    Plug.Conn.assign(conn, :scope, %Boring.Scope{current_user: ash_user, current_tenant: ash_org})
  end

  def call(%{assigns: %{current_user: current_user}} = conn, _opts) when not is_nil(current_user) do
    # Get Boring.Accounts.User record
    ash_user = Boring.Accounts.get_user_by_id!(current_user.id, authorize?: false)

    Plug.Conn.assign(conn, :scope, %Boring.Scope{current_user: ash_user})
  end

  def call(conn, _opts) do
    Plug.Conn.assign(conn, :scope, %Boring.Scope{})
  end
end
