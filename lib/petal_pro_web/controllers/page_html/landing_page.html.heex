<%!-- This should match max_width in `public.html.heex`. Options: ["sm", "md", "lg", "xl", "full"] --%>
<% max_width = "xl" %>
<LandingPageComponents.hero
  image_src_light="https://res.cloudinary.com/boring-business-leads/image/upload/v1751574076/deal_flow_gzhzvj.png"
  image_src_dark="https://res.cloudinary.com/boring-business-leads/image/upload/v1751574076/deal_flow_gzhzvj.png"
  max_width={max_width}
>
  <:title>
    <span class="block">{gettext("Snag your next")}</span>
    <span class="block">
      <span class="from-primary-800 via-primary-600 to-primary-400 bg-gradient-to-r bg-clip-text text-transparent dark:from-primary-600 dark:via-primary-400 dark:to-primary-200">
        {gettext("Off-Market")}
      </span>
      <span>{gettext(" deal")}</span>
    </span>
  </:title>
  <:action_buttons>
    <.button
      label={gettext("Start free trial")}
      link_type="a"
      color="primary"
      to="#pricing"
      size="lg"
    />
  </:action_buttons>
  <:description>
    {gettext(
      "Find off-market self-storage opportunities, get owner contact info, track deals through your pipeline, and collaborate with your team—all in one platform."
    )}
  </:description>
</LandingPageComponents.hero>

<LandingPageComponents.features
  title={gettext("Features")}
  description={gettext("Features to to help you find, track, and close deals.")}
  features={[
    %{
      id: "1",
      title: gettext("Lead Search"),
      description: gettext("Interactive map to discover new opportunities"),
      icon: "hero-magnifying-glass-solid"
    },
    %{
      id: "2",
      title: gettext("Deal Flow"),
      description:
        gettext(
          "Track deals from outreach to acquisition with status tracking, contact management, and follow-ups"
        ),
      icon: "hero-funnel-solid"
    },
    %{
      id: "3",
      title: gettext("Teams"),
      description: gettext("Collaborate on your deal flow with your team members"),
      icon: "hero-user-group-solid"
    }
  ]}
  max_width={max_width}
/>

<LandingPageComponents.solo_feature
  title={gettext("Search")}
  icon="hero-magnifying-glass"
  description={gettext("Search your target areas for self-storage opportunities")}
  image_src_light="https://res.cloudinary.com/boring-business-leads/image/upload/v1751574076/deal_search_plvb1a.png"
  image_src_dark="https://res.cloudinary.com/boring-business-leads/image/upload/v1751574076/deal_search_plvb1a.png"
  blur_color="primary"
  max_width={max_width}
>
</LandingPageComponents.solo_feature>

<%!-- <LandingPageComponents.testimonials
  max_width={max_width}
  testimonials={
    [
      "https://res.cloudinary.com/wickedsites/image/upload/v1636595191/dummy_data/avatar_32_cayiid.png",
      "https://res.cloudinary.com/wickedsites/image/upload/v1636595191/dummy_data/avatar_26_mjcxen.png",
      "https://res.cloudinary.com/wickedsites/image/upload/v1636595189/dummy_data/avatar_10_d3tw5q.png",
      "https://res.cloudinary.com/wickedsites/image/upload/v1636595190/dummy_data/avatar_17_eolat2.png",
      "https://res.cloudinary.com/wickedsites/image/upload/v1636595189/dummy_data/avatar_13_xrlvql.png"
    ]
    |> Enum.map(fn src ->
      %{
        name: Faker.Person.name(),
        title: "CEO, #{Faker.Company.name()}",
        image_src: src,
        content: Faker.Lorem.sentence(10..20)
      }
    end)
  }
/>
--%>

<LandingPageComponents.pricing title={gettext("Pricing")} max_width={max_width} />

<LandingPageComponents.faq
  title={gettext("Everything you need to know")}
  description={gettext("Frequently Asked Questions.")}
  max_width={max_width}
/>
