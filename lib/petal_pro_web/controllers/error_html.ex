defmodule PetalProWeb.ErrorHTML do
  use PetalProWeb, :html

  # If you want to customize your error pages,
  # uncomment the embed_templates/1 call below
  # and add pages to the error directory:
  #
  #   * lib/petal_pro_web/controllers/error/404.html.heex
  #   * lib/petal_pro_web/controllers/error/500.html.heex
  #
  # embed_templates "error/*"

  # The default is to render a plain text page based on
  # the template name. For example, "404.html" becomes
  # "Not Found".

  def render(template, assigns) do
    maybe_show_sentry_form(template, assigns, PetalPro.config(:env))
  end

  defp maybe_show_sentry_form("500.html" = template, assigns, :prod) do
    case Sentry.get_last_event_id_and_source() do
      {event_id, :plug} when is_binary(event_id) ->
        opts = Jason.encode!(%{eventId: event_id})

        assigns = assign(assigns, :opts, opts)

        ~H"""
        <script
          src="https://browser.sentry-cdn.com/5.9.1/bundle.min.js"
          integrity="sha384-/x1aHz0nKRd6zVUazsV6CbQvjJvr6zQL2CHbQZf3yoLkezyEtZUpqUNnOLW9Nt3v"
          crossorigin="anonymous"
        >
        </script>
        <script>
          Sentry.init({ dsn: '<%= Sentry.Config.dsn() %>' });
          Sentry.showReportDialog(<%= raw @opts %>)
        </script>
        """

      _ ->
        Phoenix.Controller.status_message_from_template(template)
    end
  end

  defp maybe_show_sentry_form(template, _assigns, _env) do
    Phoenix.Controller.status_message_from_template(template)
  end
end
