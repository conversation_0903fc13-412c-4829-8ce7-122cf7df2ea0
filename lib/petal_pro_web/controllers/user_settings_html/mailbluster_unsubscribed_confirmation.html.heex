<div class="fixed inset-0 z-10 overflow-y-auto">
  <div class="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 transition-opacity" aria-hidden="true">
      <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
    </div>

    <span class="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">
      &#8203;
    </span>
    <div
      class="inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-headline"
    >
      <div>
        <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
          <svg
            class="h-6 w-6 text-green-600"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <div class="mt-3 text-center sm:mt-5">
          <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-headline">
            {gettext("Unsubscribed successfully")}
          </h3>
          <div class="prose-sm mt-2 text-sm text-gray-500">
            {gettext("You will no longer receive marketing related notifications.")}
          </div>
        </div>

        <div class="mt-10 flex items-center justify-center gap-5">
          <.button to="/" link_type="a" label={gettext("Continue")} />
        </div>
      </div>
    </div>
  </div>
</div>
