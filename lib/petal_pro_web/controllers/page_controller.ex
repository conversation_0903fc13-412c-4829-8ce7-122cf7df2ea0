defmodule PetalProWeb.PageController do
  use PetalProWeb, :controller

  def landing_page(conn, _params) do
    render(conn, :landing_page, page_title: gettext("Home"))
  end

  def license(conn, _params) do
    render(conn, :license, page_title: gettext("License"))
  end

  def subscribed(conn, _params) do
    render(conn, :subscribed)
  end

  def policy(conn, params) do
    policy_slug = params["policy_slug"]

    policy =
      :policies
      |> PetalPro.config()
      |> Enum.find(&(&1.slug == policy_slug))

    render(conn, :policy, page_title: policy.title, policy_id: policy.id)
  end
end
