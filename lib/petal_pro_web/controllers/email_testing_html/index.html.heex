<PetalProWeb.DevLayoutComponent.dev_layout
  current_page={:dev_email_templates}
  current_user={@current_user}
>
  <div class="flex h-screen overflow-hidden bg-white dark:bg-gray-900">
    <div class="hidden md:flex md:shrink-0">
      <div class="flex w-64 flex-col">
        <div class="flex h-0 flex-1 flex-col border-r border-gray-200 dark:border-gray-800">
          <div class="flex flex-1 flex-col overflow-y-auto pt-6 pb-4">
            <h3 class="pl-4 text-xs font-semibold uppercase text-gray-500 dark:text-gray-400">
              Emails
            </h3>
            <nav class="mt-2 flex-1 space-y-1 px-2">
              <%= for option <- @email_options do %>
                <.link
                  href={~p"/dev/emails/preview/#{option}"}
                  class={menu_item_classes(@email_name == option)}
                >
                  {option}
                </.link>
              <% end %>
            </nav>
          </div>
        </div>
      </div>
    </div>
    <div class="flex w-0 flex-1 flex-col overflow-hidden">
      <main class="relative z-0 flex-1 overflow-y-auto focus:outline-hidden">
        <div class="py-6">
          <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
            <.h2>
              {@email_name}
            </.h2>

            <div class="flex justify-between">
              <div class="text-sm dark:text-gray-100">
                <div>
                  From
                  <strong>
                    {@email.from |> elem(0)} , {@email.from |> elem(1)}
                  </strong>
                </div>
                <div>
                  Subject:
                  <strong>
                    {@email.subject}
                  </strong>
                </div>
                <div>Preview text: {@email.assigns[:preview_text]}</div>
              </div>

              <%= if Application.fetch_env!(:boring, PetalPro.Mailer) |> Keyword.get(:adapter) ==
                   Swoosh.Adapters.Local do %>
                <.alert class="!w-auto max-w-sm self-start" color="warning">
                  Can't send test emails while Swoosh adapter is Swoosh.Adapters.Local. See dev.exs
                </.alert>
              <% else %>
                <%= if Util.email_valid?(@current_user.email) do %>
                  <%= PhoenixHTMLHelpers.Form.form_for :test_email, ~p"/dev/emails/send_test_email/#{@email_name}", fn _f -> %>
                    <div class="text-right">
                      <.button label={"Send to #{@current_user.email}"} size="sm" />
                    </div>
                  <% end %>
                <% else %>
                  <.alert class="!w-auto max-w-sm self-start" color="warning">
                    Can't send test email to {@current_user.email} . Please ensure it is a legit email.
                  </.alert>
                <% end %>
              <% end %>
            </div>

            <div class="mt-10">
              <.h3 class="mt-5 mb-3">
                Desktop view
              </.h3>
              <iframe
                style="width: 100%; height: 800px; border: 2px solid #bfbfbf;"
                src={@iframe_url}
              >
              </iframe>
            </div>
            <div class="mt-10">
              <.h3 class="mt-10 mb-3">
                Mobile view
              </.h3>
              <iframe
                style="width: 360px; height: 400px; border: 2px solid #bfbfbf;"
                src={@iframe_url}
              >
              </iframe>
            </div>
            <.h3 class="mt-10 mb-3">
              Plain text
            </.h3>
            <div class="whitespace-pre-line bg-white p-5">
              {@email.text_body}
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</PetalProWeb.DevLayoutComponent.dev_layout>
