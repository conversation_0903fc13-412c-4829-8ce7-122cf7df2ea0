defmodule Boring.Accounts do
  @moduledoc false
  use Ash.Domain, extensions: [AshPhoenix]

  resources do
    resource __MODULE__.User do
      define :get_user_by_email, action: :read, get_by: :email
      define :get_user_by_id, action: :read, get_by: :id
    end

    resource __MODULE__.UserToken
    resource __MODULE__.UserPin
    resource __MODULE__.UserTotp

    resource __MODULE__.WaitlistRequest do
      define :create_waitlist_request, action: :create
      define :destroy_waitlist_request, action: :destroy
      define :send_invites_by_region, args: [:region]
      define :send_invite
    end
  end
end
