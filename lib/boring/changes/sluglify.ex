defmodule Boring.Changes.Slugify do
  @moduledoc false
  use Ash.Resource.Change

  @impl true
  def init(opts) do
    if (opts[:attribute] && is_atom(opts[:attribute])) ||
         (opts[:generate_random?] && is_boolean(opts[:generate_random?])) do
      {:ok, opts}
    else
      {:error, "attribute or generate_random? is required!"}
    end
  end

  @impl true
  def change(changeset, [attribute: attribute], _context) do
    case Ash.Changeset.fetch_change(changeset, attribute) do
      {:ok, new_value} ->
        slug = Slug.slugify(new_value)
        Ash.Changeset.force_change_attribute(changeset, :slug, slug)

      :error ->
        changeset
    end
  end

  @impl true
  def change(changeset, [generate_random?: true], _context) do
    random_name = "#{Faker.Color.fancy_name()} #{Faker.Pokemon.name()}"
    slug = Slug.slugify(random_name)
    Ash.Changeset.force_change_attribute(changeset, :slug, slug)
  end

  @impl true
  def change(changeset, _opts, _context) do
    changeset
  end
end
