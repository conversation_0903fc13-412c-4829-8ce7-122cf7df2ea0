defmodule Boring.Features do
  @moduledoc false
  use Ash.Domain

  resources do
    resource __MODULE__.Feature do
      define :list_features, action: :read

      define :list_enabled_features, action: :read_enabled
      define :list_disabled_features, action: :read_disabled

      define :get_feature, action: :read, get_by: :name, not_found_error?: false
      define :create_feature, args: [:name], action: :create
      define :update_feature, action: :update
      define :delete_feature, action: :destroy
    end
  end
end
