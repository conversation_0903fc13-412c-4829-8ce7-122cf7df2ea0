defmodule Boring.Notifications.UserNotification do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Notifications

  alias <PERSON><PERSON>.Accounts.User
  alias Boring.Orgs.Org

  postgres do
    table "user_notifications"
    repo Boring.Repo

    references do
      reference :recipient, on_delete: :delete
      reference :sender, on_delete: :delete
      reference :org, on_delete: :delete
    end

    custom_indexes do
      # all unread_notifications for user
      index [:recipient_id, :read_at]
      # maybe read notifications on mount with request_path
      index [:recipient_id, :read_path, :read_at]
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :type, :user_notification_type, allow_nil?: false, public?: true
    attribute :message, :string, public?: true

    attribute :read_at, :utc_datetime, public?: true

    # optional to cater for broad use cases, but :read_path is
    # important for most notifications as it's queried for during on_mount/4
    # so we can mark a notification read when loading the relevant page
    attribute :read_path, :string, public?: true

    timestamps()
  end

  relationships do
    belongs_to :recipient, User, allow_nil?: false, public?: true
    belongs_to :sender, User, public?: true
    belongs_to :org, Org, public?: true
  end
end
