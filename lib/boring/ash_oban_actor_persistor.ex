defmodule Boring.AshObanActorPersister do
  @moduledoc false
  use AshOban.ActorPersister

  alias <PERSON>ring.Accounts.User, as: Ash<PERSON>ser
  alias PetalPro.Accounts.User, as: PetalProUser

  def store(%PetalProUser{id: id}), do: %{"type" => "user", "id" => id}

  def store(%AshUser{id: id}), do: %{"type" => "user", "id" => id}

  def lookup(%{"type" => "user", "id" => id}) do
    case Boring.Accounts.get_user_by_id(id, authorize?: false) do
      {:ok, user} -> {:ok, user}
      {:error, error} -> {:error, error}
    end
  end

  # This allows you to set a default actor
  # in cases where no actor was present
  # when scheduling.
  def lookup(nil), do: {:ok, nil}
end
