defmodule Boring.Geo.Circle do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    domain: Boring.Geo

  resource do
    require_primary_key? false
  end

  actions do
    default_accept []

    create :create do
      primary? true

      argument :center, :struct do
        constraints instance_of: Geo.Point
        allow_nil? false
      end

      argument :radius, :integer do
        constraints min: 0
        allow_nil? false
      end

      argument :units, :atom do
        default :miles
        allow_nil? false
      end

      change fn changeset, _context ->
        center = Ash.Changeset.get_argument(changeset, :center)
        radius = Ash.Changeset.get_argument(changeset, :radius)
        units = Ash.Changeset.get_argument(changeset, :units)

        geom =
          center
          |> Geo.Turf.Transformation.circle(radius, units: units)
          |> Map.put(:srid, center.srid)

        properties = %{sub_type: "Circle", center: center, radius: %{value: radius, units: units}}

        changeset
        |> Ash.Changeset.force_change_attribute(:properties, properties)
        |> Ash.Changeset.force_change_attribute(:geometry, geom)
      end
    end

    action :get_radius_as_units, :integer do
      argument :circle, :struct do
        constraints instance_of: __MODULE__
        allow_nil? false
      end

      argument :units, :atom do
        allow_nil? false
      end

      run fn inputs, _context ->
        units = Ash.ActionInput.get_argument(inputs, :units)
        circle = Ash.ActionInput.get_argument(inputs, :circle)

        new_radius =
          Geo.Turf.Math.convert_length(
            circle.properties.radius.value,
            circle.properties.radius.units,
            units
          )

        {:ok, new_radius}
      end
    end

    action :get_bounding_box, :struct do
      argument :circle, :struct do
        constraints instance_of: __MODULE__
        allow_nil? false
      end

      run fn inputs, _context ->
        circle = Ash.ActionInput.get_argument(inputs, :circle)
        {:ok, Geo.Turf.Helpers.bbox(circle.geometry)}
      end
    end
  end

  attributes do
    attribute :type, :string do
      allow_nil? false
      default "Feature"
    end

    attribute :geometry, :struct do
      constraints instance_of: Geo.Polygon
      allow_nil? false
    end

    attribute :properties, __MODULE__.Types.Properties do
      allow_nil? false
    end
  end
end
