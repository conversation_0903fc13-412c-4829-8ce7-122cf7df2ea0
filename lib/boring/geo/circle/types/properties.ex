defmodule Boring.Geo.Circle.Types.Properties do
  @moduledoc false
  use Ash.Type.NewType,
    subtype_of: :map,
    constraints: [
      fields: [
        sub_type: [
          type: :string,
          allow_nil?: false
        ],
        center: [
          type: :struct,
          constraints: [
            instance_of: Geo.Point
          ],
          allow_nil?: false
        ],
        radius: [
          type: :map,
          constraints: [
            fields: [
              value: [
                type: :integer,
                constraints: [
                  min: 0
                ],
                allow_nil?: false
              ],
              units: [
                type: :atom,
                allow_nil?: false
              ]
            ]
          ]
        ]
      ]
    ]
end
