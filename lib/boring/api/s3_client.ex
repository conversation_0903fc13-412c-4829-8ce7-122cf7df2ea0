defmodule Boring.Api.S3Client do
  @moduledoc false

  def list_buckets(config_overrides \\ []) do
    ExAws.request(ExAws.S3.list_buckets(), config_overrides)
  end

  def list_objects(bucket, config_overrides \\ []) do
    bucket
    |> ExAws.S3.list_objects()
    |> ExAws.request(config_overrides)
  end

  def put_object(bucket, key, body, config_overrides \\ []) do
    bucket
    |> ExAws.S3.put_object(key, body)
    |> ExAws.request(config_overrides)
  end

  def delete_object(bucket, key, config_overrides \\ []) do
    bucket
    |> ExAws.S3.delete_object(key)
    |> ExAws.request(config_overrides)
  end

  def get_object(bucket, key, config_overrides \\ []) do
    bucket
    |> ExAws.S3.get_object(key)
    |> ExAws.request(config_overrides)
  end

  def presigned_post(config, bucket, key, opts \\ []) do
    ExAws.S3.presigned_post(config, bucket, key, opts)
  end

  def presigned_url(config, http_method, bucket, key, opts \\ []) do
    ExAws.S3.presigned_url(config, http_method, bucket, key, opts)
  end
end
