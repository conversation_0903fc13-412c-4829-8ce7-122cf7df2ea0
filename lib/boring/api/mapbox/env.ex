defmodule Boring.Api.Mapbox.Env do
  @moduledoc """
  This Env module wraps request & response data for the Mapbox API.
  First you build a request, then call `get_static_image` to fetch the image.
  """

  use Ash.Resource,
    otp_app: :boring,
    domain: Boring.Api.Mapbox,
    data_layer: Ash.DataLayer.Simple

  alias Boring.Api.HttpClient

  resource do
    require_primary_key? false
  end

  actions do
    defaults [:create, :update, :read, :destroy]

    create :build do
      argument :request_attrs, :map, allow_nil?: false, public?: true
      accept []

      change set_attribute(:request, arg(:request_attrs))
    end

    update :get_static_image do
      accept []
      change before_action(&get_static_image/2)
    end
  end

  attributes do
    attribute :request, Boring.Api.Mapbox.Request do
      allow_nil? false
      public? true
    end

    attribute :response, Boring.Api.Mapbox.Response do
      allow_nil? true
      public? true
    end
  end

  defp get_static_image(changeset, _ctx) do
    request = Ash.Changeset.get_attribute(changeset, :request)

    case HttpClient.get(request.url) do
      {:ok, %{status: 200, body: body}} ->
        Ash.Changeset.force_change_attribute(changeset, :response, %{
          status: 200,
          image: body
        })

      {:ok, %{status: status, body: body} = response} ->
        Ash.Changeset.add_error(changeset,
          field: :response,
          message: "Error with #{status} status and body: #{inspect(body)}",
          value: response
        )

      {:error, reason} ->
        Ash.Changeset.add_error(changeset, [reason])
    end
  end
end
