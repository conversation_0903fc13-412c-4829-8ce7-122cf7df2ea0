defmodule Boring.Api.Mapbox.Request do
  @moduledoc false
  use Ash.Resource,
    data_layer: :embedded

  @type t :: %__MODULE__{}

  @base_url "https://api.mapbox.com/styles/v1"

  actions do
    defaults [:update, :read, :destroy]

    create :create do
      accept [:style_id, :latitude, :longitude, :zoom, :width, :height]
      primary? true

      change fn changeset, _ctx ->
        if is_nil(access_token()) do
          Ash.Changeset.add_error(changeset,
            message: "Mapbox access token not set in application config",
            value: nil
          )
        else
          url =
            changeset
            |> get_attributes([:style_id, :latitude, :longitude, :zoom, :width, :height])
            |> build_url()

          Ash.Changeset.force_change_attribute(changeset, :url, url)
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id
    attribute :style_id, :string, allow_nil?: false, public?: true, default: "satellite-v9"
    attribute :latitude, :float, allow_nil?: false, public?: true
    attribute :longitude, :float, allow_nil?: false, public?: true
    attribute :zoom, :integer, allow_nil?: false, public?: true, default: 16
    attribute :width, :integer, allow_nil?: false, public?: true, default: 1024
    attribute :height, :integer, allow_nil?: false, public?: true, default: 1024
    attribute :url, :string, allow_nil?: false, public?: true
  end

  defp build_url(attrs) do
    "#{@base_url}/#{username()}/#{attrs.style_id}/static/pin-s-l+FF0000(#{attrs.longitude},#{attrs.latitude})/#{attrs.longitude},#{attrs.latitude},#{attrs.zoom}/#{attrs.width}x#{attrs.height}?access_token=#{access_token()}"
  end

  defp access_token do
    Application.get_env(:boring, :mapbox)[:access_token]
  end

  defp username do
    Application.get_env(:boring, :mapbox)[:username] || "mapbox"
  end

  defp get_attributes(changeset, list_of_keys) do
    Map.new(list_of_keys, fn key ->
      {key, Ash.Changeset.get_attribute(changeset, key)}
    end)
  end
end
