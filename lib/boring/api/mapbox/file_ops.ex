defmodule Boring.Api.Mapbox.FileOps do
  @moduledoc false

  @behaviour Boring.Api.Mapbox.IO

  def write(path, content), do: image_download_path() |> Path.join(path) |> File.write(content)

  def read_path(path), do: Path.join(image_static_path(), path)

  defp image_download_path do
    Application.get_env(:boring, :mapbox)[:image_download_path]
  end

  def image_static_path do
    Application.get_env(:boring, :mapbox)[:image_static_path]
  end
end
