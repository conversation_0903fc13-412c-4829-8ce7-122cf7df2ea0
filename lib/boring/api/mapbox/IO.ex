defmodule Boring.Api.Mapbox.IO do
  @moduledoc """
  We have 2 different ways of saving a mapbox image. One is to save it to a file and the other is to save it to an S3 bucket.
  This module defines the interface for saving the image to a file.
  And then the src (with read_path) so that we can embed a url/path in the html and it will load the image without having to go through the phoenix server.
  """
  @callback write(path :: String.t(), content :: binary()) :: :ok | {:error, any()}
  @callback read_path(filename :: String.t()) :: String.t()
end
