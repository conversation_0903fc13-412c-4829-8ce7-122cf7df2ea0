defmodule Boring.Api.Mapbox.S3Ops do
  @moduledoc false

  @behaviour Boring.Api.Mapbox.IO

  alias Boring.Api.S3Client

  def write(path, content) do
    case S3Client.put_object(mapbox_assets_bucket(), path, content, config_overrides()) do
      {:ok, _} -> :ok
      {:error, error} -> {:error, error}
    end
  end

  def read_path(filename) do
    Path.join(image_static_path(), filename)
  end

  defp image_static_path do
    Application.get_env(:boring, :mapbox)[:image_static_path]
  end

  defp mapbox_assets_bucket do
    Application.get_env(:boring, :mapbox)[:assets_bucket]
  end

  # this ensures that tigris object storage is called with correct credentials
  # in the case that they are not available at build time
  defp config_overrides do
    [
      access_key_id: Application.get_env(:boring, :tigris)[:access_key_id],
      secret_access_key: Application.get_env(:boring, :tigris)[:secret_access_key]
    ]
  end
end
