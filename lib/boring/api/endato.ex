defmodule Boring.Api.Endato do
  @moduledoc """
  Client for interacting with the Endato API.

  This module provides a simple interface for working with Endato's Property Search V2 API,
  allowing you to search for property and owner information. It handles authentication
  and request formatting automatically.

  The module uses the Req HTTP client library to make requests to the Endato API.
  All functions come in two variants:
  - Regular functions that return `{:ok, %Req.Response{}}` or `{:error, reason}`
  - Bang (!) functions that return the response directly or raise an exception

  ## Authentication

  Endato uses custom headers for authentication:
  - `galaxy-ap-name`: Access Profile Name
  - `galaxy-ap-password`: Access Profile Password
  - `galaxy-search-type`: Search Type (e.g., "Property", "PropertyV2")

  ## Examples

      # Search for properties by address
      {:ok, %Req.Response{status: 200, body: body}} = Boring.Api.Endato.property_search([
        addressLine1: "1140 Oak St",
        addressLine2: "Brandon, MS"
      ])

      # Search for properties by owner name
      {:ok, %Req.Response{status: 200, body: body}} = Boring.Api.Endato.property_search([
        addressLine1: "123 Main St",
        addressLine2: "Springfield, IL",
        firstName: "<PERSON>",
        lastName: "Doe"
      ])
  """

  @type search_params :: %{
          optional(String.t()) => String.t()
        }

  @type property_search_params :: [
          address_line1: String.t(),
          address_line2: String.t(),
          apn: String.t(),
          business_name: String.t(),
          first_name: String.t(),
          middle_name: String.t(),
          last_name: String.t(),
          tahoe_id: String.t(),
          poseidon_ids: String.t(),
          ssn: String.t()
        ]

  @type options :: keyword()

  @doc """
  Performs a property search using the Property Search V2 API.

  Makes a POST request to the Endato Property Search V2 API to find properties
  and associated owner information based on the provided search criteria.

  ## Parameters

  - `search_params` - A keyword list of search parameters. Required parameters:
    - `:address_line1` - Address line 1 (house number, street, state, unit/suite/apt number)
    - `:address_line2` - Address line 2 (city, state, zip)

    Optional parameters include:
    - `:apn` - Assessor Parcel Number
    - `:business_name` - Business name
    - `:first_name` - First name of property owner
    - `:middle_name` - Middle name or middle initial
    - `:last_name` - Last name of property owner
    - `:tahoe_id` - Tahoe ID (unique person ID)
    - `:poseidon_ids` - Poseidon ID
    - `:ssn` - Social Security Number (format: ###-##-####)
  - `options` - Additional options (default: `[]`)

  ## Examples

      # Search by address
      iex> params = [
      ...>   address_line1: "1140 Oak St",
      ...>   address_line2: "Brandon, MS"
      ...> ]
      iex> {:ok, %Req.Response{status: 200}} = Boring.Api.Endato.property_search(params)

      # Search by owner name
      iex> params = [address_line1: "123 Main St", address_line2: "Springfield, IL", first_name: "John", last_name: "Doe"]
      iex> {:ok, %Req.Response{status: 200}} = Boring.Api.Endato.property_search(params)

  ## Returns

  - `{:ok, %Req.Response{}}` on success
  - `{:error, reason}` on failure
  """
  @spec property_search(property_search_params(), options()) ::
          {:ok, Req.Response.t()} | {:error, any()}
  def property_search(search_params, options \\ []) do
    json_params = prepare_json_params(search_params)

    request(
      "/PropertyV2Search",
      Keyword.merge([method: :post, json: json_params, search_type: "PropertyV2"], options)
    )
  end

  @doc """
  Performs a property search, returning the response directly.

  Similar to `property_search/2` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `search_params` - A map of search parameters
  - `options` - Additional options (default: `[]`)

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  @spec property_search!(property_search_params(), options()) :: Req.Response.t()
  def property_search!(search_params, options \\ []) do
    {:ok, response} = property_search(search_params, options)
    response
  end

  @type contact_enrichment_params :: [
          first_name: String.t(),
          middle_name: String.t(),
          last_name: String.t(),
          dob: String.t(),
          age: integer(),
          address: %{
            address_line1: String.t(),
            address_line2: String.t()
          },
          phone: String.t(),
          email: String.t()
        ]

  @doc """
  Performs contact enrichment using the Endato Contact Enrichment API.

  This function enriches contact information based on the provided search parameters.
  It returns name, age, top 5 phone numbers, emails and addresses for the top person match.
  At least two search criteria are required: Name, Phone, Address, or Email.

  ## Parameters

  - `search_params` - A keyword list of search parameters. At least two are required:
    - `:first_name` - First name (optional)
    - `:middle_name` - Middle name (optional)
    - `:last_name` - Last name (optional)
    - `:dob` - Date of birth (optional)
    - `:age` - Age (optional)
    - `:address` - Address map with `:address_line1` and `:address_line2` (optional)
    - `:phone` - Phone number (optional)
    - `:email` - Email address (optional)
  - `options` - Additional options (default: `[]`)

  ## Examples

      # Search by name and address
      iex> params = [
      ...>   first_name: "John",
      ...>   last_name: "Doe",
      ...>   address: %{address_line1: "123 Main St", address_line2: "Springfield, IL"}
      ...> ]
      iex> {:ok, %Req.Response{status: 200}} = Boring.Api.Endato.contact_enrichment(params)

      # Search by name only
      iex> params = [first_name: "Jane", last_name: "Smith"]
      iex> {:ok, %Req.Response{status: 200}} = Boring.Api.Endato.contact_enrichment(params)

  ## Returns

  - `{:ok, %Req.Response{}}` on success
  - `{:error, reason}` on failure
  """
  @spec contact_enrichment(contact_enrichment_params(), options()) ::
          {:ok, Req.Response.t()} | {:error, any()}
  def contact_enrichment(search_params, options \\ []) do
    json_params = prepare_json_params(search_params)

    request(
      "/Contact/Enrich",
      Keyword.merge(
        [
          method: :post,
          json: json_params,
          search_type: "DevAPIContactEnrich"
        ],
        options
      )
    )
  end

  @doc """
  Performs contact enrichment, returning the response directly.

  Similar to `contact_enrichment/2` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `search_params` - A keyword list of search parameters
  - `options` - Additional options (default: `[]`)

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  @spec contact_enrichment!(contact_enrichment_params(), options()) :: Req.Response.t()
  def contact_enrichment!(search_params, options \\ []) do
    {:ok, response} = contact_enrichment(search_params, options)
    response
  end

  def contact_id(person_id, options \\ []) when is_binary(person_id) do
    request(
      "/Contact/Id",
      Keyword.merge(
        [
          method: :post,
          json: %{"PersonId" => person_id},
          search_type: "DevAPIContactID"
        ],
        options
      )
    )
  end

  def contact_id!(person_id, options \\ []) when is_binary(person_id) do
    {:ok, response} = contact_id(person_id, options)
    response
  end

  @doc """
  Makes a request to the Endato API.

  ## Parameters

  - `url` - The URL path to request
  - `options` - A keyword list of options to pass to Req.request/2. Special options:
    - `:search_type` - The galaxy search type to use (required for API calls)

  ## Returns

  - `{:ok, response}` on success
  - `{:error, reason}` on failure
  """
  def request(url, options \\ []) do
    req_options = prepare_request_options(options)
    adapter = Application.get_env(:boring, :http_client, Req)
    adapter.request(new(url: url), req_options)
  end

  @doc """
  Makes a request to the Endato API, returning the response directly.

  Similar to `request/2` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `url` - The URL path to request
  - `options` - A keyword list of options to pass to Req.request!/2. Special options:
    - `:search_type` - The galaxy search type to use (required for API calls)

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  def request!(url, options \\ []) do
    req_options = prepare_request_options(options)
    adapter = Application.get_env(:boring, :http_client, Req)
    adapter.request!(new(url: url), req_options)
  end

  @doc """
  Creates a new Req request with Endato API configuration.

  Sets up a new Req request with the Endato API base URL, authentication headers,
  and any additional options provided. This function is useful when you need to
  create a custom request that isn't covered by the other functions in this module.

  ## Parameters

  - `options` - A keyword list of options to pass to Req.new/1

  ## Examples

      iex> request = Boring.Api.Endato.new(url: "/PropertyV2Search")
      iex> is_struct(request, Req.Request)
      true
      iex> request.options[:base_url]
      "https://devapi.enformion.com"

  ## Returns

  - A Req request struct configured for the Endato API
  """
  @spec new(keyword()) :: Req.Request.t()
  def new(options) when is_list(options) do
    [api_token: api_token, api_token_name: api_token_name] = PetalPro.config(:endato)

    base_headers = [
      {"galaxy-ap-name", api_token_name},
      {"galaxy-ap-password", api_token},
      {"content-type", "application/json"}
    ]

    [base_url: "https://devapi.enformion.com", headers: base_headers]
    |> Req.new()
    |> Req.merge(options)
  end

  defp prepare_request_options(options) do
    {search_type, req_options} = Keyword.pop(options, :search_type)

    # search_type is required for Endato API calls
    if !search_type do
      raise ArgumentError, "search_type is required for Endato API requests"
    end

    # Add search_type to headers
    headers = Keyword.get(req_options, :headers, [])
    updated_headers = [{"galaxy-search-type", search_type} | headers]
    Keyword.put(req_options, :headers, updated_headers)
  end

  # Convert keyword list to map with camelCase string keys for API requests
  defp prepare_json_params(search_params) when is_list(search_params) do
    search_params
    |> Map.new()
    |> Recase.Enumerable.stringify_keys(&Recase.to_pascal/1)
  end

  defp prepare_json_params(search_params) when is_map(search_params) do
    Recase.Enumerable.stringify_keys(search_params, &Recase.to_pascal/1)
  end
end
