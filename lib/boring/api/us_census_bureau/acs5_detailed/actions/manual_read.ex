defmodule Boring.Api.USCensusBureau.ACS5Detailed.Actions.ManualRead do
  @moduledoc false
  use Ash.Resource.ManualRead

  alias Boring.Api.USCensusBureau.ACS5Detailed
  alias Boring.Api.USCensusBureau.Types.StateId

  require Logger

  @base_url "https://api.census.gov/data/2023/acs/acs5"

  def read(query, _data_layer_query, _opts, _context) do
    state_code = Ash.Query.get_argument(query, :state_code)

    if is_nil(state_code) do
      raise Ash.Error.to_ash_error("state_code is required")
    end

    api_key = PetalPro.config([:demographics, :us_census_api_key])

    if is_nil(api_key) do
      raise Ash.Error.to_ash_error("US_CENSUS_API_KEY is not set. Cannot fetch data from US Census API")
    end

    params = %{
      "get" => field_variables(),
      "for" => "place:*",
      "in" => "state:#{state_code_to_fips(query, state_code)}",
      "key" => api_key,
      "limit" => query.limit,
      "offset" => query.offset
    }

    records =
      case Req.get!(@base_url, params: params) do
        %Req.Response{status: 200, body: [_headers | records]} when is_list(records) ->
          Enum.map(records, &to_acs5_detailed/1)

        _ ->
          []
      end

    {:ok, records}
  end

  defp state_code_to_fips(query, state_code) do
    state_code
    |> String.to_existing_atom()
    |> StateId.description()
  rescue
    _ ->
      Ash.Query.add_error(query,
        field: :state_code,
        message: "must be one of #{inspect(StateId.values())}"
      )
  end

  @field_variables [
    geo_id: "GEO_ID",
    name: "NAME",
    population_estimate: "B01001_001E",
    median_household_income_estimate: "B19013_001E"
  ]

  defp field_variables do
    @field_variables
    |> Keyword.values()
    |> Enum.join(",")
  end

  defp to_acs5_detailed(record) do
    [geo_id, name, pop, income | _] = record

    {place_name, state_name} = get_city_and_state(name)

    median_household_income_estimate = Money.parse(income)

    median_household_income_estimate =
      if Money.negative?(median_household_income_estimate) do
        Money.new!(:USD, 0)
      else
        median_household_income_estimate
      end

    %ACS5Detailed{
      id: geo_id,
      place_name: place_name,
      state_name: state_name,
      population_estimate: String.to_integer(pop),
      median_household_income_estimate: median_household_income_estimate
    }
  end

  defp get_city_and_state(name) do
    case String.split(name, ", ") do
      [raw_place_name, state_name] ->
        {place_name(raw_place_name), state_name}

      [raw_place_name_1, raw_place_name_2, state_name] ->
        {place_name("#{raw_place_name_1}, #{raw_place_name_2}"), state_name}
    end
  end

  @place_types [
    "city",
    "town",
    "village",
    "borough",
    "CDP",
    "municipality",
    "urban county",
    "metro township",
    "plantation",
    "reservation",
    "community",
    "government"
  ]

  defp place_name(raw_place_name) do
    place_types_regex = Enum.join(@place_types, "|")
    regex = ~r/\s+(#{place_types_regex})$/

    String.replace(raw_place_name, regex, "")
  end
end
