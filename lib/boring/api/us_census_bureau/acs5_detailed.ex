defmodule Boring.Api.USCensusBureau.ACS5Detailed do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    domain: Boring.Api.USCensusBureau,
    authorizers: [Ash.Policy.Authorizer],
    primary_read_warning?: false

  actions do
    read :read do
      primary? true

      pagination do
        required? true
        offset? true
        default_limit 250
        max_page_size 50_000
        countable true
      end

      argument :state_code, :string, allow_nil?: false

      manual __MODULE__.Actions.ManualRead
    end
  end

  policies do
    bypass AshOban.Checks.AshObanInteraction do
      authorize_if always()
    end

    bypass actor_attribute_equals(:role, :admin) do
      authorize_if always()
    end

    policy always() do
      forbid_if always()
    end
  end

  attributes do
    attribute :id, :string, primary_key?: true, allow_nil?: false
    attribute :place_name, :string, allow_nil?: false
    attribute :state_name, :string, allow_nil?: false
    attribute :population_estimate, :integer, allow_nil?: false
    attribute :median_household_income_estimate, :money, allow_nil?: false
  end
end
