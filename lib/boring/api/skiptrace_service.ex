defmodule Boring.Api.SkiptraceService do
  @moduledoc """
  Service for performing skip trace operations on addresses.

  This service provides functionality to find contact information (name, email, phone number)
  associated with a given address by delegating to configured skip trace providers.

  The service tries each configured provider in sequence until one returns valid results.
  If no provider returns results, an empty list is returned.

  ## Configuration

  Providers are configured in the application config:

  ```elixir
  config :boring,
    skiptrace: [providers: [Boring.Api.SkiptraceService.Providers.ApifySorowerSkiptrace]]
  ```
  """

  alias Boring.Api.SkiptraceService.Result
  alias Boring.Services.ServiceRequest

  @doc """
  Performs a skip trace operation for the given service request.

  Tries each configured provider in sequence until one returns valid results.
  If no provider returns valid results, returns an empty list.

  ## Parameters

    * `service_request` - A `ServiceRequest` struct containing the opportunity and address to skip trace

  ## Returns

    * `{:ok, results}` - A list of `Boring.Api.SkiptraceService.Result` structs containing
      the contact information found for the address
    * `{:ok, []}` - If no contact information was found for the address or all providers failed

  ## Examples

  Due to the complexity of setting up ServiceRequest structs with proper Ash resources,
  see the test file for working examples. The function expects a ServiceRequest struct
  containing an opportunity with address fields (name, street, city, state, postal_code).

      # Basic usage pattern:
      # service_request = %ServiceRequest{opportunity: opportunity}
      # {:ok, results} = SkiptraceService.run_skiptrace(service_request)
  """
  @spec run_skiptrace(service_request :: ServiceRequest.t()) ::
          {:ok, list(Result.t())}
          | {:error, any()}
  def run_skiptrace(%ServiceRequest{} = service_request) do
    Enum.reduce_while(providers(), {:ok, %Result{contacts: [], raw_result: nil}}, fn provider, _acc ->
      case provider.run_skiptrace(service_request) do
        {:ok, %Result{contacts: contacts} = result} when contacts != [] -> {:halt, {:ok, result}}
        _ -> {:cont, {:ok, %Result{contacts: [], raw_result: nil}}}
      end
    end)
  end

  @doc false
  defp providers do
    PetalPro.config([:skiptrace, :providers])
  end
end
