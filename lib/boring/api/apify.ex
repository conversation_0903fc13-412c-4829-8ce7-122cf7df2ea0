defmodule Boring.Api.Apify do
  @moduledoc """
  Client for interacting with the Apify API.

  This module provides a simple interface for working with Apify's API, allowing
  you to manage actor runs, tasks, and datasets. It handles authentication and
  request formatting automatically.

  The module uses the Req HTTP client library to make requests to the Apify API.
  All functions come in two variants:
  - Regular functions that return `{:ok, %Req.Response{}}` or `{:error, reason}`
  - Bang (!) functions that return the response directly or raise an exception

  ## Examples

      # List all actor runs
      {:ok, %Req.Response{status: 200, body: body}} = Boring.Api.Apify.list_runs()

      # Run a task asynchronously
      {:ok, %Req.Response{status: 200, body: body}} = Boring.Api.Apify.run_task_async("your_task_id", %{key: "value"})

      # Get dataset items from a run
      {:ok, %Req.Response{status: 200, body: items}} = Boring.Api.Apify.get_run_dataset("run_id")
  """

  @type run_id :: String.t()
  @type task_id :: String.t()
  @type input :: map()
  @type options :: keyword()

  @doc """
  Lists all actor runs.

  Makes a GET request to the Apify API to retrieve all actor runs.

  ## Examples

      iex> {:ok, %Req.Response{}} = Boring.Api.Apify.list_runs()

  ## Returns

  - `{:ok, %Req.Response{}}` on success
  - `{:error, reason}` on failure
  """
  @spec list_runs() :: {:ok, Req.Response.t()} | {:error, any()}
  def list_runs do
    request("/actor-runs")
  end

  @doc """
  Lists all actor runs, returning the response directly.

  Similar to `list_runs/0` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Examples

      iex> %Req.Response{} = Boring.Api.Apify.list_runs!()

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  @spec list_runs!() :: Req.Response.t()
  def list_runs! do
    {:ok, response} = list_runs()
    response
  end

  @doc """
  Runs a task asynchronously.

  Makes a POST request to the Apify API to run a task with the given ID and input.

  ## Parameters

  - `task_id` - The ID of the task to run
  - `input` - A map of input parameters for the task (default: `%{}`)

  ## Examples

      iex> {:ok, %Req.Response{}} = Boring.Api.Apify.run_task_async("my_task_id", %{query: "search term"})

  ## Returns

  - `{:ok, %Req.Response{}}` on success
  - `{:error, reason}` on failure
  """
  @spec run_task_async(task_id(), input()) :: {:ok, Req.Response.t()} | {:error, any()}
  def run_task_async(task_id, input \\ %{}) do
    request("/actor-tasks/:task_id/runs", json: input, path_params: [task_id: task_id])
  end

  @doc """
  Runs a task asynchronously, returning the response directly.

  Similar to `run_task_async/2` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `task_id` - The ID of the task to run
  - `input` - A map of input parameters for the task (default: `%{}`)

  ## Examples

      iex> %Req.Response{} = Boring.Api.Apify.run_task_async!("my_task_id", %{query: "search term"})

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  @spec run_task_async!(task_id(), input()) :: Req.Response.t()
  def run_task_async!(task_id, input \\ %{}) do
    {:ok, response} = run_task_async(task_id, input)
    response
  end

  @doc """
  Runs a task synchronously and returns dataset items.

  Makes a POST request to the Apify API to run a task with the given ID and input,
  waiting for it to complete and returning the dataset items.

  ## Parameters

  - `task_id` - The ID of the task to run
  - `input` - A map of input parameters for the task (default: `%{}`)

  ## Examples

      iex> {:ok, %Req.Response{}} = Boring.Api.Apify.run_task_sync("my_task_id", %{query: "search term"})

  ## Returns

  - `{:ok, %Req.Response{}}` on success
  - `{:error, reason}` on failure
  """
  @spec run_task_sync(task_id(), input()) :: {:ok, Req.Response.t()} | {:error, any()}
  def run_task_sync(task_id, input \\ %{}) do
    request("/actor-tasks/:task_id/run-sync-get-dataset-items",
      json: input,
      path_params: [task_id: task_id]
    )
  end

  @doc """
  Runs a task synchronously and returns dataset items, returning the response directly.

  Similar to `run_task_sync/2` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `task_id` - The ID of the task to run
  - `input` - A map of input parameters for the task (default: `%{}`)

  ## Examples

      iex> %Req.Response{} = Boring.Api.Apify.run_task_sync!("my_task_id", %{query: "search term"})

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  @spec run_task_sync!(task_id(), input()) :: Req.Response.t()
  def run_task_sync!(task_id, input \\ %{}) do
    {:ok, response} = run_task_sync(task_id, input)
    response
  end

  @doc """
  Resurrects a previously finished actor run.

  Makes a POST request to the Apify API to resurrect a run with the given ID.

  ## Parameters

  - `run_id` - The ID of the run to resurrect

  ## Examples

      iex> {:ok, %Req.Response{}} = Boring.Api.Apify.resurrect_run("my_run_id")

  ## Returns

  - `{:ok, %Req.Response{}}` on success
  - `{:error, reason}` on failure
  """
  @spec resurrect_run(run_id()) :: {:ok, Req.Response.t()} | {:error, any()}
  def resurrect_run(run_id) do
    request("/actor-runs/:run_id/resurrect", path_params: [run_id: run_id])
  end

  @doc """
  Resurrects a previously finished actor run, returning the response directly.

  Similar to `resurrect_run/1` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `run_id` - The ID of the run to resurrect

  ## Examples

      iex> %Req.Response{} = Boring.Api.Apify.resurrect_run!("my_run_id")

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  @spec resurrect_run!(run_id()) :: Req.Response.t()
  def resurrect_run!(run_id) do
    {:ok, response} = resurrect_run(run_id)
    response
  end

  @doc """
  Aborts a running actor run.

  Makes a POST request to the Apify API to abort a run with the given ID.
  By default, the run is aborted gracefully.

  ## Parameters

  - `run_id` - The ID of the run to abort

  ## Examples

      iex> {:ok, %Req.Response{}} = Boring.Api.Apify.abort_run("my_run_id")

  ## Returns

  - `{:ok, %Req.Response{}}` on success
  - `{:error, reason}` on failure
  """
  @spec abort_run(run_id()) :: {:ok, Req.Response.t()} | {:error, any()}
  def abort_run(run_id) do
    request("/actor-runs/:run_id/abort",
      path_params: [run_id: run_id],
      params: [gracefully: true]
    )
  end

  @doc """
  Aborts a running actor run, returning the response directly.

  Similar to `abort_run/1` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `run_id` - The ID of the run to abort

  ## Examples

      iex> %Req.Response{} = Boring.Api.Apify.abort_run!("my_run_id")

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  @spec abort_run!(run_id()) :: Req.Response.t()
  def abort_run!(run_id) do
    {:ok, response} = abort_run(run_id)
    response
  end

  @doc """
  Gets dataset items from an actor run.

  Makes a GET request to the Apify API to retrieve dataset items from a run with the given ID.

  ## Parameters

  - `run_id` - The ID of the run to get dataset items from
  - `options` - A keyword list of options to pass to the API (default: `[]`)

  ## Examples

      iex> {:ok, %Req.Response{}} = Boring.Api.Apify.get_run_dataset("my_run_id")

      iex> {:ok, %Req.Response{}} = Boring.Api.Apify.get_run_dataset("my_run_id", limit: 10, offset: 20)

  ## Returns

  - `{:ok, %Req.Response{}}` on success
  - `{:error, reason}` on failure
  """
  @spec get_run_dataset(run_id(), options()) :: {:ok, Req.Response.t()} | {:error, any()}
  def get_run_dataset(run_id, options \\ []) do
    request("/actor-runs/:run_id/dataset/items", path_params: [run_id: run_id], params: options)
  end

  @doc """
  Gets dataset items from an actor run, returning the response directly.

  Similar to `get_run_dataset/2` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `run_id` - The ID of the run to get dataset items from
  - `options` - A keyword list of options to pass to the API (default: `[]`)

  ## Examples

      iex> %Req.Response{} = Boring.Api.Apify.get_run_dataset!("my_run_id")

      iex> %Req.Response{} = Boring.Api.Apify.get_run_dataset!("my_run_id", limit: 10, offset: 20)

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  @spec get_run_dataset!(run_id(), options()) :: Req.Response.t()
  def get_run_dataset!(run_id, options \\ []) do
    {:ok, response} = get_run_dataset(run_id, options)
    response
  end

  @doc """
  Makes a request to the Apify API.

  ## Parameters

  - `url` - The URL path to request
  - `options` - A keyword list of options to pass to Req.request/2

  ## Returns

  - `{:ok, response}` on success
  - `{:error, reason}` on failure
  """
  def request(url, options \\ []) do
    adapter = Application.get_env(:boring, :http_client, Req)
    adapter.request(new(url: url), options)
  end

  @doc """
  Makes a request to the Apify API, returning the response directly.

  Similar to `request/2` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `url` - The URL path to request
  - `options` - A keyword list of options to pass to Req.request!/2

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  def request!(url, options \\ []) do
    adapter = Application.get_env(:boring, :http_client, Req)
    adapter.request!(new(url: url), options)
  end

  @doc """
  Creates a new Req request with Apify API configuration.

  Sets up a new Req request with the Apify API base URL, authentication token,
  and any additional options provided. This function is useful when you need to
  create a custom request that isn't covered by the other functions in this module.

  ## Parameters

  - `options` - A keyword list of options to pass to Req.new/1

  ## Examples

      iex> request = Boring.Api.Apify.new(url: "/actor-runs")
      iex> is_struct(request, Req.Request)
      true
      iex> request.options[:base_url]
      "https://api.apify.com/v2"

  ## Returns

  - A Req request struct configured for the Apify API
  """
  @spec new(keyword()) :: Req.Request.t()
  def new(options) when is_list(options) do
    [base_url: "https://api.apify.com/v2", auth: {:bearer, PetalPro.config([:apify, :api_token])}]
    |> Req.new()
    |> Req.Request.append_request_steps(
      post: fn req ->
        with %{method: :get, body: <<_::binary>>} <- req do
          %{req | method: :post}
        end
      end
    )
    |> Req.merge(options)
  end
end
