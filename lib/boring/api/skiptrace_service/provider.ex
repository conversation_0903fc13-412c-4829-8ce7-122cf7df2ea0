defmodule Boring.Api.SkiptraceService.Provider do
  @moduledoc """
  Behaviour for skip trace providers.

  This module defines the behaviour that all skip trace providers must implement.
  Skip trace providers are responsible for taking an address and returning contact
  information (name, email, phone number) associated with that address.

  Providers are configured in the application config:

  ```elixir
  config :boring,
    skiptrace: [providers: [Boring.Api.SkiptraceService.Providers.ApifySorowerSkiptrace]]
  ```

  ## Implementing a Provider

  To implement a provider, create a module that implements the `run_skiptrace/1` callback:

  ```elixir
  defmodule MyApp.SkiptraceService.Providers.MyProvider do
    @behaviour Boring.Api.SkiptraceService.Provider

    @impl true
    def run_skiptrace(address) do
      # Implementation that returns {:ok, results} or {:error, reason}
    end
  end
  ```
  """

  alias Boring.Api.SkiptraceService.Result
  alias Boring.Services.ServiceRequest

  @doc """
  Performs a skip trace operation on the given address.

  ## Parameters

    * `address` - A string containing the address to skip trace

  ## Returns

    * `{:ok, results}` - A list of `Boring.Api.SkiptraceService.Result` structs containing
      the contact information found for the address. The list may be empty if no results were found.
    * `{:error, reason}` - If an error occurred during the skip trace operation

  Note: Providers should return `{:ok, []}` when no results are found but the operation was successful.
  Only return `{:error, reason}` when the operation itself failed.
  """
  @callback run_skiptrace(service_request :: ServiceRequest.t()) ::
              {:ok, Result.t()}
              | {:error, any()}
end
