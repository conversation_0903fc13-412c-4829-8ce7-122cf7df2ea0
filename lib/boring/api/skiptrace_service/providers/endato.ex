defmodule Boring.Api.SkiptraceService.Providers.Endato do
  @moduledoc """
  Endato provider for the SkiptraceService.

  This provider uses a two-step approach to find comprehensive contact information
  associated with a given address:

  1. **Property Search V2 API** - Finds property ownership information by address
  2. **Contact Enrichment API** - Enriches each owner with detailed contact information

  ## How it works

  1. The provider parses the address string into components (street, city/state)
  2. Sends the address to the Endato Property Search V2 API to get property owners
  3. For each individual owner (filtering out businesses), calls the Contact Enrichment API
  4. Combines the enriched contact data into `Boring.Api.SkiptraceService.Result` structs

  ## Data Sources

  The provider returns contact information from two APIs:
  - **Property ownership data** from Property Search V2 (names, business filtering)
  - **Contact details** from Contact Enrichment (phones, emails, addresses, age)

  ## Filtering

  The provider filters out results where:
  - The owner is marked as a corporation/business (`isCorporationOrBusiness: true`)
  - The owner name is missing or invalid
  - Contact enrichment fails (returns empty results for that owner)

  ## Address Handling

  The provider uses enriched address data from the Contact Enrichment API rather than
  the original input address, providing more accurate and up-to-date address information.

  ## Configuration

  Requires Endato API credentials to be configured:

      config :boring, :endato,
        ap_name: "your_ap_name",
        ap_password: "your_ap_password"

  ## Examples

      # Basic skip trace
      iex> {:ok, results} = Boring.Api.SkiptraceService.Providers.Endato.run_skiptrace("123 Main St, Springfield, IL")
      iex> length(results) >= 0
      true

      # Results contain structured contact information
      iex> {:ok, [result | _]} = Boring.Api.SkiptraceService.Providers.Endato.run_skiptrace("123 Main St, Springfield, IL")
      iex> %Boring.Api.SkiptraceService.Result{} = result
      iex> is_binary(result.name)
      true
  """
  @behaviour Boring.Api.SkiptraceService.Provider

  alias Boring.Api.Endato
  alias Boring.Api.SkiptraceService.Provider
  alias Boring.Api.SkiptraceService.Result
  alias Boring.Services.ServiceRequest

  require Logger

  @typedoc """
  Owner information extracted from Property Search V2 response.
  """
  @type owner_info :: %{
          tahoe_id: binary(),
          first_name: String.t(),
          middle_name: String.t() | nil,
          last_name: String.t(),
          full_name: String.t(),
          is_current_owner: boolean(),
          is_corporation_or_business: boolean()
        }

  @typedoc """
  Search parameters for Endato Property Search V2 API.
  """
  @type property_search_params :: [
          business_name: String.t(),
          address_line1: String.t(),
          address_line2: String.t()
        ]

  @typedoc """
  Search parameters for Endato Contact Enrichment API.
  """
  @type contact_enrichment_params :: [
          first_name: String.t(),
          last_name: String.t(),
          middle_name: String.t(),
          address: %{
            address_line1: String.t(),
            address_line2: String.t()
          }
        ]

  @doc """
  Performs a skip trace operation on the given address using Endato's two-step enrichment process.

  This function implements the main skip tracing logic by:
  1. Parsing the input address into components
  2. Searching for property owners using Property Search V2 API
  3. Enriching each individual owner with contact details using Contact Enrichment API
  4. Returning structured results with comprehensive contact information

  ## Parameters

    * `address` - A string containing the address to skip trace. Supported formats:
      - "123 Main St, Springfield, IL" (comma-separated)
      - "123 Main St Springfield IL" (space-separated, requires at least 3 parts)

  ## Returns

    * `{:ok, results}` - A list of `Boring.Api.SkiptraceService.Result` structs containing
      the contact information found for the address. Returns an empty list if no valid results were found.
      Each result includes:
      - `name` - Full name of the person
      - `emails` - List of email addresses (ordered by validation status and business type)
      - `phone_numbers` - List of phone numbers (ordered by connection status)
      - `age` - Age as integer (if available)
      - `address` - Structured address from enrichment data

  ## Error Handling

  The function gracefully handles various error conditions and always returns `{:ok, []}` for:
  - Invalid or unparseable addresses
  - API errors or timeouts
  - Non-200 HTTP responses
  - Contact enrichment failures

  Errors are logged but do not cause the function to crash.

  ## Examples

      # Basic usage with comma-separated address
      iex> {:ok, results} = Boring.Api.SkiptraceService.Providers.Endato.run_skiptrace("123 Main St, Springfield, IL")
      iex> is_list(results)
      true

      # Space-separated address format
      iex> {:ok, results} = Boring.Api.SkiptraceService.Providers.Endato.run_skiptrace("456 Oak Ave Madison WI")
      iex> is_list(results)
      true

      # Invalid address returns empty list
      iex> {:ok, results} = Boring.Api.SkiptraceService.Providers.Endato.run_skiptrace("Invalid")
      iex> results
      []
  """
  @impl Provider
  @spec run_skiptrace(service_request :: ServiceRequest.t()) :: {:ok, Result.t()}
  def run_skiptrace(%ServiceRequest{opportunity: opportunity}) do
    search_params = [
      address_line1: opportunity.street,
      address_line2: "#{opportunity.city}, #{opportunity.state.code} #{opportunity.postal_code}"
    ]

    do_run_skiptrace(search_params)
  end

  @spec do_run_skiptrace(
          search_params :: property_search_params(),
          retry_count :: non_neg_integer()
        ) :: {:ok, Result.t()}
  defp do_run_skiptrace(search_params, retry_count \\ 0)

  defp do_run_skiptrace(_search_params, retry_count) when retry_count > 1,
    do: {:ok, %Result{contacts: [], raw_result: nil}}

  defp do_run_skiptrace(search_params, retry_count) when is_list(search_params) do
    case Endato.property_search(search_params) do
      {:ok, %{body: %{"propertyV2Records" => []} = body}} ->
        if Keyword.has_key?(search_params, :business_name) do
          search_params = Keyword.delete(search_params, :business_name)
          do_run_skiptrace(search_params, retry_count + 1)
        else
          {:ok, %Result{contacts: [], raw_result: body}}
        end

      {:ok, %{body: %{"propertyV2Records" => property_records} = body}} ->
        owners =
          property_records
          |> Stream.flat_map(&extract_owners_from_single_property/1)
          |> Stream.reject(&is_nil/1)
          |> Enum.to_list()
          |> Stream.map(&enrich_owner_contact(&1))
          |> Enum.to_list()

        {:ok, %Result{contacts: owners, raw_result: body}}

      {:ok, %{status: status, body: body}} when status not in 200..299 ->
        Logger.error("Endato Skiptrace: Issue with Provider API")

        {:ok, %Result{contacts: [], raw_result: body}}

      {_, %{body: body}} ->
        {:ok, %Result{contacts: [], raw_result: body}}

      _ ->
        {:ok, %Result{contacts: [], raw_result: nil}}
    end
  end

  # Extract owners from a single property result
  defp extract_owners_from_single_property(%{"property" => %{"summary" => summary}}) do
    current_owners = Map.get(summary, "currentOwners", [])
    mailing_addresses = get_in(summary, ["currentOwnerMetaData", "mailingAddresses"]) || []

    # Get the first mailing address if available
    mailing_address = List.first(mailing_addresses)

    current_owners
    |> Stream.map(&extract_owner_name_parts(&1, mailing_address))
    |> Stream.reject(&is_nil/1)
    |> Stream.map(&Map.put(&1, :is_current_owner, true))
    |> Enum.to_list()
  end

  defp extract_owners_from_single_property(_), do: []

  defp extract_owner_name_parts(
         %{"name" => name_parts, "isCorporationOrBusiness" => is_corp_or_business},
         mailing_address
       ) do
    parsed_name_parts =
      name_parts
      |> Enum.reject(fn {_key, value} -> is_nil(value) || byte_size(value) == 0 end)
      |> Map.new()
      |> Map.take([
        "companyName",
        "firstName",
        "middleName",
        "lastName",
        "fullName",
        "tahoeId"
      ])

    if Enum.empty?(parsed_name_parts) do
      nil
    else
      tahoe_id = Map.get(parsed_name_parts, "tahoeId")
      full_name = Map.get(parsed_name_parts, "fullName")
      is_business? = is_corp_or_business

      full_name =
        if is_business? do
          Map.get(parsed_name_parts, "companyName") || full_name
        else
          full_name
        end

      %{
        tahoe_id: tahoe_id,
        full_name: full_name,
        first_name: Map.get(parsed_name_parts, "firstName"),
        last_name: Map.get(parsed_name_parts, "lastName"),
        middle_name: Map.get(parsed_name_parts, "middleName"),
        is_current_owner: false,
        is_corporation_or_business: is_business?,
        mailing_address: mailing_address
      }
    end
  end

  defp extract_owner_name_parts(%{"name" => name_parts}, mailing_address) do
    # Fallback for owners without isCorporationOrBusiness field
    # Default to checking if tahoeId is nil (likely a business)
    parsed_name_parts =
      name_parts
      |> Enum.reject(fn {_key, value} -> is_nil(value) || byte_size(value) == 0 end)
      |> Map.new()
      |> Map.take([
        "companyName",
        "firstName",
        "middleName",
        "lastName",
        "fullName",
        "tahoeId"
      ])

    if Enum.empty?(parsed_name_parts) do
      nil
    else
      tahoe_id = Map.get(parsed_name_parts, "tahoeId")
      full_name = Map.get(parsed_name_parts, "fullName")
      is_business? = is_nil(tahoe_id)

      full_name =
        if is_business? do
          Map.get(parsed_name_parts, "companyName") || full_name
        else
          full_name
        end

      %{
        tahoe_id: tahoe_id,
        full_name: full_name,
        first_name: Map.get(parsed_name_parts, "firstName"),
        last_name: Map.get(parsed_name_parts, "lastName"),
        middle_name: Map.get(parsed_name_parts, "middleName"),
        is_current_owner: false,
        is_corporation_or_business: is_business?,
        mailing_address: mailing_address
      }
    end
  end

  defp extract_owner_name_parts(_, _), do: nil

  @spec enrich_owner_contact(owner_info()) :: Result.t() | nil
  defp enrich_owner_contact(%{tahoe_id: person_id} = owner) when not is_nil(person_id) do
    case Endato.contact_id(person_id) do
      {:ok, %{status: 200, body: %{"person" => person_data}}} ->
        to_skiptrace_result(owner, person_data)

      {:ok, %{status: status}} ->
        Logger.debug("Endato Contact ID HTTP #{status} for #{owner.full_name}")
        to_skiptrace_result(owner)

      {:error, reason} ->
        Logger.debug("Endato Contact ID error for #{owner.full_name}: #{inspect(reason)}")
        to_skiptrace_result(owner)
    end
  end

  defp enrich_owner_contact(owner) do
    to_skiptrace_result(owner)
  end

  # Convert enriched owner data to SkiptraceService.Result struct
  defp to_skiptrace_result(owner, enrichment_data \\ %{}) do
    # Use the enriched name if available, otherwise fall back to property owner name
    full_name = extract_name_from_enrichment(owner, enrichment_data)
    emails = extract_emails_from_enrichment(enrichment_data)
    phone_numbers = extract_phone_numbers_from_enrichment(enrichment_data)
    age = Map.get(enrichment_data, "age")

    # Try to get address from enrichment data first, fall back to mailing address
    address =
      extract_address_from_enrichment(enrichment_data) ||
        extract_mailing_address(owner.mailing_address)

    %Result.Contact{
      name: full_name,
      emails: emails,
      phone_numbers: phone_numbers,
      age: age,
      address: address,
      is_current_owner?: owner.is_current_owner,
      is_corporation_or_business?: owner.is_corporation_or_business
    }
  end

  # Extract name from Contact Id response
  defp extract_name_from_enrichment(owner, %{"name" => name_parts}) do
    first_name = Map.get(name_parts, "firstName")
    middle_name = Map.get(name_parts, "middleName")
    last_name = Map.get(name_parts, "lastName")

    full_name_parts =
      Enum.reject([first_name, middle_name, last_name], &(is_nil(&1) || &1 == ""))

    if Enum.empty?(full_name_parts) do
      owner.full_name
    else
      Enum.join(full_name_parts, " ")
    end
  end

  defp extract_name_from_enrichment(owner, _enrichment_data), do: owner.full_name

  # Extract emails from Contact Enrichment response (assumes API returns them in order of relevance)
  defp extract_emails_from_enrichment(enrichment_data) do
    case enrichment_data["emails"] do
      email_list when is_list(email_list) ->
        email_list
        |> Stream.filter(&(&1["isValidated"] == true))
        |> Stream.map(& &1["email"])
        |> Enum.to_list()

      _ ->
        []
    end
  end

  # Extract phone numbers from Contact Enrichment response (assumes API returns them in order of relevance)
  defp extract_phone_numbers_from_enrichment(enrichment_data) do
    case enrichment_data["phones"] do
      phone_list when is_list(phone_list) ->
        phone_list
        |> Stream.filter(&(&1["isConnected"] == true))
        |> Stream.map(& &1["number"])
        |> Enum.to_list()

      _ ->
        []
    end
  end

  # Extract address from Contact Enrichment response (uses first/most recent address)
  defp extract_address_from_enrichment(enrichment_data) do
    case enrichment_data["addresses"] do
      address_list when is_list(address_list) and length(address_list) > 0 ->
        # Use the first address (assuming API returns them in order of relevance/recency)
        address_data = List.first(address_list)

        street = build_street_from_address(address_data)

        address_parts = %{
          street: street,
          city: address_data["city"],
          state: address_data["state"],
          postal_code: address_data["zip"]
        }

        if address_valid?(address_parts) do
          address_parts
        end

        if address_valid?(address_parts) do
          address_parts
        end

      _ ->
        nil
    end
  end

  # Build street address from enrichment address object
  defp build_street_from_address(address_data) do
    street = address_data["street"] || ""
    unit = address_data["unit"] || ""

    if unit == "" do
      street
    else
      String.trim("#{street} #{unit}")
    end
  end

  defp address_valid?(address_parts) do
    not Enum.any?(address_parts, fn {_, value} ->
      is_nil(value) or value == ""
    end)
  end

  # Extract mailing address from currentOwnerMetaData
  defp extract_mailing_address(nil), do: nil

  defp extract_mailing_address(mailing_address) when is_map(mailing_address) do
    address_parts = %{
      street: mailing_address["addressLine1"],
      city: mailing_address["city"],
      state: mailing_address["state"],
      postal_code: mailing_address["zipCode"]
    }

    if address_valid?(address_parts) do
      address_parts
    end
  end

  defp extract_mailing_address(_), do: nil
end
