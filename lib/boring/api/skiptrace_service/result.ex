defmodule Boring.Api.SkiptraceService.Result do
  @moduledoc false

  @enforce_keys [:contacts, :raw_result]
  defstruct [
    :contacts,
    :raw_result
  ]

  @typedoc """
  Type specification for a skip trace result.

  * `:contacts` - List of `Contact` structs containing the contact information found for the address
  * `:raw_result` - Raw result data from the skip trace provider (for debugging and auditing purposes)
  """
  @type t :: %__MODULE__{
          contacts: list(Contact.t()),
          raw_result: map()
        }

  defmodule Contact do
    @moduledoc false
    @enforce_keys [:name]
    defstruct [
      :name,
      :emails,
      :phone_numbers,
      :age,
      :address,
      :is_current_owner?,
      :is_corporation_or_business?
    ]

    @type address() :: %{
            street: String.t(),
            city: String.t(),
            postal_code: String.t(),
            state: String.t()
          }

    @typedoc """
    Type specification for a skip trace result.

    * `:name` - String containing the person's name
    * `:emails` - String containing the person's email address
    * `:phone_numbers` - String containing the person's phone number
    * `:age` - Integer containing the person's age
    * `:address` - Map containing the person's address
    """
    @type t :: %__MODULE__{
            name: String.t(),
            emails: list(String.t()),
            phone_numbers: list(String.t()),
            age: integer() | nil,
            address: address(),
            is_current_owner?: boolean(),
            is_corporation_or_business?: boolean()
          }
  end
end
