defmodule Boring.Api.Google.Places do
  @moduledoc """
  Client for interacting with the Google Places API (New).

  This module provides a simple interface for working with Google's new Places API,
  including text search functionality. It uses the shared base module for consistent
  request handling across all Google APIs.

  All functions come in two variants:
  - Regular functions that return `{:ok, %Req.Response{}}` or `{:error, reason}`
  - Bang (!) functions that return the response directly or raise an exception

  ## Examples

      # Search for places using text
      {:ok, %Req.Response{status: 200, body: body}} = Boring.Api.Google.Places.text_search("restaurants in NYC")

      # Search with custom options
      {:ok, %Req.Response{}} = Boring.Api.Google.Places.text_search("hotels", max_result_count: 10)
  """

  alias Boring.Api.Google.HttpClient

  # Response structures
  defmodule TextSearchResponse do
    @moduledoc """
    Response structure for Places API Text Search endpoint.
    """

    @type t :: %__MODULE__{
            places: [Place.t()],
            next_page_token: String.t() | nil
          }

    defstruct [:places, :next_page_token]
  end

  defmodule Place do
    @moduledoc """
    Structure representing a place from the Places API.
    """

    @type t :: %__MODULE__{
            id: String.t() | nil,
            display_name: DisplayName.t() | nil,
            formatted_address: String.t() | nil,
            location: Location.t() | nil,
            rating: float() | nil,
            user_rating_count: integer() | nil,
            price_level: String.t() | nil,
            business_status: String.t() | nil,
            types: [String.t()] | nil,
            primary_type: String.t() | nil,
            primary_type_display_name: DisplayName.t() | nil,
            opening_hours: OpeningHours.t() | nil,
            photos: [Photo.t()] | nil,
            plus_code: PlusCode.t() | nil,
            reviews: [Review.t()] | nil,
            website_uri: String.t() | nil,
            google_maps_uri: String.t() | nil,
            international_phone_number: String.t() | nil,
            national_phone_number: String.t() | nil,
            address_components: [AddressComponent.t()] | nil,
            viewport: Viewport.t() | nil,
            # Parsed address fields for convenience
            city: String.t() | nil,
            state: String.t() | nil,
            postal_code: String.t() | nil,
            country: String.t() | nil,
            street: String.t() | nil
          }

    defstruct [
      :id,
      :display_name,
      :formatted_address,
      :location,
      :rating,
      :user_rating_count,
      :price_level,
      :business_status,
      :types,
      :primary_type,
      :primary_type_display_name,
      :opening_hours,
      :photos,
      :plus_code,
      :reviews,
      :website_uri,
      :google_maps_uri,
      :international_phone_number,
      :national_phone_number,
      :address_components,
      :viewport,
      :city,
      :state,
      :postal_code,
      :country,
      :street
    ]
  end

  defmodule DisplayName do
    @moduledoc """
    Display name with language code.
    """

    @type t :: %__MODULE__{
            text: String.t() | nil,
            language_code: String.t() | nil
          }

    defstruct [:text, :language_code]
  end

  defmodule Location do
    @moduledoc """
    Geographic coordinates.
    """

    @type t :: %__MODULE__{
            latitude: float() | nil,
            longitude: float() | nil
          }

    defstruct [:latitude, :longitude]
  end

  defmodule OpeningHours do
    @moduledoc """
    Opening hours information.
    """
    @derive Jason.Encoder

    @type t :: %__MODULE__{
            open_now: boolean() | nil,
            periods: [Period.t()] | nil,
            weekday_text: [String.t()] | nil
          }

    defstruct [:open_now, :periods, :weekday_text]
  end

  defmodule Period do
    @moduledoc """
    Opening period with open and close times.
    """
    @derive Jason.Encoder

    @type t :: %__MODULE__{
            open: TimePoint.t() | nil,
            close: TimePoint.t() | nil
          }

    defstruct [:open, :close]
  end

  defmodule TimePoint do
    @moduledoc """
    Time point with day and time.
    """
    @derive Jason.Encoder

    @type t :: %__MODULE__{
            day: integer() | nil,
            hour: integer() | nil,
            minute: integer() | nil
          }

    defstruct [:day, :hour, :minute]
  end

  defmodule Photo do
    @moduledoc """
    Photo reference information.
    """
    @derive Jason.Encoder

    @type t :: %__MODULE__{
            name: String.t() | nil,
            width_px: integer() | nil,
            height_px: integer() | nil,
            photo_reference: String.t() | nil
          }

    defstruct [:name, :width_px, :height_px, :photo_reference]
  end

  defmodule PlusCode do
    @moduledoc """
    Plus code location information.
    """
    @derive Jason.Encoder

    @type t :: %__MODULE__{
            global_code: String.t() | nil,
            compound_code: String.t() | nil
          }

    defstruct [:global_code, :compound_code]
  end

  defmodule Review do
    @moduledoc """
    Place review information.
    """
    @derive Jason.Encoder

    @type t :: %__MODULE__{
            name: String.t() | nil,
            relative_publish_time_description: String.t() | nil,
            rating: float() | nil,
            text: String.t() | nil,
            original_text: String.t() | nil,
            author_attribution: AuthorAttribution.t() | nil,
            publish_time: String.t() | nil
          }

    defstruct [
      :name,
      :relative_publish_time_description,
      :rating,
      :text,
      :original_text,
      :author_attribution,
      :publish_time
    ]
  end

  defmodule AuthorAttribution do
    @moduledoc """
    Author attribution for reviews.
    """
    @derive Jason.Encoder

    @type t :: %__MODULE__{
            display_name: String.t() | nil,
            uri: String.t() | nil,
            photo_uri: String.t() | nil
          }

    defstruct [:display_name, :uri, :photo_uri]
  end

  defmodule AddressComponent do
    @moduledoc """
    Address component with types and names.
    """
    @derive Jason.Encoder

    @type t :: %__MODULE__{
            language_code: String.t() | nil,
            long_text: String.t() | nil,
            short_text: String.t() | nil,
            types: [String.t()] | nil
          }

    defstruct [:language_code, :long_text, :short_text, :types]
  end

  defmodule Viewport do
    @moduledoc """
    Viewport bounds for the place.
    """
    @derive Jason.Encoder

    @type t :: %__MODULE__{
            low: Location.t() | nil,
            high: Location.t() | nil
          }

    defstruct [:low, :high]
  end

  @type query :: String.t()
  @type text_search_options :: [
          fields: [String.t()],
          max_result_count: pos_integer(),
          location_bias: map(),
          location_restriction: map(),
          price_levels: [String.t()],
          open_now: boolean(),
          type: String.t(),
          region_code: String.t(),
          rank_preference: String.t(),
          language_code: String.t()
        ]

  @doc """
  Searches for places using the Places API Text Search endpoint.

  Makes a POST request to the Google Places API to search for places based on a text query.
  Uses the new Places API (New) format with proper authentication headers.

  ## Parameters

  - `query` - The text query to search for (e.g., "restaurants in NYC", "coffee shops near me")
  - `options` - Optional parameters for the search (default: `[]`)

  ## Options

  - `:fields` - List of fields to include in the response (default: default field set)
  - `:max_result_count` - Maximum number of results to return (default: `20`)
  - `:location_bias` - Location bias for the search
  - `:location_restriction` - Location restriction for the search
  - `:price_levels` - Filter by price level (list of strings)
  - `:open_now` - Filter by places that are open now (boolean)
  - `:type` - Restrict results to specific place types
  - `:region_code` - Region code for biasing results
  - `:rank_preference` - Ranking preference for results
  - `:language_code` - Language for results

  ## Examples

      iex> {:ok, %Req.Response{}} = Boring.Api.Google.Places.text_search("pizza restaurants")

      iex> {:ok, %Req.Response{}} = Boring.Api.Google.Places.text_search("hotels",
      ...>   max_result_count: 10,
      ...>   fields: ["places.id", "places.displayName", "places.location"]
      ...> )

  ## Returns

  - `{:ok, TextSearchResponse.t()}` on success
  - `{:error, reason}` on failure
  """
  @spec text_search(query(), text_search_options()) ::
          {:ok, TextSearchResponse.t()} | {:error, any()}
  def text_search(query, options \\ []) do
    case HttpClient.request(:places, "/places:searchText",
           method: :post,
           json: build_text_search_body(query, options),
           field_mask: build_field_mask(options)
         ) do
      {:ok, %{status: status, body: body}} when status in 200..299 ->
        parse_text_search_response(body)

      {:ok, %{status: _status, body: body}} ->
        {:error, body}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  Searches for places using the Places API Text Search endpoint, returning the response directly.

  Similar to `text_search/2` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `query` - The text query to search for
  - `options` - Optional parameters for the search (default: `[]`)

  ## Examples

      iex> %Req.Response{} = Boring.Api.Google.Places.text_search!("pizza restaurants")

  ## Returns

  - The parsed response on success
  - Raises an exception on failure
  """
  @spec text_search!(query(), text_search_options()) :: TextSearchResponse.t()
  def text_search!(query, options \\ []) do
    case text_search(query, options) do
      {:ok, response} -> response
      {:error, reason} -> raise "Places API request failed: #{inspect(reason)}"
    end
  end

  # Private helper functions

  defp build_text_search_body(query, options) do
    base_body = %{
      "text_query" => query,
      "max_result_count" => Keyword.get(options, :max_result_count, 20)
    }

    # Merge all options (except :fields which is handled separately)
    # Convert atom keys to strings first, then to camelCase using Recase.Enumerable
    options
    |> Keyword.delete(:fields)
    |> Map.new(fn {k, v} -> {to_string(k), v} end)
    |> Map.merge(base_body)
    |> Recase.Enumerable.stringify_keys()
    |> Recase.Enumerable.convert_keys(&Recase.to_camel/1)
  end

  defp build_field_mask(options) do
    default_fields = [
      "places.id",
      "places.displayName",
      "places.formattedAddress",
      "places.location",
      "places.addressComponents",
      "places.rating",
      "places.userRatingCount",
      "places.types",
      "places.primaryType",
      "places.primaryTypeDisplayName",
      "places.nationalPhoneNumber",
      "places.websiteUri",
      "places.viewport",
      "places.businessStatus"
    ]

    options
    |> Keyword.get(:fields, default_fields)
    |> Enum.join(",")
  end

  # Response parsing functions

  @doc """
  Converts a raw API response map into structured response types.

  ## Parameters

  - `response_body` - The raw response body from the Places API

  ## Returns

  - `{:ok, TextSearchResponse.t()}` on successful parsing
  - `{:error, reason}` on parsing failure
  """
  @spec parse_text_search_response(map()) :: {:ok, TextSearchResponse.t()} | {:error, any()}
  def parse_text_search_response(%{"places" => places} = response) do
    parsed_places = Enum.map(places, &parse_place/1)

    text_search_response = %TextSearchResponse{
      places: parsed_places,
      next_page_token: Map.get(response, "nextPageToken")
    }

    {:ok, text_search_response}
  end

  def parse_text_search_response(_) do
    text_search_response = %TextSearchResponse{
      places: []
    }

    {:ok, text_search_response}
  end

  # Private helper functions for parsing

  defp parse_place(place_data) do
    address_components = parse_address_components(Map.get(place_data, "addressComponents", []))
    parsed_address = extract_address_fields(address_components)

    %Place{
      id: Map.get(place_data, "id"),
      display_name: parse_display_name(Map.get(place_data, "displayName")),
      formatted_address: Map.get(place_data, "formattedAddress"),
      location: parse_location(Map.get(place_data, "location")),
      rating: Map.get(place_data, "rating"),
      user_rating_count: Map.get(place_data, "userRatingCount"),
      price_level: Map.get(place_data, "priceLevel"),
      business_status: Map.get(place_data, "businessStatus"),
      types: Map.get(place_data, "types"),
      primary_type: Map.get(place_data, "primaryType"),
      primary_type_display_name: parse_display_name(Map.get(place_data, "primaryTypeDisplayName")),
      opening_hours: parse_opening_hours(Map.get(place_data, "openingHours")),
      photos: parse_photos(Map.get(place_data, "photos", [])),
      plus_code: parse_plus_code(Map.get(place_data, "plusCode")),
      reviews: parse_reviews(Map.get(place_data, "reviews", [])),
      website_uri: Map.get(place_data, "websiteUri"),
      google_maps_uri: Map.get(place_data, "googleMapsUri"),
      international_phone_number: Map.get(place_data, "internationalPhoneNumber"),
      national_phone_number: Map.get(place_data, "nationalPhoneNumber"),
      address_components: address_components,
      viewport: parse_viewport(Map.get(place_data, "viewport")),
      # Parsed address fields
      city: Map.get(parsed_address, :city),
      state: Map.get(parsed_address, :state),
      postal_code: Map.get(parsed_address, :postal_code),
      country: Map.get(parsed_address, :country),
      street: "#{parsed_address[:street_number]} #{parsed_address[:route]}"
    }
  end

  defp parse_display_name(nil), do: nil

  defp parse_display_name(display_name) do
    %DisplayName{
      text: Map.get(display_name, "text"),
      language_code: Map.get(display_name, "languageCode")
    }
  end

  defp parse_location(nil), do: nil

  defp parse_location(location) do
    %Location{
      latitude: Map.get(location, "latitude"),
      longitude: Map.get(location, "longitude")
    }
  end

  defp parse_opening_hours(nil), do: nil

  defp parse_opening_hours(opening_hours) do
    %OpeningHours{
      open_now: Map.get(opening_hours, "openNow"),
      periods: parse_periods(Map.get(opening_hours, "periods", [])),
      weekday_text: Map.get(opening_hours, "weekdayText")
    }
  end

  defp parse_periods(periods) when is_list(periods) do
    Enum.map(periods, &parse_period/1)
  end

  defp parse_periods(_), do: []

  defp parse_period(period) do
    %Period{
      open: parse_time_point(Map.get(period, "open")),
      close: parse_time_point(Map.get(period, "close"))
    }
  end

  defp parse_time_point(nil), do: nil

  defp parse_time_point(time_point) do
    %TimePoint{
      day: Map.get(time_point, "day"),
      hour: Map.get(time_point, "hour"),
      minute: Map.get(time_point, "minute")
    }
  end

  defp parse_photos(photos) when is_list(photos) do
    Enum.map(photos, &parse_photo/1)
  end

  defp parse_photos(_), do: []

  defp parse_photo(photo) do
    %Photo{
      name: Map.get(photo, "name"),
      width_px: Map.get(photo, "widthPx"),
      height_px: Map.get(photo, "heightPx"),
      photo_reference: Map.get(photo, "photoReference")
    }
  end

  defp parse_plus_code(nil), do: nil

  defp parse_plus_code(plus_code) do
    %PlusCode{
      global_code: Map.get(plus_code, "globalCode"),
      compound_code: Map.get(plus_code, "compoundCode")
    }
  end

  defp parse_reviews(reviews) when is_list(reviews) do
    Enum.map(reviews, &parse_review/1)
  end

  defp parse_reviews(_), do: []

  defp parse_review(review) do
    %Review{
      name: Map.get(review, "name"),
      relative_publish_time_description: Map.get(review, "relativePublishTimeDescription"),
      rating: Map.get(review, "rating"),
      text: Map.get(review, "text"),
      original_text: Map.get(review, "originalText"),
      author_attribution: parse_author_attribution(Map.get(review, "authorAttribution")),
      publish_time: Map.get(review, "publishTime")
    }
  end

  defp parse_author_attribution(nil), do: nil

  defp parse_author_attribution(author) do
    %AuthorAttribution{
      display_name: Map.get(author, "displayName"),
      uri: Map.get(author, "uri"),
      photo_uri: Map.get(author, "photoUri")
    }
  end

  defp parse_address_components(address_components) when is_list(address_components) do
    Enum.map(address_components, &parse_address_component/1)
  end

  defp parse_address_components(_), do: []

  defp parse_address_component(component) do
    %AddressComponent{
      language_code: Map.get(component, "languageCode"),
      long_text: Map.get(component, "longText"),
      short_text: Map.get(component, "shortText"),
      types: Map.get(component, "types", [])
    }
  end

  defp parse_viewport(nil), do: nil

  defp parse_viewport(viewport) do
    %Viewport{
      low: parse_location(Map.get(viewport, "low")),
      high: parse_location(Map.get(viewport, "high"))
    }
  end

  defp extract_address_fields(address_components) when is_list(address_components) do
    Enum.reduce(address_components, %{}, fn component, acc ->
      types = Map.get(component, :types, [])
      long_text = Map.get(component, :long_text)

      cond do
        "locality" in types -> Map.put(acc, :city, long_text)
        "administrative_area_level_1" in types -> Map.put(acc, :state, long_text)
        "postal_code" in types -> Map.put(acc, :postal_code, long_text)
        "country" in types -> Map.put(acc, :country, long_text)
        "route" in types -> Map.put(acc, :route, long_text)
        "street_number" in types -> Map.put(acc, :street_number, long_text)
        true -> acc
      end
    end)
  end

  defp extract_address_fields(_), do: %{}
end
