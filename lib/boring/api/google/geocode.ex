defmodule Boring.Api.Google.Geocode do
  @moduledoc """
  Client for interacting with the Google Geocoding API.

  This module provides a simple interface for working with Google's Geocoding API,
  allowing you to convert addresses into geographic coordinates and vice versa.
  It uses the shared base module for consistent request handling across all Google APIs.

  All functions come in two variants:
  - Regular functions that return `{:ok, %Req.Response{}}` or `{:error, reason}`
  - Bang (!) functions that return the response directly or raise an exception

  ## Examples

      # Get coordinates for an address
      {:ok, %Req.Response{status: 200, body: body}} = Boring.Api.Google.Geocode.geocode("1600 Amphitheatre Parkway, Mountain View, CA")

      # Reverse geocode coordinates to address
      {:ok, %Req.Response{}} = Boring.Api.Google.Geocode.reverse_geocode({37.4224764, -122.0842499})
  """

  alias Boring.Api.Google.HttpClient

  # Response structures
  defmodule GeocodeResponse do
    @moduledoc """
    Response structure for Geocoding API endpoints.
    """

    @type t :: %__MODULE__{
            results: [Result.t()],
            status: String.t(),
            error_message: String.t() | nil,
            info_messages: [String.t()] | nil
          }

    defstruct [:results, :status, :error_message, :info_messages]
  end

  defmodule Result do
    @moduledoc """
    Structure representing a geocoding result.
    """

    @type t :: %__MODULE__{
            address_components: [AddressComponent.t()],
            formatted_address: String.t(),
            geometry: Geometry.t(),
            place_id: String.t(),
            plus_code: PlusCode.t() | nil,
            types: [String.t()]
          }

    defstruct [
      :address_components,
      :formatted_address,
      :geometry,
      :place_id,
      :plus_code,
      :types
    ]
  end

  defmodule AddressComponent do
    @moduledoc """
    Structure representing an address component.
    """

    @type t :: %__MODULE__{
            long_name: String.t(),
            short_name: String.t(),
            types: [String.t()]
          }

    defstruct [:long_name, :short_name, :types]
  end

  defmodule Geometry do
    @moduledoc """
    Structure representing geometry information.
    """

    @type t :: %__MODULE__{
            location: Location.t(),
            location_type: String.t(),
            viewport: Viewport.t() | nil,
            bounds: Bounds.t() | nil
          }

    defstruct [:location, :location_type, :viewport, :bounds]
  end

  defmodule Location do
    @moduledoc """
    Structure representing a geographic location.
    """

    @type t :: %__MODULE__{
            lat: float(),
            lng: float()
          }

    defstruct [:lat, :lng]
  end

  defmodule Viewport do
    @moduledoc """
    Structure representing a viewport (recommended view area).
    """

    @type t :: %__MODULE__{
            northeast: Location.t(),
            southwest: Location.t()
          }

    defstruct [:northeast, :southwest]
  end

  defmodule Bounds do
    @moduledoc """
    Structure representing bounds (exact coverage area).
    """

    @type t :: %__MODULE__{
            northeast: Location.t(),
            southwest: Location.t()
          }

    defstruct [:northeast, :southwest]
  end

  defmodule PlusCode do
    @moduledoc """
    Structure representing Plus Code information.
    """

    @type t :: %__MODULE__{
            global_code: String.t(),
            compound_code: String.t()
          }

    defstruct [:global_code, :compound_code]
  end

  @type address :: String.t()
  @type coordinates :: {float(), float()}
  @type geocode_options :: [
          region: String.t(),
          bounds: String.t(),
          language: String.t(),
          result_type: String.t() | [String.t()],
          location_type: String.t() | [String.t()],
          components: String.t()
        ]
  @type reverse_geocode_options :: [
          language: String.t(),
          result_type: String.t() | [String.t()],
          location_type: String.t() | [String.t()]
        ]

  @doc """
  Geocodes an address to get its coordinates using the Geocoding API.

  Makes a GET request to the Google Geocoding API to convert an address into
  geographic coordinates (latitude and longitude).

  ## Parameters

  - `address` - The address or location to geocode
  - `options` - Optional parameters for geocoding (default: `[]`)

  ## Options

  - `:region` - Region code for biasing results (e.g., "us", "uk")
  - `:bounds` - Bounding box for biasing results
  - `:language` - Language for results (e.g., "en", "es")
  - `:result_type` - Filter results by type
  - `:location_type` - Filter results by location type
  - `:components` - Component filters

  ## Examples

      iex> {:ok, %Req.Response{}} = Boring.Api.Google.Geocode.geocode("1600 Amphitheatre Parkway, Mountain View, CA")

      iex> {:ok, %Req.Response{}} = Boring.Api.Google.Geocode.geocode("New York, NY", region: "us", language: "en")

  ## Returns

  - `{:ok, GeocodeResponse.t()}` on success
  - `{:error, reason}` on failure
  """
  @spec geocode(address(), geocode_options()) :: {:ok, GeocodeResponse.t()} | {:error, any()}
  def geocode(address, options \\ []) do
    params = Keyword.put(options, :address, address)

    case HttpClient.request(:geocode, "/json", params: params) do
      {:ok, %{status: status, body: body}} when status in 200..299 ->
        parse_geocode_response(body)

      {:ok, %{status: _status, body: body}} ->
        {:error, body}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  Geocodes an address to get its coordinates, returning the response directly.

  Similar to `geocode/2` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `address` - The address or location to geocode
  - `options` - Optional parameters for geocoding (default: `[]`)

  ## Examples

      iex> %Req.Response{} = Boring.Api.Google.Geocode.geocode!("1600 Amphitheatre Parkway, Mountain View, CA")

  ## Returns

  - The parsed response on success
  - Raises an exception on failure
  """
  @spec geocode!(address(), geocode_options()) :: GeocodeResponse.t()
  def geocode!(address, options \\ []) do
    case geocode(address, options) do
      {:ok, response} -> response
      {:error, reason} -> raise "Geocoding API request failed: #{inspect(reason)}"
    end
  end

  @doc """
  Reverse geocodes coordinates to get address information.

  Makes a GET request to the Google Geocoding API to convert latitude and longitude
  coordinates into human-readable address information.

  ## Parameters

  - `coordinates` - A tuple of {latitude, longitude} coordinates
  - `options` - Optional parameters for reverse geocoding (default: `[]`)

  ## Options

  - `:language` - Language for results (e.g., "en", "es")
  - `:result_type` - Filter results by type
  - `:location_type` - Filter results by location type

  ## Examples

      iex> {:ok, %Req.Response{}} = Boring.Api.Google.Geocode.reverse_geocode({37.4224764, -122.0842499})

      iex> {:ok, %Req.Response{}} = Boring.Api.Google.Geocode.reverse_geocode({40.7128, -74.0060}, language: "en")

  ## Returns

  - `{:ok, GeocodeResponse.t()}` on success
  - `{:error, reason}` on failure
  """
  @spec reverse_geocode(coordinates(), reverse_geocode_options()) ::
          {:ok, GeocodeResponse.t()} | {:error, any()}
  def reverse_geocode({lat, lng}, options \\ []) do
    latlng = "#{lat},#{lng}"
    params = Keyword.put(options, :latlng, latlng)

    case HttpClient.request(:geocode, "/json", params: params) do
      {:ok, %{status: status, body: body}} when status in 200..299 ->
        parse_geocode_response(body)

      {:ok, %{status: _status, body: body}} ->
        {:error, body}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  Reverse geocodes coordinates to get address information, returning the response directly.

  Similar to `reverse_geocode/2` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `coordinates` - A tuple of {latitude, longitude} coordinates
  - `options` - Optional parameters for reverse geocoding (default: `[]`)

  ## Examples

      iex> %Req.Response{} = Boring.Api.Google.Geocode.reverse_geocode!({37.4224764, -122.0842499})

  ## Returns

  - The parsed response on success
  - Raises an exception on failure
  """
  @spec reverse_geocode!(coordinates(), reverse_geocode_options()) :: GeocodeResponse.t()
  def reverse_geocode!(coordinates, options \\ []) do
    case reverse_geocode(coordinates, options) do
      {:ok, response} -> response
      {:error, reason} -> raise "Reverse geocoding API request failed: #{inspect(reason)}"
    end
  end

  # Response parsing functions

  @doc """
  Converts a raw API response map into structured response types.

  ## Parameters

  - `response_body` - The raw response body from the Geocoding API

  ## Returns

  - `{:ok, GeocodeResponse.t()}` on successful parsing
  - `{:error, reason}` on parsing failure
  """
  @spec parse_geocode_response(map()) :: {:ok, GeocodeResponse.t()} | {:error, any()}
  def parse_geocode_response(%{"status" => status} = response) do
    parsed_results =
      response
      |> Map.get("results", [])
      |> Enum.map(&parse_result/1)

    geocode_response = %GeocodeResponse{
      results: parsed_results,
      status: status,
      error_message: Map.get(response, "error_message"),
      info_messages: Map.get(response, "info_messages")
    }

    {:ok, geocode_response}
  end

  def parse_geocode_response(_), do: {:error, :invalid_response_format}

  # Private helper functions for parsing

  defp parse_result(result_data) do
    %Result{
      address_components: parse_address_components(Map.get(result_data, "address_components", [])),
      formatted_address: Map.get(result_data, "formatted_address"),
      geometry: parse_geometry(Map.get(result_data, "geometry")),
      place_id: Map.get(result_data, "place_id"),
      plus_code: parse_plus_code(Map.get(result_data, "plus_code")),
      types: Map.get(result_data, "types")
    }
  end

  defp parse_address_components(components) when is_list(components) do
    Enum.map(components, &parse_address_component/1)
  end

  defp parse_address_components(_), do: []

  defp parse_address_component(component) do
    %AddressComponent{
      long_name: Map.get(component, "long_name"),
      short_name: Map.get(component, "short_name"),
      types: Map.get(component, "types")
    }
  end

  defp parse_geometry(nil), do: nil

  defp parse_geometry(geometry) do
    %Geometry{
      location: parse_location(Map.get(geometry, "location")),
      location_type: Map.get(geometry, "location_type"),
      viewport: parse_viewport(Map.get(geometry, "viewport")),
      bounds: parse_bounds(Map.get(geometry, "bounds"))
    }
  end

  defp parse_location(nil), do: nil

  defp parse_location(location) do
    %Location{
      lat: Map.get(location, "lat"),
      lng: Map.get(location, "lng")
    }
  end

  defp parse_viewport(nil), do: nil

  defp parse_viewport(viewport) do
    %Viewport{
      northeast: parse_location(Map.get(viewport, "northeast")),
      southwest: parse_location(Map.get(viewport, "southwest"))
    }
  end

  defp parse_bounds(nil), do: nil

  defp parse_bounds(bounds) do
    %Bounds{
      northeast: parse_location(Map.get(bounds, "northeast")),
      southwest: parse_location(Map.get(bounds, "southwest"))
    }
  end

  defp parse_plus_code(nil), do: nil

  defp parse_plus_code(plus_code) do
    %PlusCode{
      global_code: Map.get(plus_code, "global_code"),
      compound_code: Map.get(plus_code, "compound_code")
    }
  end
end
