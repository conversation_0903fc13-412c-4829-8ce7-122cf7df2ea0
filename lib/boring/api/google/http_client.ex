defmodule Boring.Api.Google.HttpClient do
  @moduledoc """
  HTTP client for Google Maps Platform API requests.

  This module provides a unified request building system following the pattern
  described in the Dashbit Req/Stripe blog post. It reduces duplication across
  different Google API modules while maintaining flexibility.

  Instead of inferring the API type from the URL, this module accepts an explicit
  API type parameter to determine the correct configuration.
  """

  @type api_type :: :places | :geocode
  @type auth_type :: :header | :query_param

  @doc """
  Creates a new Req request configured for the specified Google API.

  ## Parameters

  - `api_type` - The type of Google API (:places or :geocode)
  - `options` - A keyword list of options to pass to Req.new/1

  ## Examples

      iex> request = Boring.Api.Google.HttpClient.new(:places, url: "/places:searchText")
      iex> is_struct(request, Req.Request)
      true

  ## Returns

  - A Req request struct configured for the specified Google API
  """
  @spec new(api_type(), keyword()) :: Req.Request.t()
  def new(api_type, options \\ []) do
    {base_url, auth_type} = get_api_config(api_type)

    base_options = [
      base_url: base_url,
      headers: build_headers(auth_type, options)
    ]

    # Add params for Geocoding API (uses query params for auth)
    base_options =
      if auth_type == :query_param do
        Keyword.put(base_options, :params, key: PetalPro.config([:google_maps, :api_token]))
      else
        base_options
      end

    base_options
    |> Req.new()
    |> Req.merge(options)
  end

  @doc """
  Makes a request to a Google API endpoint.

  ## Parameters

  - `api_type` - The type of Google API (:places or :geocode)
  - `url` - The API endpoint path
  - `options` - A keyword list of options to pass to Req.request/2

  ## Examples

      iex> {:ok, response} = Boring.Api.Google.HttpClient.request(:places, "/places:searchText", json: %{textQuery: "pizza"})

  ## Returns

  - `{:ok, %Req.Response{}}` on success
  - `{:error, reason}` on failure
  """
  @spec request(api_type(), String.t(), keyword()) :: {:ok, Req.Response.t()} | {:error, any()}
  def request(api_type, url, options \\ []) do
    adapter = Application.get_env(:boring, :http_client, Req)

    # Handle field mask for Places API
    {field_mask, clean_options} = Keyword.pop(options, :field_mask)

    req_options =
      if field_mask && api_type == :places do
        # For Places API, add field mask to headers
        headers = Keyword.get(clean_options, :headers, [])
        updated_headers = [{"X-Goog-FieldMask", field_mask} | headers]
        Keyword.put(clean_options, :headers, updated_headers)
      else
        clean_options
      end

    # For geocode API, merge params with base params (including API key)
    req_options =
      if api_type == :geocode and Keyword.has_key?(req_options, :params) do
        base_request = new(api_type, url: url)
        base_params = base_request.options[:params] || []
        additional_params = Keyword.get(req_options, :params, [])
        merged_params = Keyword.merge(base_params, additional_params)
        Keyword.put(req_options, :params, merged_params)
      else
        req_options
      end

    adapter.request(new(api_type, url: url), req_options)
  end

  @doc """
  Makes a request to a Google API endpoint, returning the response directly.

  Similar to `request/3` but returns the response directly instead of wrapping it
  in an `:ok` tuple. Raises an exception if the request fails.

  ## Parameters

  - `api_type` - The type of Google API (:places or :geocode)
  - `url` - The API endpoint path
  - `options` - A keyword list of options to pass to Req.request!/2

  ## Examples

      iex> response = Boring.Api.Google.HttpClient.request!(:places, "/places:searchText", json: %{textQuery: "pizza"})

  ## Returns

  - The response on success
  - Raises an exception on failure
  """
  @spec request!(api_type(), String.t(), keyword()) :: Req.Response.t()
  def request!(api_type, url, options \\ []) do
    case request(api_type, url, options) do
      {:ok, response} -> response
      {:error, reason} -> raise "Request failed: #{inspect(reason)}"
    end
  end

  # Private functions

  defp get_api_config(api_type) do
    case api_type do
      :places -> {"https://places.googleapis.com/v1", :header}
      :geocode -> {"https://maps.googleapis.com/maps/api/geocode", :query_param}
    end
  end

  defp build_headers(:header, options) do
    base_headers = [
      {"Content-Type", "application/json"},
      {"X-Goog-Api-Key", PetalPro.config([:google_maps, :api_token])}
    ]

    # Merge with any additional headers from options
    additional_headers = Keyword.get(options, :headers, [])
    base_headers ++ additional_headers
  end

  defp build_headers(:query_param, options) do
    base_headers = [{"Content-Type", "application/json"}]

    # Merge with any additional headers from options
    additional_headers = Keyword.get(options, :headers, [])
    base_headers ++ additional_headers
  end
end
