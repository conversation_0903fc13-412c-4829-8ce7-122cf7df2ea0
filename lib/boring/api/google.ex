defmodule Boring.Api.Google do
  @moduledoc """
  Facade module for Google Maps Platform APIs.

  This module provides a unified interface to Google's various APIs including
  Places and Geocoding. It delegates to specific modules for each API.

  For more specific functionality, you can use the individual modules directly:
  - `Boring.Api.Google.Places` - Places API (New) functionality
  - `Boring.Api.Google.Geocode` - Geocoding API functionality

  ## Examples

      # Search for places using text
      {:ok, %Boring.Api.Google.Places.TextSearchResponse{}} = Boring.Api.Google.text_search("restaurants in NYC")

      # Get coordinates for an address
      {:ok, %Boring.Api.Google.Geocode.GeocodeResponse{}} = Boring.Api.Google.geocode("1600 Amphitheatre Parkway, Mountain View, CA")
  """

  alias Boring.Api.Google.Geocode
  alias Boring.Api.Google.Places

  @type query :: String.t()
  @type address :: String.t()
  @type coordinates :: {float(), float()}
  @type text_search_options :: Places.text_search_options()
  @type geocode_options :: Geocode.geocode_options()
  @type reverse_geocode_options :: Geocode.reverse_geocode_options()

  # Places API delegation

  @doc """
  Searches for places using the Places API Text Search endpoint.

  Delegates to `Boring.Api.Google.Places.text_search/2`.

  ## Examples

      iex> {:ok, %Boring.Api.Google.Places.TextSearchResponse{}} = Boring.Api.Google.text_search("pizza restaurants")

  """
  @spec text_search(query(), text_search_options()) ::
          {:ok, Places.TextSearchResponse.t()} | {:error, any()}
  defdelegate text_search(query, options \\ []), to: Places

  @doc """
  Searches for places using the Places API Text Search endpoint, returning the response directly.

  Delegates to `Boring.Api.Google.Places.text_search!/2`.

  ## Examples

      iex> %Boring.Api.Google.Places.TextSearchResponse{} = Boring.Api.Google.text_search!("pizza restaurants")

  """
  @spec text_search!(query(), text_search_options()) :: Places.TextSearchResponse.t()
  defdelegate text_search!(query, options \\ []), to: Places

  # Geocoding API delegation

  @doc """
  Geocodes an address to get its coordinates using the Geocoding API.

  Delegates to `Boring.Api.Google.Geocode.geocode/2`.

  ## Examples

      iex> {:ok, %Boring.Api.Google.Geocode.GeocodeResponse{}} = Boring.Api.Google.geocode("1600 Amphitheatre Parkway, Mountain View, CA")

  """
  @spec geocode(address(), geocode_options()) ::
          {:ok, Geocode.GeocodeResponse.t()} | {:error, any()}
  defdelegate geocode(address, options \\ []), to: Geocode

  @doc """
  Geocodes an address to get its coordinates, returning the response directly.

  Delegates to `Boring.Api.Google.Geocode.geocode!/2`.

  ## Examples

      iex> %Boring.Api.Google.Geocode.GeocodeResponse{} = Boring.Api.Google.geocode!("1600 Amphitheatre Parkway, Mountain View, CA")

  """
  @spec geocode!(address(), geocode_options()) :: Geocode.GeocodeResponse.t()
  defdelegate geocode!(address, options \\ []), to: Geocode

  @doc """
  Reverse geocodes coordinates to get address information.

  Delegates to `Boring.Api.Google.Geocode.reverse_geocode/2`.

  ## Examples

      iex> {:ok, %Boring.Api.Google.Geocode.GeocodeResponse{}} = Boring.Api.Google.reverse_geocode({37.4224764, -122.0842499})

  """
  @spec reverse_geocode(coordinates(), reverse_geocode_options()) ::
          {:ok, Geocode.GeocodeResponse.t()} | {:error, any()}
  defdelegate reverse_geocode(coordinates, options \\ []), to: Geocode

  @doc """
  Reverse geocodes coordinates to get address information, returning the response directly.

  Delegates to `Boring.Api.Google.Geocode.reverse_geocode!/2`.

  ## Examples

      iex> %Boring.Api.Google.Geocode.GeocodeResponse{} = Boring.Api.Google.reverse_geocode!({37.4224764, -122.0842499})

  """
  @spec reverse_geocode!(coordinates(), reverse_geocode_options()) :: Geocode.GeocodeResponse.t()
  defdelegate reverse_geocode!(coordinates, options \\ []), to: Geocode
end
