defmodule Boring.Orgs.Checks.IsOrgAdmin do
  @moduledoc false
  use Ash.Policy.FilterCheck

  alias Boring.Orgs.Org

  @impl true
  def describe(_options) do
    "actor is an org admin"
  end

  @impl true
  def filter(actor, %{resource: Org}, _options) when not is_nil(actor) do
    expr(exists(memberships, user_id == ^actor.id and role == :admin))
  end

  @impl true
  def filter(_actor, _context, _options) do
    false
  end
end
