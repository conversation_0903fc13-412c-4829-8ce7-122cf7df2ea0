defmodule Boring.Orgs.Invitation do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Orgs

  alias <PERSON><PERSON>.Accounts
  alias Boring.Orgs.Org

  postgres do
    table "orgs_invitations"
    repo Boring.Repo

    references do
      reference :user, on_delete: :delete
      reference :org, on_delete: :delete
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end

  multitenancy do
    strategy :attribute
    attribute :org_id
    global? true
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :email, :ci_string, allow_nil?: false, public?: true

    timestamps()
  end

  relationships do
    belongs_to :user, Accounts.User
    belongs_to :org, Org, allow_nil?: false, public?: true
  end

  identities do
    identity :unique_invitation, [:org_id, :email]
  end
end
