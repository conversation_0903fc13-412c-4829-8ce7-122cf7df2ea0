defmodule Boring.Orgs.Membership do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Orgs

  alias Boring.Accounts.User
  alias Boring.Orgs.Org

  postgres do
    table "orgs_memberships"
    repo Boring.Repo

    references do
      reference :user, on_delete: :delete
      reference :org, on_delete: :delete
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end

  multitenancy do
    strategy :attribute
    attribute :org_id
    global? true
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :role, :org_role, allow_nil?: false, public?: true

    timestamps()
  end

  relationships do
    belongs_to :user, User, allow_nil?: false, public?: true
    belongs_to :org, Org, public?: true
  end
end
