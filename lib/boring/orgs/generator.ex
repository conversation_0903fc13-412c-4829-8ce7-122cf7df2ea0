defmodule Boring.Orgs.Generator do
  @moduledoc false
  use Ash.Generator

  import StreamData, only: [repeatedly: 1]

  alias Boring.Billing.Generator
  alias Boring.Orgs.Membership
  alias Boring.Orgs.Org

  def org(opts \\ []) do
    changeset_generator(
      Org,
      :create,
      defaults: [
        name: repeatedly(fn -> Faker.Company.name() end)
      ],
      overrides: Keyword.take(opts, [:name]),
      actor: opts[:actor],
      authorize?: false
    )
  end

  def subscribed_org(opts \\ []) do
    changeset_generator(
      Org,
      :create,
      defaults: [
        name: repeatedly(fn -> Faker.Company.name() end)
      ],
      overrides: Keyword.take(opts, [:name]),
      actor: opts[:actor],
      after_action: fn org ->
        customer = generate(Generator.customer(source: :org, org_id: org.id))

        generate(
          Generator.subscription(
            billing_customer_id: customer.id,
            status: opts[:subscription_status] || "active"
          )
        )

        org
      end
    )
  end

  def org_membership(opts \\ []) do
    if !Keyword.has_key?(opts, :user_id), do: raise("Must provide user ID")
    if !Keyword.has_key?(opts, :org_id), do: raise("Must provide org ID")

    changeset_generator(
      Membership,
      :create,
      defaults: [],
      overrides: opts
    )
  end
end
