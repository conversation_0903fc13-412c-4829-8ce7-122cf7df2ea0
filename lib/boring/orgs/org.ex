defmodule Boring.Orgs.Org do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Orgs,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [<PERSON><PERSON>ban]

  alias Ash.Resource.Info
  alias Boring.Accounts.User
  alias Boring.Acquisitions.Opportunity
  alias Boring.Acquisitions.OrgOpportunity
  alias Boring.Billing
  alias Boring.Changes.Slugify
  alias Boring.Orgs.Membership

  require Ash.Query

  @derive {Phoenix.Param, key: :slug}

  postgres do
    table "orgs"
    repo Boring.Repo
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      primary? true
      accept [:*]

      change {Slugify, attribute: :name},
        where: [present(:name), changing(:name)],
        only_when_valid?: true
    end

    create :create_personal_org do
      accept []

      change set_attribute(:name, actor(:name))
      change {Slugify, generate_random?: true}
    end

    update :update do
      primary? true
      require_atomic? false
      accept [:*]

      change {Slugify, attribute: :name},
        where: [present(:name), changing(:name)],
        only_when_valid?: true
    end
  end

  policies do
    bypass AshOban.Checks.AshObanInteraction do
      authorize_if always()
    end

    bypass actor_attribute_equals(:role, :admin) do
      authorize_if always()
    end

    bypass action_type([:update, :destroy]) do
      authorize_if Boring.Orgs.Checks.IsOrgAdmin
    end

    policy action_type(:create) do
      authorize_if Boring.Accounts.Checks.IsConfirmedUser
    end

    policy action_type(:read) do
      authorize_if relates_to_actor_via(:users)
    end
  end

  changes do
    change __MODULE__.Changes.AddActorAsOrgAdminHook,
      only_when_valid?: true,
      on: :create
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true

      constraints trim?: true,
                  min_length: 2,
                  max_length: 160
    end

    attribute :slug, :string, allow_nil?: false

    timestamps()
  end

  relationships do
    has_many :memberships, Membership
    many_to_many :users, User, through: Membership
    has_one :customer, Billing.Customers.Customer
    many_to_many :opportunities, Opportunity, through: OrgOpportunity
    has_many :org_opportunities, OrgOpportunity
  end

  calculations do
    calculate :remaining_opportunity_entitlement,
              :integer,
              {Boring.Orgs.Calculations.RemainingEntitlement,
               name: :opportunities, amount_used: :opportunities_assigned_this_interval}

    calculate :has_opportunities?, :boolean, expr(exists(opportunities, true))

    calculate :billing_product, :string, expr(customer.active_subscription.product)

    calculate :active_subscriber?, :boolean, expr(customer.active_subscriber?)
  end

  aggregates do
    count :opportunities_assigned_this_interval, :org_opportunities do
      filter expr(
               inserted_at >= parent(customer.active_subscription.current_period_start) and
                 inserted_at <= parent(customer.active_subscription.current_period_end_at)
             )
    end
  end

  identities do
    identity :unique_slug, [:slug]
  end

  defimpl Ash.ToTenant do
    def to_tenant(%{id: id}, resource) do
      to_tenant(id, resource)
    end

    def to_tenant(id, resource) when is_binary(id) do
      if Info.data_layer(resource) == AshPostgres.DataLayer &&
           Info.multitenancy_strategy(resource) == :context do
        "org_#{id}"
      else
        id
      end
    end
  end
end
