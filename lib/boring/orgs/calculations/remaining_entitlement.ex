defmodule Boring.Orgs.Calculations.RemainingEntitlement do
  @moduledoc """
  Calculate how much of an org's plan-level entitlement they
  have remaining for a given time period.
  """
  use Ash.Resource.Calculation

  @opt_schema [
    name: [
      doc: "The entitlement name to check.",
      required: true,
      type: :atom
    ],
    amount_used: [
      doc: "The calculation/aggregate used to evaluate the current entitlement usage.",
      required: true,
      type: :atom
    ]
  ]

  opt_schema = @opt_schema

  defmodule Opts do
    @moduledoc false
    use Spark.Options.Validator, schema: opt_schema
  end

  def opt_schema, do: @opt_schema

  @impl true
  def init(opts) do
    case Opts.validate(opts) do
      {:ok, opts} ->
        {:ok, Opts.to_options(opts)}

      {:error, error} ->
        {:error, Exception.message(error)}
    end
  end

  @impl true
  def load(_query, opts, _context) do
    [opts[:amount_used], [customer: [active_subscription: [:plan_id]]]]
  end

  @impl true
  def expression(opts, _context) do
    products =
      :billing_products
      |> PetalPro.config()
      |> Enum.flat_map(fn product ->
        Enum.map(product.plans, fn plan ->
          base_entitlement = product.entitlements[opts[:name]] || 0

          scaled_entitlement =
            case plan.interval do
              :month -> base_entitlement
              :year -> base_entitlement * 12
              _ -> 0
            end

          {plan.id, scaled_entitlement}
        end)
      end)

    expr(^get_entitlement(products) - ^ref(opts[:amount_used]))
  end

  # Customer is on a plan not in our config
  defp get_entitlement([]), do: 0

  defp get_entitlement([{plan_id, val} | rest]) do
    nested = get_entitlement(rest)

    expr(
      if customer.active_subscription.plan_id == ^plan_id do
        ^val
      else
        ^nested
      end
    )
  end
end
