defmodule Boring.Orgs.Org.Changes.AddActorAsOrgAdminHook do
  @moduledoc false
  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, %{actor: actor} = context) when not is_nil(actor) do
    Ash.Changeset.after_action(changeset, fn _changeset, record ->
      opts =
        context
        |> Ash.Context.to_opts()
        |> Keyword.put(:tenant, record)

      Boring.Orgs.create_membership!(actor, :admin, opts)

      {:ok, record}
    end)
  end

  @impl true
  def change(changeset, _opts, _context) do
    changeset
  end
end
