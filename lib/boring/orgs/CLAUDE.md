# Orgs Domain

Multi-tenant organization management with role-based access control and billing integration.

## Multi-Tenancy Architecture

### Tenant Model
- **Pattern**: Organization-based tenancy (`"org_#{id}"`)
- **Strategies**: `:context` (data isolation) + `:attribute` (global resources)
- **Protocol**: `Ash.ToTenant` converts org IDs to tenant contexts

### Tenant Scope Management
Use `Boring.Scope` for centralized scope handling:
```elixir
# Set scope from conn/socket
scope = Scope.from_conn(conn)
Ash.read!(Resource, scope.ash_options)

# Update scope
new_scope = Scope.put_tenant(scope, "org_#{org_id}")
```

## Key Resources

### Org (`org.ex`)
Primary tenant entity:
- **Attributes**: `name`, `slug` (auto-generated), timestamps
- **Relationships**: memberships, users, customer, opportunities, buybox
- **Multi-tenancy**: Context-based tenant
- **Pattern**: UUID v7 primary keys with automatic slug generation

### Membership (`membership.ex`)
User-organization relationships with roles:
- **Strategy**: `:attribute` multi-tenancy (global resource)
- **Roles**: `:admin` | `:member`
- **Cascading**: Deletes when user/org removed

### Invitation (`invitation.ex`)
Organization invitation management:
- **Strategy**: `:attribute` multi-tenancy (global resource)
- **Constraint**: Unique per org-email combination
- **Cross-tenant**: Accessible for invitation workflows

## Role-Based Access Control

### Authorization Patterns
```elixir
# Check org admin status
authorize_if IsOrgAdmin
authorize_if actor_attribute_equals(:role, :admin)  # System admin bypass

# Relationship-based access
authorize_if relates_to_actor_via(:users)
```

### Policy Checks
- **`IsOrgAdmin`**: Verifies admin membership via relationship traversal
- **System Admin Bypass**: Global admin role overrides org restrictions
- **AshOban Bypass**: Background jobs operate with elevated permissions

## Common Actions

### Organization Management
```elixir
# Create organization (actor becomes admin)
Boring.Orgs.create_org(%{name: "Company Name"})

# Create personal org for individual users
Boring.Orgs.create_personal_org(actor)

# Get remaining opportunity entitlements
Boring.Orgs.get_remaining_entitlement(org)
```

### Membership Management
```elixir
# Add user to organization
Boring.Orgs.create_membership(%{user_id: user_id, org_id: org_id, role: :admin})

# Send invitation
Boring.Orgs.create_invitation(%{email: email, org_id: org_id})
```

## Web Integration

### Plug Integration
```elixir
# In controller pipeline
plug AssignScopePlug

# In LiveView
on_mount AssignScopeHook
```

### Scope Usage
```elixir
# Access current scope
scope = conn.assigns.scope
tenant = scope.current_tenant  # "org_#{id}"

# Execute with scope
Ash.read!(Resource, scope.ash_options)
```

## Scheduled Jobs

### Opportunity Assignment
Daily job (`@daily` cron) for opportunity distribution:
- Respects billing entitlements and subscription status
- Filters active subscribers with valid buyboxes
- Assigns based on remaining quota

## Entitlement System

### Billing Integration
```elixir
# Check remaining entitlements
calculation :remaining_entitlement do
  # Integrates with billing plans
  # Considers usage within time periods
  # Supports multiple plan types
end
```

### Usage Tracking
- Weekly interval-based entitlement periods
- Opportunity assignment quotas per plan
- Automatic enforcement in scheduled jobs

## Types & Enums

### OrgRole (`types/org_role.ex`)
```elixir
:admin   # Full organization management
:member  # Limited access based on policies
```

## Key Files
- `orgs.ex` - Domain definition with scheduled jobs
- `org.ex` - Primary tenant entity
- `membership.ex` - User-org relationships
- `invitation.ex` - Invitation workflows
- `scope.ex` - Tenant scope management
- `checks/is_org_admin.ex` - Admin authorization
- `calculations/remaining_entitlement.ex` - Billing integration