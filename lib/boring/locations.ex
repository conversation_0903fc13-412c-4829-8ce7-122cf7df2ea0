defmodule Boring.Locations do
  @moduledoc false
  use Ash.Domain,
    extensions: [AshPhoenix]

  resources do
    resource __MODULE__.State do
      define :list_states, action: :read
      define :get_state, action: :read, get_by: :id
      define :get_state_by_name_or_code, args: [:name_or_code]
      define :create_state, action: :create
      define :update_state, action: :update
      define :delete_state, action: :destroy
      define :list_with_opportunities
      define :list_with_org_opportunities, args: [:org_id]
    end
  end
end
