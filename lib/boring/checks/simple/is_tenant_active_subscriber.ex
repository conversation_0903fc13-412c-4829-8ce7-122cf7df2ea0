defmodule Boring.Checks.Simple.IsTenantActiveSubscriber do
  @moduledoc false
  use Ash.Policy.SimpleCheck

  require Ash.Query

  def describe(_) do
    "tenant has an active subscription"
  end

  def match?(_actor, %{subject: %{tenant: nil}}, _opts), do: false

  def match?(_actor, %{subject: changeset}, _opts) do
    tenant_id =
      if is_struct(changeset.tenant), do: changeset.tenant.id, else: changeset.tenant

    Boring.Billing.Customers.Customer
    |> Ash.Query.for_read(:read)
    |> Ash.Query.filter(source_id == ^tenant_id)
    |> Ash.Query.filter(active_subscriber? == true)
    |> Ash.exists?(authorize?: false, tenant: changeset.tenant)
  end

  def match?(_, _, _), do: false
end
