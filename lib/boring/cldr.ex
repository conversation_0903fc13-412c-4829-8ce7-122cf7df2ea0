defmodule Boring.Cldr do
  @moduledoc false
  use Cldr,
    default_locale: "en",
    default_territory: "US",
    locales: ["en"],
    add_fallback_locales: false,
    gettext: PetalProWeb.Gettext,
    data_dir: "./priv/cldr",
    otp_app: :boring,
    precompile_number_formats: ["¤¤#,##0.##"],
    providers: [Cldr.DateTime, Cldr.Number, Cldr.Calendar, Money],
    generate_docs: true,
    force_locale_download: Mix.env() == :prod
end
