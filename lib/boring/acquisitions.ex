defmodule Boring.Acquisitions do
  @moduledoc false
  use Ash.Domain,
    extensions: [AshPhoenix]

  forms do
    form(:create_opportunity_contact, args: [:opportunity_id])
  end

  resources do
    resource __MODULE__.Opportunity do
      define :get_opportunity, action: :read, get_by: :id

      define :list_off_market_candidates_by_ids do
        action :read_off_market_candidates
        args [:ids]
      end

      define :list_opportunities_with_open_service_requests, action: :with_open_service_requests

      define :create_opportunity do
        action :create

        default_options bulk_options: [
                          stop_on_error?: false,
                          return_errors?: true,
                          notify?: true
                        ]
      end

      define :update_opportunity, action: :update
      define :delete_opportunity, action: :destroy
      define :update_opportunity_global_contacts, action: :update_global_contacts
    end

    resource __MODULE__.OpportunityExclusion do
      define :get_opportunity_exclusion, action: :read, get_by: :id
      define :create_opportunity_exclusion, action: :create
      define :update_opportunity_exclusion, action: :update
      define :delete_opportunity_exclusion, action: :destroy
    end

    resource __MODULE__.OrgOpportunity do
      define :read_org_opportunities_for_cards, action: :read_for_cards

      define :assign_opportunity do
        action :create

        default_options bulk_options: [
                          stop_on_error?: false,
                          return_errors?: true,
                          notify?: true
                        ]
      end

      define :get_org_opportunity, action: :read, get_by: :id
      define :get_org_opportunity_for_slide_over, action: :read_for_slide_over, get_by: :id
      define :update_org_opportunity, action: :update
      define :update_org_opportunity_status, action: :update_status
      define :favorite_org_opportunity, action: :favorite
      define :unfavorite_org_opportunity, action: :unfavorite

      define :set_org_opportunity_follow_up, action: :update_follow_up
      define :clear_org_opportunity_follow_up, action: :clear_follow_up

      define_calculation :get_org_opportunity_filter_counts,
        calculation: :preset_filter_counts

      define_calculation :org_opportunity_contacts_pubsub_topic do
        calculation :contacts_pubsub_topic

        args [:_record]
      end

      define_calculation :org_opportunity_service_requests_pubsub_topic do
        calculation :service_requests_pubsub_topic

        args [:_record]
      end
    end

    resource __MODULE__.CloudFile do
      define :create_cloud_file, action: :create
      define :cloud_file_upload_complete, action: :upload_complete
      define :get_cloud_file, action: :read, get_by: :id
      define :delete_cloud_file, action: :destroy
    end

    resource __MODULE__.Contact do
      define :list_contacts, action: :read
      define :list_editable_contacts, action: :read_editable
      define :list_all_contacts, action: :read_all
      define :list_archived_contacts, action: :read_archived
      define :get_contact, action: :read, get_by: :id
      define :get_contact_by_email, action: :read, get_by: :email
      define :create_contact, action: :create
      define :update_contact, action: :update
      define :archive_contact, action: :destroy
      define :unarchive_contact, action: :unarchive
    end

    resource __MODULE__.OpportunityContact do
      define :create_opportunity_contact, action: :create
      define :update_opportunity_contact, action: :update
      define :delete_opportunity_contact, action: :destroy
    end
  end
end
