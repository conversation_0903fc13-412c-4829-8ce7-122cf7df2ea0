defmodule Boring.Billing.Meters.MeterEvent do
  @moduledoc """
  Represents a metered billing event that is sent to the billing provider.

  Meter events track usage-based billing for customers. Each event has:
  - An event type (e.g., :skiptrace)
  - A numeric value representing the quantity
  - A reference to the customer being billed

  When a meter event is created, it is initially stored without provider information.
  The `sync_meter_events` action is then used to send the event to the billing provider
  and update the record with the provider's identifier and timestamp.
  """
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Billing.Meters,
    extensions: [AshOban]

  alias Boring.Billing.Meters.Changes.CreateBillingProviderMeterEventHook

  postgres do
    table "billing_meter_events"
    repo Boring.Repo

    references do
      reference :customer, on_delete: :restrict
    end
  end

  actions do
    defaults [:read]

    create :create do
      description """
      Creates a new meter event for a customer.

      This action records the billing event but does not immediately send it to the provider.
      The event will be queued for synchronization with the billing provider.
      """

      primary? true
      accept [:billing_customer_id, :event, :value]
    end

    update :sync_meter_events do
      description """
      Synchronizes a meter event with the billing provider.

      This action sends the meter event to the billing provider and updates
      the record with the provider's identifier and timestamp.

      This action is triggered automatically by the Oban scheduler for any
      meter events that don't have a provider_identifier.
      """

      require_atomic? false
      change CreateBillingProviderMeterEventHook
    end
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :event, :billing_meter_event do
      description "The type of meter event (e.g., :skiptrace)"
      allow_nil? false
    end

    attribute :value, :integer do
      description "The quantity or amount for this meter event"
      allow_nil? false
      constraints min: 1
    end

    attribute :provider_identifier, :string do
      description "The unique identifier assigned by the billing provider"
    end

    attribute :provider_created_at, :utc_datetime do
      description "The timestamp when the event was created in the billing provider"
    end

    timestamps()
  end

  relationships do
    belongs_to :customer, Boring.Billing.Customers.Customer do
      source_attribute :billing_customer_id
      allow_nil? false
    end
  end
end
