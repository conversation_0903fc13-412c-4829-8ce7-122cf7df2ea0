defmodule Boring.Billing.Meters.Changes.CreateBillingProviderMeterEventHook do
  @moduledoc """
  An Ash Resource Change that sends meter events to the billing provider.

  This change is used in the `sync_meter_events` action of the `MeterEvent` resource.
  It performs the following steps:

  1. Loads the customer relationship to get the provider_customer_id
  2. Formats the meter event data for the billing provider
  3. Sends the event to the billing provider via the configured provider module
  4. Updates the meter event with the provider's identifier and timestamp

  If the billing provider returns an error, it's converted to an Ash error and
  added to the changeset.
  """
  use Ash.Resource.Change

  @billing_provider Application.compile_env(:boring, :billing_provider)

  @impl true
  def change(changeset, _opts, _context) do
    Ash.Changeset.before_action(changeset, fn changeset ->
      record = Ash.load!(changeset.data, [customer: [:provider_customer_id]], authorize?: false)

      params = %{
        event_name: Atom.to_string(record.event),
        payload: %{
          stripe_customer_id: record.customer.provider_customer_id,
          value: record.value
        },
        timestamp: DateTime.to_unix(record.inserted_at)
      }

      case @billing_provider.create_meter_event(params) do
        {:ok, meter_event} ->
          changeset
          |> Ash.Changeset.force_change_attribute(
            :provider_identifier,
            meter_event.identifier
          )
          |> Ash.Changeset.force_change_attribute(
            :provider_created_at,
            DateTime.from_unix!(meter_event.created)
          )

        {:error, error} ->
          Ash.Changeset.add_error(changeset, Ash.Error.to_ash_error(error))
      end
    end)
  end

  @impl true
  def batch_change(changesets, opts, context) do
    Enum.map(changesets, &change(&1, opts, context))
  end
end
