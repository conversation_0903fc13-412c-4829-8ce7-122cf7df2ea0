defmodule Boring.Billing.Meters.Types.Event do
  @moduledoc """
  Defines the enum type for meter event types.

  This enum represents the different types of billable events that can be tracked
  in the system. Currently supported event types:

  - `:skiptrace` - Represents a skiptrace lookup operation

  When adding new event types, add them to the values list and ensure they are
  properly handled in the billing provider integration.
  """
  use Ash.Type.Enum,
    values: [
      :skiptrace
    ]
end
