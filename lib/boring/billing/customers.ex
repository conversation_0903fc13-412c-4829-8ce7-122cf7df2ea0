defmodule Boring.Billing.Customers do
  @moduledoc false
  use Ash.Domain

  resources do
    resource __MODULE__.Customer do
      define :entity, action: :read_billing_entity

      define :get_customer_by_source,
        args: [:source, :source_id],
        action: :get_by_source

      define :get_customer_by_provider_customer_id, action: :read, get_by: :provider_customer_id
      define :create_customer, action: :create
    end
  end
end
