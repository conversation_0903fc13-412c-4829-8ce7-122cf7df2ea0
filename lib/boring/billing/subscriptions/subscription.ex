defmodule Boring.Billing.Subscriptions.Subscription do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Billing.Subscriptions

  alias Boring.Billing.Customers.Customer

  postgres do
    table "billing_subscriptions"
    repo Boring.Repo

    references do
      reference :customer, on_delete: :restrict
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    read :read_active do
      prepare build(sort: [current_period_start: :desc])

      filter expr(active? == true)
    end

    update :cancel do
      change set_attribute(:status, "canceled")
      change set_attribute(:canceled_at, &NaiveDateTime.utc_now/0)
    end
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :status, :billing_subscription_status do
      allow_nil? false
      public? true
    end

    attribute :plan_id, :string, allow_nil?: false, public?: true

    attribute :provider_subscription_id, :string do
      allow_nil? false
      public? true
    end

    attribute :provider_subscription_items, {:array, :map} do
      allow_nil? false
      public? true
    end

    attribute :cancel_at, :naive_datetime
    attribute :canceled_at, :naive_datetime
    attribute :current_period_end_at, :naive_datetime, public?: true
    attribute :current_period_start, :naive_datetime, allow_nil?: false, public?: true

    timestamps()
  end

  relationships do
    belongs_to :customer, Customer do
      source_attribute :billing_customer_id
      allow_nil? false
      public? true
    end
  end

  calculations do
    calculate :product, :string, Boring.Billing.Calculations.GetProduct
    calculate :plan, :map, Boring.Billing.Calculations.GetPlanById
    calculate :active?, :boolean, expr(status in ["active", "trialing"])
  end
end
