defmodule Boring.Billing.Meters do
  @moduledoc """
  Domain module for managing metered billing events.

  This domain provides functionality for tracking and billing usage-based events
  in the application. It includes:

  - The `MeterEvent` resource for storing billable events
  - The `create_meter_event/4` function for creating new meter events
  - Integration with the billing provider to sync meter events

  Meter events are initially created without provider information and are later
  synchronized with the billing provider through an Oban job that calls the
  `sync_meter_events` action.

  ## Usage

  ```elixir
  # Create a meter event for a customer
  {:ok, meter_event} = Boring.Billing.Meters.create_meter_event(customer, :skiptrace, 1)

  # The meter event will be automatically synced with the billing provider
  # through the Oban scheduler
  ```
  """
  use Ash.Domain

  resources do
    resource __MODULE__.MeterEvent do
      define :create_meter_event do
        action :create
        args [:customer, :event, :value]

        custom_input :customer, :struct do
          constraints instance_of: Boring.Billing.Customers.Customer
          allow_nil? false

          transform do
            to :billing_customer_id
            using & &1.id
          end
        end
      end
    end
  end
end
