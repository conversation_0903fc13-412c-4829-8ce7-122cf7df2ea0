defmodule Boring.Billing.Subscriptions do
  @moduledoc false
  use Ash.Domain

  resources do
    resource __MODULE__.Subscription do
      define :create_subscription, action: :create
      define :get_subscription, action: :read, get_by: :id

      define :get_subscription_by_provider_subscription_id,
        action: :read,
        get_by: :provider_subscription_id,
        default_options: [load: [:customer]]

      define :get_active_subscription_by_customer_id,
        action: :read_active,
        get_by: :billing_customer_id

      define :cancel_subscription, action: :cancel
    end
  end
end
