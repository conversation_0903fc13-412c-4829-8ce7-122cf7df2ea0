defmodule Boring.Billing.Customers.Customer do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Billing.Customers

  alias Boring.Accounts.User
  alias Boring.Billing.Subscriptions.Subscription
  alias Boring.Orgs.Org

  postgres do
    table "billing_customers"
    repo Boring.Repo

    references do
      reference :user, on_delete: :delete
      reference :org, on_delete: :delete
    end

    custom_indexes do
      index [:provider]
    end
  end

  actions do
    defaults [:read, :destroy, update: :*]

    create :create do
      primary? true
      accept [:email, :provider, :provider_customer_id]

      argument :source, :atom do
        allow_nil? false
        constraints one_of: [:org, :user]
      end

      argument :source_id, :uuid, allow_nil?: false

      change manage_relationship(:source_id, :user, type: :append),
        where: [argument_equals(:source, :user)],
        only_when_valid?: true

      change manage_relationship(:source_id, :org, type: :append),
        where: [argument_equals(:source, :org)],
        only_when_valid?: true

      change load([:user, :org, :source_id, :source])
    end

    read :get_by_source do
      get? true

      argument :source, :atom do
        allow_nil? false
        constraints one_of: [:user, :org]
      end

      argument :source_id, :uuid, allow_nil?: false

      prepare fn query, _context ->
        source = Ash.Query.get_argument(query, :source)
        Ash.Query.build(query, load: source)
      end

      filter expr(source == ^arg(:source))
      filter expr(source_id == ^arg(:source_id))
    end

    action :read_billing_entity, :atom do
      constraints one_of: [:org, :user]

      run fn _input, _context ->
        {:ok, PetalPro.config(:billing_entity)}
      end
    end
  end

  preparations do
    prepare build(load: [:source_id, :source])
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :email, :ci_string, allow_nil?: false, public?: true
    attribute :provider, :string, allow_nil?: false, public?: true
    attribute :provider_customer_id, :string, allow_nil?: false, public?: true

    timestamps()
  end

  relationships do
    belongs_to :user, User, public?: true
    belongs_to :org, Org, public?: true

    has_many :subscriptions, Subscription do
      destination_attribute :billing_customer_id
    end

    has_one :active_subscription, Subscription do
      destination_attribute :billing_customer_id
      filter expr(status in ["active", "trialing", "past_due"])
    end
  end

  calculations do
    calculate :source_id, :uuid do
      calculation expr(if is_nil(org_id), do: user_id, else: org_id)
    end

    calculate :source, :atom do
      constraints one_of: [:org, :user]

      calculation expr(if is_nil(org_id), do: :user, else: :org)
    end
  end

  aggregates do
    exists :active_subscriber?, :subscriptions do
      filter expr(status in ["active", "trialing", "past_due"])
    end
  end
end
