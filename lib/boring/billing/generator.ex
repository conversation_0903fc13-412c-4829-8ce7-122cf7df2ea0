defmodule Boring.Billing.Generator do
  @moduledoc false
  use Ash.Generator

  import StreamData, only: [repeatedly: 1]

  alias Boring.Billing
  alias Boring.Billing.Customers.Customer
  alias Boring.Billing.Subscriptions.Subscription
  alias Boring.Orgs.Generator, as: Orgs

  def customer(overrides \\ []) do
    source = overrides[:source] || Boring.Billing.Customers.entity!()

    source_id =
      if source == :org do
        overrides[:org_id] || once(:default_org_id, fn -> generate(Orgs.org()).id end)
      else
        overrides[:user_id] ||
          once(:default_user_id, fn -> PetalPro.Accounts.UserSeeder.random_user().id end)
      end

    changeset_generator(
      Customer,
      :create,
      defaults: [
        email: repeatedly(fn -> Faker.Internet.email() end),
        provider: "stripe",
        provider_customer_id: repeatedly(fn -> "cus_#{Faker.String.base64(14)}" end),
        source_id: source_id,
        source: source
      ],
      overrides: overrides,
      authorize?: false
    )
  end

  def subscription(overrides \\ []) do
    billing_customer_id =
      overrides[:billing_customer_id] ||
        once(:default_billing_customer_id, fn -> generate(customer()).id end)

    changeset_generator(
      Subscription,
      :create,
      defaults: [
        status:
          repeatedly(fn ->
            overrides[:status] || Enum.random(Billing.Types.SubscriptionStatus.values())
          end),
        plan_id: "stripe-test-plan-a-monthly",
        current_period_start: repeatedly(fn -> DateTime.utc_now() end),
        provider_subscription_id: repeatedly(fn -> "sub_#{Faker.String.base64(25)}" end),
        provider_subscription_items: [
          %{
            price_id: "price_1OQj8pIWVkWpNCp74VstFtnd",
            product_id: "prod_PFDZyFfhgGUNOg"
          }
        ],
        billing_customer_id: billing_customer_id
      ],
      overrides: overrides,
      authorize?: false
    )
  end
end
