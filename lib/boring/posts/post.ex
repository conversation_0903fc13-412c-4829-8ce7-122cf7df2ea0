defmodule Boring.Posts.Post do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Posts

  alias Boring.Accounts.User

  postgres do
    table "posts"
    repo Boring.Repo
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :category, :string, public?: true
    attribute :title, :string, allow_nil?: false, public?: true
    attribute :slug, :string, allow_nil?: false, public?: true
    attribute :cover, :string, public?: true
    attribute :cover_caption, :string, public?: true
    attribute :summary, :string, public?: true
    attribute :content, :string, public?: true
    attribute :duration, :integer, public?: true

    attribute :published_category, :string, public?: true
    attribute :published_title, :string, public?: true
    attribute :published_slug, :string, public?: true
    attribute :published_cover, :string, public?: true
    attribute :published_cover_caption, :string, public?: true
    attribute :published_summary, :string, public?: true
    attribute :published_content, :string, public?: true
    attribute :published_duration, :integer, public?: true

    attribute :last_published, :naive_datetime, public?: true
    attribute :go_live, :utc_datetime, public?: true

    timestamps()
  end

  relationships do
    belongs_to :author, User, allow_nil?: false, public?: true
  end
end
