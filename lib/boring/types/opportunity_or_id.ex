defmodule Boring.Types.OpportunityOrId do
  @moduledoc false
  use Ash.Type.NewType,
    subtype_of: :union,
    constraints: [
      types: [
        opportunity: [type: :struct, constraints: [instance_of: Boring.Acquisitions.Opportunity]],
        opportunity_id: [type: :uuid]
      ]
    ]

  def to_id(%Ash.Union{type: :opportunity, value: opportunity}), do: opportunity.id
  def to_id(%Ash.Union{type: :opportunity_id, value: opportunity_id}), do: opportunity_id
end
