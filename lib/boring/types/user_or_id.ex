defmodule Boring.Types.UserOrId do
  @moduledoc false
  use Ash.Type.NewType,
    subtype_of: :union,
    constraints: [
      types: [
        petal_pro_user: [type: :struct, constraints: [instance_of: PetalPro.Accounts.User]],
        ash_user: [type: :struct, constraints: [instance_of: Boring.Accounts.User]],
        user_id: [type: :uuid]
      ]
    ]

  def to_id(%Ash.Union{type: type, value: user}) when type in [:petal_pro_user, :ash_user] do
    user.id
  end

  def to_id(%Ash.Union{type: :user_id, value: user_id}), do: user_id
end
