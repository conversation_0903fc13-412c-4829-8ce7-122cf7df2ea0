defmodule Boring.Types.CustomerOrId do
  @moduledoc false
  use Ash.Type.NewType,
    subtype_of: :union,
    constraints: [
      types: [
        ecto_customer: [
          type: :struct,
          constraints: [instance_of: PetalPro.Billing.Customers.Customer]
        ],
        ash_customer: [
          type: :struct,
          constraints: [instance_of: Boring.Billing.Customers.Customer]
        ],
        customer_id: [type: :uuid]
      ]
    ]

  def to_id(%Ash.Union{type: type, value: customer}) when type in [:ecto_customer, :ash_customer] do
    customer.id
  end

  def to_id(%Ash.Union{type: :customer_id, value: customer_id}), do: customer_id
end
