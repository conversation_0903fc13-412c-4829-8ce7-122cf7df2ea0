defmodule Boring.Locations.Generator do
  @moduledoc false
  use Ash.Generator

  alias Boring.Locations.State

  @doc """
  All states currently supported by Boring Business Leads (US and Canada).
  """
  def all_states do
    [
      %{code: "AK", name: "Alaska", country_code: "US"},
      %{code: "AL", name: "Alabama", country_code: "US"},
      %{code: "AR", name: "Arkansas", country_code: "US"},
      %{code: "AZ", name: "Arizona", country_code: "US"},
      %{code: "CA", name: "California", country_code: "US"},
      %{code: "CO", name: "Colorado", country_code: "US"},
      %{code: "CT", name: "Connecticut", country_code: "US"},
      %{code: "DC", name: "District of Columbia", country_code: "US"},
      %{code: "DE", name: "Delaware", country_code: "US"},
      %{code: "FL", name: "Florida", country_code: "US"},
      %{code: "GA", name: "Georgia", country_code: "US"},
      %{code: "HI", name: "Hawaii", country_code: "US"},
      %{code: "IA", name: "Iowa", country_code: "US"},
      %{code: "ID", name: "Idaho", country_code: "US"},
      %{code: "IL", name: "Illinois", country_code: "US"},
      %{code: "IN", name: "Indiana", country_code: "US"},
      %{code: "KS", name: "Kansas", country_code: "US"},
      %{code: "KY", name: "Kentucky", country_code: "US"},
      %{code: "LA", name: "Louisiana", country_code: "US"},
      %{code: "MA", name: "Massachusetts", country_code: "US"},
      %{code: "MD", name: "Maryland", country_code: "US"},
      %{code: "ME", name: "Maine", country_code: "US"},
      %{code: "MI", name: "Michigan", country_code: "US"},
      %{code: "MN", name: "Minnesota", country_code: "US"},
      %{code: "MO", name: "Missouri", country_code: "US"},
      %{code: "MS", name: "Mississippi", country_code: "US"},
      %{code: "MT", name: "Montana", country_code: "US"},
      %{code: "NC", name: "North Carolina", country_code: "US"},
      %{code: "ND", name: "North Dakota", country_code: "US"},
      %{code: "NE", name: "Nebraska", country_code: "US"},
      %{code: "NH", name: "New Hampshire", country_code: "US"},
      %{code: "NJ", name: "New Jersey", country_code: "US"},
      %{code: "NM", name: "New Mexico", country_code: "US"},
      %{code: "NV", name: "Nevada", country_code: "US"},
      %{code: "NY", name: "New York", country_code: "US"},
      %{code: "OH", name: "Ohio", country_code: "US"},
      %{code: "OK", name: "Oklahoma", country_code: "US"},
      %{code: "OR", name: "Oregon", country_code: "US"},
      %{code: "PA", name: "Pennsylvania", country_code: "US"},
      %{code: "RI", name: "Rhode Island", country_code: "US"},
      %{code: "SC", name: "South Carolina", country_code: "US"},
      %{code: "SD", name: "South Dakota", country_code: "US"},
      %{code: "TN", name: "Tennessee", country_code: "US"},
      %{code: "TX", name: "Texas", country_code: "US"},
      %{code: "UT", name: "Utah", country_code: "US"},
      %{code: "VA", name: "Virginia", country_code: "US"},
      %{code: "VT", name: "Vermont", country_code: "US"},
      %{code: "WA", name: "Washington", country_code: "US"},
      %{code: "WI", name: "Wisconsin", country_code: "US"},
      %{code: "WV", name: "West Virginia", country_code: "US"},
      %{code: "WY", name: "Wyoming", country_code: "US"}
    ]
  end

  @doc """
  Generates a valid state.

  Valid overrides:

  * country_code - a two-letter country code, either "US" or "CA". Defaults to "US".
  * state_code - a state abbreviation, eg. "MS". If not valid for the selected
    country, a random state will be used.
  """
  def state(overrides \\ []) do
    country_code = Keyword.get(overrides, :country_code, "US")

    if !Enum.member?(["CA", "US"], country_code) do
      raise RuntimeError, "Only US and CA country codes supported in State generator"
    end

    states = Enum.filter(all_states(), &(&1.country_code == country_code))

    valid_state =
      if state_code = Keyword.get(overrides, :state_code) do
        state_code = String.to_charlist(state_code)
        Enum.find(states, &(&1.id == state_code))
      end

    state_details =
      if valid_state do
        StreamData.repeatedly(fn -> valid_state end)
      else
        StreamData.repeatedly(fn -> Enum.random(states) end)
      end

    changeset_generator(
      State,
      :create,
      uses: [state_details: state_details],
      defaults: fn %{state_details: state_details} ->
        [
          name: state_details.name,
          code: state_details.code,
          visible: true,
          country_code: country_code
        ]
      end,
      authorize?: false
    )
  end

  def generate_unique_states(count, overrides \\ []) do
    do_generate_unique_states(count, overrides, [])
  end

  defp do_generate_unique_states(0, _overrides, acc), do: acc

  defp do_generate_unique_states(count, overrides, acc) do
    new_state = generate(state(overrides))

    if Enum.member?(Enum.map(acc, & &1.code), new_state.code) do
      do_generate_unique_states(count, overrides, acc)
    else
      do_generate_unique_states(count - 1, overrides, [new_state | acc])
    end
  end
end
