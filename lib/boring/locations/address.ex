defmodule Boring.Locations.Address do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: :embedded,
    domain: Boring.Locations

  actions do
    defaults [:read, :destroy, :create, :update]

    default_accept [:street, :city, :state, :postal_code]
  end

  attributes do
    uuid_primary_key :id

    attribute :street, :string do
      public? true
      allow_nil? false
      constraints trim?: true, allow_empty?: false
    end

    attribute :city, :string do
      public? true
      allow_nil? false
      constraints trim?: true, allow_empty?: false
    end

    attribute :postal_code, :string do
      public? true
      allow_nil? false
      constraints trim?: true, allow_empty?: false
    end

    attribute :state, :string do
      public? true
      allow_nil? false
      constraints trim?: true, allow_empty?: false
    end
  end

  calculations do
    calculate :formatted, :string do
      public? true
      constraints trim?: true, allow_empty?: false

      calculation expr(street <> ", " <> city <> ", " <> state <> " " <> postal_code)
    end
  end
end
