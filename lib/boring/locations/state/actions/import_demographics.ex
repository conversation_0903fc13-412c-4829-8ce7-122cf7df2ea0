defmodule Boring.Locations.State.Actions.ImportDemographics do
  @moduledoc false
  use Ash.Resource.Actions.Implementation

  alias Ash.Page.Offset
  alias Boring.Api.USCensusBureau
  alias Boring.Api.USCensusBureau.ACS5Detailed
  alias Boring.Locations.State

  require Logger

  def run(input, _opts, _context) do
    %{"id" => state_id} = Ash.ActionInput.get_argument(input, :primary_key)

    state_id
    |> Boring.Locations.get_state!(authorize?: false)
    |> import_demographics(authorize?: false)
  end

  defp import_demographics(%State{code: state_code} = state, opts) do
    case USCensusBureau.list_acs5_detailed(state_code, opts) do
      {:ok, %Offset{results: results} = page} when results != [] ->
        results
        |> Enum.map(&to_demographic/1)
        |> Boring.Demographics.create_demographic!(
          bulk_options: [return_errors?: true, stop_on_error?: false],
          authorize?: opts[:authorize?]
        )

        next_opts = Keyword.put(opts, :page, offset: page.offset + page.limit)

        import_demographics(state, next_opts)

      {:ok, %Offset{results: []}} ->
        Logger.debug("NO RESULTS")
        :ok

      {:error, error} ->
        {:error, Ash.Error.to_ash_error(error)}
    end
  end

  defp to_demographic(%ACS5Detailed{} = source_demographic) do
    %{
      geo_id: source_demographic.id,
      city: source_demographic.place_name,
      state_name: source_demographic.state_name,
      population_estimate: source_demographic.population_estimate,
      median_household_income_estimate: source_demographic.median_household_income_estimate
    }
  end
end
