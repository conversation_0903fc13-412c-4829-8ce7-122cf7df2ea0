defmodule Boring.Locations.State do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Locations,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [AshOban]

  postgres do
    table "states"
    repo Boring.Repo
  end

  actions do
    defaults [:read, :destroy, update: :*]

    create :create do
      accept [:name, :code, :country_code]

      primary? true
      upsert? true
      upsert_identity :unique_country_state
      upsert_fields {:replace_all_except, [:id, :inserted_at]}
    end

    read :get_state_by_name_or_code do
      get? true

      argument :name_or_code, :string, allow_nil?: false

      filter expr(name == ^arg(:name_or_code) or code == ^arg(:name_or_code))
    end

    read :list_with_opportunities do
      filter expr(has_opportunities? == true)
    end

    read :list_with_org_opportunities do
      argument :org_id, :uuid, allow_nil?: false

      filter expr(exists(opportunities.orgs, id == ^arg(:org_id)))
    end

    action :import_demographics do
      argument :primary_key, :map

      run __MODULE__.Actions.ImportDemographics
    end
  end

  policies do
    bypass AshOban.Checks.AshObanInteraction do
      authorize_if always()
    end

    bypass actor_attribute_equals(:role, :admin) do
      authorize_if always()
    end

    policy action_type(:read) do
      authorize_if always()
    end
  end

  preparations do
    prepare build(sort: [name: :asc])
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :name, :string, allow_nil?: false, public?: true
    attribute :code, :string, allow_nil?: false, public?: true

    attribute :country_code, Boring.Locations.Country,
      allow_nil?: false,
      public?: true,
      default: "US"

    timestamps()
  end

  relationships do
    has_many :opportunities, Boring.Acquisitions.Opportunity
    has_many :demographics, Boring.Demographics.Demographic
  end

  calculations do
    calculate :needs_demographics?, :boolean do
      calculation expr(
                    has_opportunities? == true and
                      count_demographic_cities < count_opportunity_cities
                  )
    end
  end

  aggregates do
    count :count_opportunities, :opportunities
    exists :has_opportunities?, :opportunities

    count :count_opportunity_cities, :opportunities do
      field :city
      uniq? true
    end

    count :count_demographic_cities, :demographics do
      field :city
      uniq? true
    end
  end

  identities do
    identity :unique_country_state, [:code, :country_code]
    identity :state_name, [:name]
    identity :state_code, [:code]
  end
end
