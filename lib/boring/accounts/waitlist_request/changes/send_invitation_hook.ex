defmodule Boring.Accounts.WaitlistRequest.Changes.SendInvitationHook do
  @moduledoc false
  use Ash.Resource.Change

  require Logger

  @impl true
  def change(changeset, _opts, _context) do
    Ash.Changeset.after_transaction(changeset, &send_invitation/3)
  end

  defp send_invitation(changeset, {:ok, record}, _context) do
    Logger.info("Successfully executed transaction for action #{changeset.action.name} on #{inspect(changeset.resource)}")

    record
    |> PetalPro.Email.waitlist_invitation()
    |> PetalPro.Mailer.deliver()

    {:ok, record}
  end

  defp send_invitation(changeset, {:error, reason}, _context) do
    Logger.error(
      "Failed to execute transaction for action #{changeset.action.name} on #{inspect(changeset.resource)}, reason: #{inspect(reason)}"
    )

    {:error, reason}
  end

  @impl true
  def atomic(changeset, opts, context) do
    {:ok, change(changeset, opts, context)}
  end
end
