defmodule Boring.Accounts.UserTotp do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Accounts

  alias Boring.Accounts.User

  postgres do
    table "users_totps"
    repo Boring.Repo

    references do
      reference :user, on_delete: :delete
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :secret, :binary, allow_nil?: false, sensitive?: true, public?: true
    # field :code, :string, virtual: true
    attribute :backup_codes, {:array, __MODULE__.BackupCode}, allow_nil?: false, public?: true

    timestamps()
  end

  relationships do
    belongs_to :user, User, allow_nil?: false, public?: true
  end
end
