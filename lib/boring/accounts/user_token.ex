defmodule Boring.Accounts.UserToken do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Accounts

  alias Boring.Accounts.User

  postgres do
    table "users_tokens"
    repo Boring.Repo

    references do
      reference :user, on_delete: :delete
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :token, :binary, allow_nil?: false, public?: true
    attribute :context, :string, allow_nil?: false, public?: true
    attribute :sent_to, :string

    create_timestamp :inserted_at
  end

  relationships do
    belongs_to :user, User, allow_nil?: false, public?: true
  end

  identities do
    identity :unique_token, [:context, :token]
  end
end
