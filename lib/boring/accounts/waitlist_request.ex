defmodule Boring.Accounts.WaitlistRequest do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Accounts

  alias Boring.Accounts.Types.PreferredRegion

  postgres do
    table "waitlist_requests"
    repo Boring.Repo
  end

  actions do
    defaults [:read, :destroy, update: :*]

    read :list_invite_candidates do
      argument :preferred_region, PreferredRegion, allow_nil?: false

      prepare build(sort: [inserted_at: :asc])

      filter expr(is_nil(invited_at))
      filter expr(is_nil(user))
      filter expr(report_feedback? == true)
      filter expr(testimonial? == true)
      filter expr(^arg(:preferred_region) in preferred_regions)
    end

    create :create do
      primary? true
      accept [:*]

      validate {Boring.Validations.IsEmailValid, attribute: :email}
    end

    action :send_invites_by_region do
      argument :region, PreferredRegion, allow_nil?: false

      run fn input, context ->
        opts = Ash.Context.to_opts(context)

        results =
          __MODULE__
          |> Ash.Query.for_read(
            :list_invite_candidates,
            %{preferred_region: input.arguments.region},
            opts
          )
          |> Ash.bulk_update!(
            :send_invite,
            %{},
            Keyword.put(opts, :return_errors?, true)
          )
      end
    end

    update :send_invite do
      require_atomic? false
      accept []
      change set_attribute(:invited_at, &DateTime.utc_now/0)

      change __MODULE__.Changes.SendInvitationHook
    end
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :email, :ci_string do
      allow_nil? false
      public? true
      constraints max_length: 160, trim?: true
    end

    attribute :name, :string do
      allow_nil? false
      public? true

      constraints trim?: true,
                  min_length: 1,
                  max_length: 160
    end

    attribute :interest_reason, :string,
      allow_nil?: false,
      public?: true

    attribute :testimonial?, :boolean, allow_nil?: false, public?: true

    attribute :lead_contacts_per_week, Boring.Accounts.Types.LeadContactsPerWeek,
      allow_nil?: false,
      public?: true

    attribute :report_feedback?, :boolean, allow_nil?: false, public?: true

    attribute :preferred_regions, {:array, PreferredRegion},
      default: [],
      allow_nil?: false,
      constraints: [min_length: 1],
      public?: true

    attribute :invited_at, :utc_datetime, public?: true

    timestamps(type: :utc_datetime)
  end

  relationships do
    has_one :user, Boring.Accounts.User do
      public? true
      no_attributes? true
      filter expr(email == parent(email))
    end
  end

  calculations do
    calculate :registered_at, :utc_datetime, expr(user.inserted_at)
    calculate :registered?, :boolean, expr(exists(user, true))
  end

  identities do
    identity :unique_email, [:email],
      eager_check?: true,
      message: "Looks like you've already signed up"
  end
end
