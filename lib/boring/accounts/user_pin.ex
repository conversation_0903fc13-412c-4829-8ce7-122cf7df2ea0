defmodule Boring.Accounts.UserPin do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Accounts

  alias Boring.Accounts.User

  postgres do
    table "users_pins"
    repo Boring.Repo

    references do
      reference :user, on_delete: :delete
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :hashed_pin, :binary, allow_nil?: true, public?: true
    attribute :attempts, :integer, default: 0, public?: true

    timestamps()
  end

  relationships do
    belongs_to :user, User, allow_nil?: false, public?: true
  end
end
