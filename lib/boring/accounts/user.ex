defmodule Boring.Accounts.User do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Accounts

  alias Boring.Billing
  alias Boring.Orgs

  postgres do
    table "users"
    repo Boring.Repo

    references do
      reference :customer, on_delete: :restrict
    end
  end

  actions do
    defaults [:read, :destroy, create: :*]

    update :update do
      primary? true
      require_atomic? false
      accept [:*]
    end
  end

  changes do
    change set_attribute(:last_signed_in_datetime, &DateTime.utc_now/0),
      where: [changing(:last_signed_in_ip)]
  end

  validations do
    validate {Boring.Validations.IsEmailValid, attribute: :email},
      where: [changing(:email)]
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :name, :string do
      public? true

      constraints trim?: true,
                  min_length: 1,
                  max_length: 160
    end

    attribute :email, :ci_string,
      allow_nil?: false,
      public?: true,
      constraints: [max_length: 160, trim?: true]

    # attribute :password, :string, virtual: true, redact: true
    # attribute :current_password, :string, virtual: true, redact: true
    attribute :hashed_password, :string, sensitive?: true
    attribute :confirmed_at, :naive_datetime, public?: true

    attribute :role, :user_role do
      default :user
      public? true
      allow_nil? false
    end

    attribute :avatar, :string, public?: true
    attribute :last_signed_in_ip, :string, public?: true
    attribute :last_signed_in_datetime, :utc_datetime

    attribute :is_subscribed_to_marketing_notifications, :boolean do
      default !PetalPro.gdpr_mode()
      public? true
    end

    attribute :is_suspended, :boolean, default: false, public?: true
    attribute :is_deleted, :boolean, default: false, public?: true
    attribute :is_onboarded, :boolean, default: false, public?: true

    # If you want to remove the impersonation feature, you can remove this field.
    # Search for and delete the files `user_impersonation_controller.ex` and
    # `auth_impersonation_routes.ex`. Then resolve errors using `mix test`
    # field :current_impersonator, :any, virtual: true

    # field :current_org, :map, virtual: true
    # field :is_online, :boolean, virtual: true, default: false

    timestamps()
  end

  relationships do
    has_many :memberships, Orgs.Membership
    many_to_many :orgs, Orgs.Org, through: Orgs.Membership
    has_one :customer, Billing.Customers.Customer
  end

  identities do
    identity :unique_email, [:email]
  end
end
