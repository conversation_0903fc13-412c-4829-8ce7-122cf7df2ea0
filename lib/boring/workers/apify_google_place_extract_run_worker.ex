defmodule Boring.Workers.ApifyGooglePlaceExtractRunWorker do
  @moduledoc """
  This worker creates a new Apify Actor Run pased on the given PlaceExtract id.

  Run an individual PlaceExtract with:
  Oban.insert(Boring.Workers.PlaceExtractRunWorker.new(%{id: "place_extract_id"}))
  """
  use Oban.Worker,
    queue: :data_extraction,
    max_attempts: 1,
    unique: [fields: [:args, :queue, :worker], keys: [:id]]

  alias Boring.Google
  alias Boring.Google.Apify

  require Logger

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"id" => id}} = _job) do
    place_extract = Google.get_place_extract!(id, load: [state: [:name, :country_code]])

    Logger.info(
      "PlaceExtractRunWorker: Scheduling ID #{place_extract.id} (#{place_extract.state.name}, #{place_extract.state.country_code})"
    )

    # Schedule the PlaceExtract
    case Apify.create_actor_run(place_extract) do
      {:ok, %{data: response}} ->
        Logger.info(
          "PlaceExtractRunWorker: Started ID #{place_extract.id} (#{place_extract.state.name}, #{place_extract.state.country_code})"
        )

        Google.update_place_extract(place_extract, %{
          last_actor_run_id: response["id"],
          last_actor_run_started_at: response["startedAt"],
          last_run_at: response["startedAt"],
          status: :running
        })

      {:error, error} ->
        Logger.error(
          "PlaceExtractRunWorker: Failed to schedule for ID #{place_extract.id} (#{place_extract.state.name}, #{place_extract.state.country_code}): #{inspect(error)}"
        )

        Google.update_place_extract(place_extract, %{
          status: :error
        })

        {:error, error}
    end
  end
end
