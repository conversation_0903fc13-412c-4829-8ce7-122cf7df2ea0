defmodule Boring.Workers.ApifyGooglePlaceIngestWorker do
  @moduledoc false
  use Oban.Worker,
    queue: :data_ingestion,
    max_attempts: 3,
    unique: [fields: [:args, :queue, :worker], keys: [:id]]

  alias Boring.Google
  alias Boring.Google.Apify
  alias Boring.Google.PlaceExtract

  require Logger

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"id" => id, "actor_run_id" => actor_run_id}}) do
    extract = Google.get_place_extract!(id, load: [:state], authorize?: false)

    Logger.info(
      "ApifyGooglePlaceIngestWorker: Parsing dataset for ID #{extract.id} (#{extract.state.name}, #{extract.state.country_code})"
    )

    Google.update_place_extract(extract, %{status: :ingesting}, authorize?: false)

    {:ok, _} = ingest_actor_run_dataset(extract, actor_run_id, limit: 250, offset: 0)

    :ok
  end

  defp ingest_actor_run_dataset(%PlaceExtract{} = extract, actor_run_id, query_params) do
    case Apify.get_actor_run_dataset(actor_run_id, query_params) do
      {:ok, %Apify.Response{data: results} = response} when results != [] ->
        ingest_google_places(extract, results)

        ingest_actor_run_dataset(
          extract,
          actor_run_id,
          response.next_query_params
        )

      {:ok, %Apify.Response{data: results}} when results == [] ->
        Google.update_place_extract(extract, %{status: :ingested}, authorize?: false)
        {:ok, "#{extract.state.name}, #{extract.state.country_code} - Actor Run: #{actor_run_id}"}

      {:error, %Apify.Response{error: error_message}} ->
        Logger.error(error_message)
        Google.update_place_extract(extract, %{status: :error}, authorize?: false)
        {:error, error_message}
    end
  end

  defp google_place_to_attrs(google_place) do
    %{
      place_id: google_place["placeId"],
      customer_id: google_place["cid"],
      maps_url: google_place["url"],
      google_state: google_place["state"],
      google_country: google_place["countryCode"],
      review_count: google_place["reviewsCount"],
      website: google_place["website"],
      city: google_place["city"],
      phone_number: google_place["phone"],
      business_name: google_place["title"],
      street: google_place["street"],
      review_score: google_place["totalScore"],
      postal_code: google_place["postalCode"],
      full_address: google_place["address"],
      description: google_place["description"],
      latitude: if(google_place["location"] != nil, do: google_place["location"]["lat"]),
      longitude: if(google_place["location"] != nil, do: google_place["location"]["lng"]),
      categories: google_place["categories"],
      review_distribution:
        if(google_place["reviewsDistribution"] != nil,
          do:
            Recase.Enumerable.convert_keys(
              google_place["reviewsDistribution"],
              &Recase.to_snake/1
            )
        ),
      reviews:
        if(google_place["reviews"] != nil,
          do: Recase.Enumerable.convert_keys(google_place["reviews"], &Recase.to_snake/1)
        ),
      review_tags: google_place["reviewsTags"],
      opening_hours: google_place["openingHours"],
      additional_info: google_place["additionalInfo"],
      result_rank: google_place["rank"],
      permanently_closed: google_place["permanentlyClosed"],
      temporarily_closed: google_place["temporarilyClosed"],
      image_urls: google_place["imageUrls"],
      is_business_claimed: not google_place["claimThisBusiness"]
    }
  end

  defp strip_nil_values(google_place) do
    google_place
    |> Enum.filter(fn {_, value} -> value != nil end)
    |> Map.new()
  end

  defp ingest_google_places(%PlaceExtract{} = extract, google_places) when is_list(google_places) do
    google_places
    |> Enum.map(&parse_place_data(&1, extract))
    |> Boring.Google.create_place(
      authorize?: false,
      bulk_options: [return_records?: false, return_errors?: true]
    )
  end

  def parse_place_data(record, extract) do
    record
    |> google_place_to_attrs()
    |> strip_nil_values()
    |> Map.put(:state_id, extract.state_id)
  end
end
