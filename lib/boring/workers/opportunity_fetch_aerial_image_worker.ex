defmodule Boring.Workers.OpportunityFetchAerialImageWorker do
  @moduledoc false
  use Oban.Worker, queue: :aerial_images, max_attempts: 1

  alias Boring.Acquisitions

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"opportunity_id" => id}} = _job) do
    id
    |> Acquisitions.get_opportunity!(authorize?: false)
    |> Ash.Changeset.for_update(:fetch_and_save_aerial_image)
    |> Ash.update(authorize?: false)
  end
end
