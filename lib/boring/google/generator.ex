defmodule Boring.Google.Generator do
  @moduledoc false
  use Ash.Generator

  import StreamData, only: [repeatedly: 1]
  import Util.Generator

  alias Boring.Google
  alias Boring.Locations
  alias Faker.Address.En

  def place_extract(overrides \\ []) do
    status_cycle_pid =
      Faker.Util.cycle_start(Boring.Google.Types.ExtractStatus.values())

    seed_generator(%Google.PlaceExtract{
      is_enabled: true,
      status: repeatedly(fn -> Faker.Util.cycle(status_cycle_pid) end),
      last_run_at: repeatedly(fn -> Faker.DateTime.backward(2) end),
      last_actor_run_id: repeatedly(fn -> Faker.UUID.v4() end),
      last_actor_run_started_at: repeatedly(fn -> Faker.DateTime.backward(2) end),
      last_actor_run_finished_at: repeatedly(fn -> Faker.DateTime.backward(1) end),
      state_id: repeatedly(fn -> overrides[:state_id] || generate(Locations.Generator.state()).id end)
    })
  end

  def place(overrides \\ []) do
    categories = Google.Place.Preparations.OpportunityCandidateFilter.storage_categories()

    latitude_bounds = StreamData.resize(StreamData.float(min: 24.520833, max: 49.384472), 50)
    longitude_bounds = StreamData.resize(StreamData.float(min: -124.771694, max: -66.947028), 50)

    changeset_generator(
      Google.Place,
      :create,
      uses: %{
        # A function using `changeset_generator` just like this one.
        state_generator: Locations.Generator.state(),
        street: repeatedly(&En.street_address/0),
        city: repeatedly(&En.city/0),
        postal_code: repeatedly(&En.zip_code/0),
        google_place_id: repeatedly(&Ash.UUID.generate/0),
        review_count: overrides[:review_count] || Enum.random(1..10)
      },
      defaults: fn inputs ->
        state = generate(inputs.state_generator)

        reviews =
          (&review_create_input/0)
          |> repeatedly()
          |> Enum.take(inputs.review_count)

        review_score =
          reviews
          |> Enum.map(& &1.stars)
          |> Enum.sum()
          |> Kernel./(length(reviews))
          |> Float.round(2)

        [
          place_id: inputs.google_place_id,
          customer_id: repeatedly(&Faker.UUID.v4/0),
          maps_url: repeatedly(fn -> "https://maps.google.com/#{inputs.google_place_id}" end),
          google_state: state.name,
          google_country: state.country_code,
          state_id: state.id,
          website: repeatedly(&Faker.Internet.url/0),
          city: inputs.city,
          phone_number: repeatedly(&random_us_phone_number/0),
          business_name: repeatedly(&Faker.Company.En.name/0),
          street: inputs.street,
          postal_code: inputs.postal_code,
          full_address: "#{inputs.street}, #{inputs.city}, #{state.code}, #{inputs.postal_code}",
          description: repeatedly(&Faker.Lorem.sentence/0),
          longitude: Enum.at(longitude_bounds, 0),
          latitude: Enum.at(latitude_bounds, 0),
          permanently_closed: false,
          temporarily_closed: false,
          result_rank: repeatedly(fn -> Faker.random_between(1, 100) end),
          categories:
            repeatedly(fn ->
              Enum.take(categories, Faker.random_between(1, length(categories)))
            end),
          reviews: reviews,
          review_score: review_score,
          review_count: repeatedly(fn -> Enum.count(reviews) end),
          review_distribution: review_distribution_fixture(reviews),
          is_business_claimed: repeatedly(fn -> Enum.random([true, false]) end)
        ]
      end,
      overrides: overrides,
      authorize?: false
    )
  end

  def review(overrides \\ []) do
    changeset_generator(
      Google.Place.Review,
      :create,
      defaults: review_create_input(),
      overrides: overrides,
      authorize?: false
    )
  end

  def place_fixture(opts \\ []) do
    opts
    |> Keyword.take([:review_count])
    |> place()
    |> Stream.take(opts[:count] || 1)
    |> Stream.map(fn changeset ->
      Map.new()
      |> Map.merge(changeset.attributes)
      |> Map.merge(changeset.arguments)
    end)
    |> Enum.to_list()
  end

  def review_create_input do
    review_id = Faker.UUID.v4()
    published_at = Faker.DateTime.backward(Faker.random_between(1, 100))

    %{
      review_id: review_id,
      name: Faker.Lorem.sentence(),
      text: Faker.Lorem.paragraph(),
      stars: Faker.random_between(1, 5),
      published_at: to_string(published_at),
      published_at_date: published_at,
      likes_count: Faker.random_between(1, 10),
      review_url: "https://maps.app.goo.gl/#{review_id}",
      reviewer_number_of_reviews: Faker.random_between(1, 100),
      response_from_owner_date: Faker.DateTime.backward(Enum.random(1..3)),
      response_from_owner_text: Faker.Lorem.sentence()
    }
  end

  defp review_distribution_fixture(reviews) do
    distribution =
      reviews
      |> Enum.to_list()
      |> Enum.frequencies_by(& &1.stars)

    %{
      one_star: Map.get(distribution, 1, 0),
      two_star: Map.get(distribution, 2, 0),
      three_star: Map.get(distribution, 3, 0),
      four_star: Map.get(distribution, 4, 0),
      five_star: Map.get(distribution, 5, 0)
    }
  end
end
