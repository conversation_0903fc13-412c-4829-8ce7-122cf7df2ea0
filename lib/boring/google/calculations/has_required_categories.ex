defmodule Boring.Google.Calculations.HasRequiredCategories do
  @moduledoc false
  use Ash.Resource.Calculation

  require Ash.Query

  @impl true
  def load(_query, _opts, _context), do: [:categories]

  @impl true
  def expression(_opts, _context) do
    expr(
      fragment(
        "? && array[
          'Self storage facility',
          'Self-storage facility',
          'Storage facility',
          'Storage',
          'Boat storage facility',
          'RV storage facility',
          'Automobile storage facility'
        ]",
        categories
      )
    )
  end

  def storage_categories do
    [
      "Self storage facility",
      "Self-storage facility",
      "Storage facility",
      "Storage",
      "Boat storage facility",
      "RV storage facility",
      "Automobile storage facility"
    ]
  end
end
