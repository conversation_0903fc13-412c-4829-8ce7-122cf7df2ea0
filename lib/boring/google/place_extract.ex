defmodule Boring.Google.PlaceExtract do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Google,
    authorizers: [Ash.Policy.Authorizer]

  alias Boring.Google.Apify
  alias Boring.Locations

  postgres do
    table "google_places_extracts"
    repo Boring.Repo

    references do
      reference :state, on_delete: :restrict
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    read :list_active_place_extracts do
      filter expr(is_enabled == true)
    end

    read :get_by_state do
      get? true

      argument :state, :ci_string, allow_nil?: false

      filter expr(state.code == ^arg(:state) or state.name == ^arg(:state))
    end

    action :ingest do
      argument :extract, :struct,
        allow_nil?: false,
        constraints: [instance_of: __MODULE__]

      run fn input, _context ->
        extract = Ash.ActionInput.get_argument(input, :extract)

        job =
          Boring.Workers.ApifyGooglePlaceIngestWorker.new(%{
            "id" => extract.id,
            "actor_run_id" => extract.last_actor_run_id
          })

        case Oban.insert(job) do
          {:ok, _} -> :ok
          {:error, error} -> {:error, Ash.Error.to_ash_error(error)}
        end
      end
    end

    action :start_extract do
      argument :extract, :struct,
        allow_nil?: false,
        constraints: [instance_of: __MODULE__]

      run fn input, _context ->
        extract = Ash.ActionInput.get_argument(input, :extract)
        Apify.create_actor_run(extract)
      end
    end
  end

  policies do
    bypass actor_attribute_equals(:role, :admin) do
      authorize_if always()
    end

    policy always() do
      access_type :strict

      forbid_if always()
    end
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :interval, :integer, default: 1, allow_nil?: false, public?: true

    attribute :interval_type, :extract_interval_type,
      default: :months,
      allow_nil?: false,
      public?: true

    attribute :is_enabled, :boolean, default: true, public?: true
    attribute :status, :extract_status, allow_nil?: false, default: :not_started
    attribute :last_run_at, :utc_datetime
    attribute :last_actor_run_id, :string, public?: true
    attribute :last_actor_run_started_at, :utc_datetime
    attribute :last_actor_run_finished_at, :utc_datetime

    timestamps()
  end

  relationships do
    belongs_to :state, Locations.State, allow_nil?: false, public?: true
  end
end
