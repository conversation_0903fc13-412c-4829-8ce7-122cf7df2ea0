defmodule Boring.Google.Place.Preparations.OpportunityCandidateFilter do
  @moduledoc false
  use Ash.Resource.Preparation

  @impl true
  def prepare(query, _opts, _context) do
    query
    |> filter_no_matching_exclusions()
    |> filter_matching_categories()
    |> filter_no_associated_opportunities()
  end

  defp filter_no_matching_exclusions(query) do
    Ash.Query.filter(query, not exists(opportunity_exclusions, true))
  end

  defp filter_no_associated_opportunities(query) do
    Ash.Query.filter(query, is_nil(related_opportunity))
  end

  defp filter_matching_categories(query) do
    Ash.Query.filter(query, at(categories, 0) in ^storage_categories())
  end

  def storage_categories do
    [
      "Self storage facility",
      "Self-storage facility",
      "Storage facility",
      "Storage",
      "Boat storage facility",
      "RV storage facility",
      "Caravan Storage Facility"
    ]
  end
end
