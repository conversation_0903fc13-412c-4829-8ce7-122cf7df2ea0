defmodule Boring.Google.Place.ReviewDistribution do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: :embedded,
    embed_nil_values?: false

  attributes do
    attribute :one_star, :integer, allow_nil?: false, public?: true
    attribute :two_star, :integer, allow_nil?: false, public?: true
    attribute :three_star, :integer, allow_nil?: false, public?: true
    attribute :four_star, :integer, allow_nil?: false, public?: true
    attribute :five_star, :integer, allow_nil?: false, public?: true
  end
end
