defmodule Boring.Google.Place.Changes.ConvertToOpportunity do
  @moduledoc false
  use Ash.Resource.Change

  alias Boring.Utils.PhoneNumber

  @impl true
  def change(changeset, _opts, _context) do
    place = Ash.load!(changeset.data, :state, authorize?: false)
    phone_number = Ash.Changeset.get_attribute(changeset, :phone_number)

    phone_number =
      case PhoneNumber.parse(phone_number) do
        {:ok, parsed} -> PhoneNumber.format_as_e164(parsed)
        _ -> nil
      end

    opportunity_params = %{
      phone_number: phone_number,
      name: Ash.Changeset.get_attribute(changeset, :business_name),
      map_url: Ash.Changeset.get_attribute(changeset, :maps_url),
      state_id: place.state.id
    }

    opportunity_params =
      Enum.reduce(
        [
          :full_address,
          :city,
          :street,
          :postal_code,
          :latitude,
          :longitude,
          :website
        ],
        opportunity_params,
        fn attribute, params ->
          value = Ash.Changeset.get_attribute(changeset, attribute)
          Map.put(params, attribute, value)
        end
      )

    Ash.Changeset.manage_relationship(changeset, :related_opportunity, opportunity_params,
      on_lookup: :relate_and_update,
      on_match: :update,
      on_no_match: :create,
      use_identities: [:google_place_id]
    )
  end

  @impl true
  def batch_change(changesets, opts, context) do
    Enum.map(changesets, &change(&1, opts, context))
  end
end
