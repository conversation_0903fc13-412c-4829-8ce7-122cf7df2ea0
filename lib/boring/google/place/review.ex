defmodule Boring.Google.Place.Review do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Google,
    primary_read_warning?: false

  postgres do
    table "google_place_reviews"
    repo Boring.Repo

    custom_indexes do
      index :published_at_date
    end

    references do
      reference :place, on_delete: :delete, name: "google_place_reviews_place_id_fkey"
    end
  end

  actions do
    defaults [:read, :destroy, update: :*]

    create :create do
      primary? true
      upsert? true
      upsert_identity :unique_google_review
      skip_unknown_inputs :*

      accept [:*]

      argument :stars, :integer do
        constraints min: 1, max: 5
        allow_nil? false
      end

      change set_attribute(:rating, arg(:stars))
    end
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :review_id, :string, allow_nil?: false, public?: true
    attribute :name, :string, public?: true
    attribute :text, :string, public?: true
    attribute :published_at, :string, public?: true
    attribute :published_at_date, :utc_datetime, public?: true
    attribute :likes_count, :integer, public?: true
    attribute :review_url, :string, public?: true
    attribute :reviewer_number_of_reviews, :integer

    attribute :rating, :integer,
      public?: true,
      constraints: [min: 1, max: 5],
      allow_nil?: false

    attribute :response_from_owner_date, :utc_datetime, public?: true
    attribute :response_from_owner_text, :string, public?: true
    attribute :review_context, :map
    attribute :review_detailed_rating, :map
    attribute :visited_in, :string, public?: true
    attribute :reviewer_photo_url, :string, public?: true

    timestamps()
  end

  relationships do
    belongs_to :place, Boring.Google.Place, allow_nil?: false, public?: true
  end

  identities do
    identity :unique_google_review, [:review_id]
  end
end
