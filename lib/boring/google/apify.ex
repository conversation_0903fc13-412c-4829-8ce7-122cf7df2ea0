defmodule Boring.Google.Apify do
  @moduledoc """
  API interface to Apify.
  """
  use PetalProWeb, :verified_routes

  alias Boring.Google
  alias Boring.Google.Apify.Response

  def client do
    Tesla.client(middleware(), adapter())
  end

  defp middleware do
    [
      {Tesla.Middleware.BaseUrl, "https://api.apify.com/v2"},
      Tesla.Middleware.PathParams,
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Timeout, timeout: 60_000}
    ]
  end

  defp adapter do
    {Tesla.Adapter.Finch, name: PetalPro.Finch}
  end

  @task_id PetalPro.config(:apify_task_id)
  @status_types [
    "READY",
    "RUNNING",
    "SUCCEEDED",
    "FAILED",
    "TIMING-OUT",
    "TIMED-OUT",
    "ABORTING",
    "ABORTED"
  ]

  def create_actor_run(%Google.PlaceExtract{} = extract) do
    body = %{
      searchStringsArray: [
        "self-storage"
      ],
      state: extract.state.name,
      countryCode: extract.state.country_code |> to_string() |> String.downcase(),
      language: "en",
      maxReviews: 0,
      deeperCityScrape: true,
      reviewsSort: "newest",
      scrapeReviewsPersonalData: false,
      scrapeResponseFromOwnerText: false,
      maxImages: 0
    }

    case Tesla.post(client(), "/actor-tasks/#{@task_id}/runs", body, query: add_query_token()) do
      {:ok, %Tesla.Env{body: %{"error" => %{"message" => message}}}} ->
        {:error, message}

      {:ok, %Tesla.Env{body: body}} ->
        {:ok, body["data"]["id"]}

      {:error, error} ->
        {:error, error}
    end
  end

  def get_actor_run(run_id) do
    Tesla.get(client(), "/actor-runs/:run_id",
      query: add_query_token(),
      opts: [path_params: [run_id: run_id]]
    )
  end

  def abort_actor_run(run_id) do
    Tesla.post(client(), "/actor-runs/:run_id/abort",
      opts: [path_params: [run_id: run_id]],
      query: add_query_token(gracefully: false)
    )
  end

  def list_actor_runs do
    Tesla.get(client(), "/actor-runs", query: add_query_token())
  end

  def get_actor_runs_by_status(status) when is_binary(status) and status in @status_types do
    Tesla.get(client(), "/actor-runs",
      query:
        add_query_token(
          desc: true,
          status: status
        )
    )
  end

  def get_actor_runs_count_by_status(status) when is_binary(status) and status in @status_types do
    {:ok, response} = get_actor_runs_by_status(status)
    response.body["data"]["count"]
  end

  def get_datasets do
    Tesla.get(client(), "/datasets", query: add_query_token())
  end

  def get_dataset_items(dataset_id, query_params \\ []) do
    default_query_params = [
      format: "json",
      clean: true,
      limit: 250,
      offset: 0
    ]

    query_params =
      default_query_params
      |> Keyword.merge(query_params)
      |> add_query_token()

    Tesla.get(client(), "/datasets/:dataset_id/items",
      opts: [
        path_params: [dataset_id: dataset_id]
      ],
      query: query_params
    )
  end

  def get_actor_run_dataset(actor_run_id, query_params \\ []) do
    default_query_params = [
      format: "json",
      clean: true,
      limit: 250,
      offset: 0
    ]

    query_params =
      default_query_params
      |> Keyword.merge(query_params)
      |> add_query_token()

    result =
      client()
      |> Tesla.get(
        "/actor-runs/:actor_run_id/dataset/items",
        query: query_params,
        opts: [
          path_params: [actor_run_id: actor_run_id]
        ]
      )
      |> get_response()

    case result do
      {:ok, %Response{} = response} ->
        next_query_params =
          [limit: query_params[:limit], offset: query_params[:offset] + query_params[:limit]]

        {:ok, Map.put(response, :next_query_params, next_query_params)}

      {:error, %Response{}} = response ->
        response
    end
  end

  def get_response({_, %Tesla.Env{}} = response) do
    case response do
      {:ok, %Tesla.Env{status: 200, body: results}} when results != [] ->
        {:ok, %Response{data: results}}

      {:ok, %Tesla.Env{status: 200, body: results}} when results == [] ->
        {:ok, %Response{}}

      {:ok, %Tesla.Env{body: %{"error" => %{"message" => message}}}} ->
        {:error, %Response{error: message}}

      {:ok, %Tesla.Env{status: status}} when status != 200 ->
        {:error, %Response{error: Plug.Conn.Status.reason_phrase(status)}}

      {:error, error_message} ->
        {:error, %Response{error: error_message}}
    end
  end

  def get_status_types, do: @status_types

  defp add_query_token(query_params \\ []) do
    Keyword.put(query_params, :token, PetalPro.config([:apify, :api_token]))
  end
end
