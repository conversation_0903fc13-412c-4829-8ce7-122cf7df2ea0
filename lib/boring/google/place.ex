defmodule Boring.Google.Place do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Google,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [AshOban]

  alias Boring.Acquisitions.Opportunity
  alias Boring.Acquisitions.OpportunityExclusion
  alias Boring.Locations

  require Logger

  @accept_from_google [
    :place_id,
    :business_name,
    :full_address,
    :city,
    :street,
    :postal_code,
    :google_state,
    :google_country,
    :latitude,
    :longitude,
    :phone_number,
    :website,
    :review_score,
    :review_count,
    :categories,
    :permanently_closed,
    :temporarily_closed
  ]

  postgres do
    table "google_places"
    repo Boring.Repo

    references do
      reference :state, on_delete: :restrict
    end
  end

  oban do
    triggers do
      #   trigger :convert_to_opportunities do
      #     queue :data_ingestion
      #     read_action :list_opportunity_candidates
      #     stream_batch_size 250
      #     action :convert_to_opportunity
      #     state :active
      #     worker_module_name Boring.Google.Place.AshOban.Worker.ConvertToOpportunities
      #     scheduler_module_name Boring.Google.Place.AshOban.Scheduler.ConvertToOpportunities
      #   end
    end
  end

  actions do
    defaults [:read, :destroy, :update]

    default_accept [
      :place_id,
      :customer_id,
      :maps_url,
      :google_state,
      :google_country,
      :website,
      :city,
      :phone_number,
      :business_name,
      :street,
      :review_score,
      :review_count,
      :postal_code,
      :full_address,
      :description,
      :longitude,
      :latitude,
      :permanently_closed,
      :temporarily_closed,
      :result_rank,
      :categories,
      :additional_info,
      :review_distribution,
      :review_tags,
      :opening_hours,
      :image_urls,
      :is_business_claimed
    ]

    read :list_opportunity_candidates do
      pagination keyset?: true, required?: false, max_page_size: 250

      prepare __MODULE__.Preparations.OpportunityCandidateFilter
    end

    read :list_by_place_ids do
      argument :place_ids, {:array, :string}, allow_nil?: false

      filter expr(place_id in ^arg(:place_ids))
    end

    create :create do
      primary? true
      upsert? true
      upsert_identity :unique_google_place
      upsert_fields {:replace_all_except, [:id, :place_id, :inserted_at]}
      skip_unknown_inputs :*

      # change __MODULE__.Changes.SetState
    end

    create :create_from_google do
      upsert? true
      upsert_identity :unique_google_place
      skip_unknown_inputs :*

      accept @accept_from_google

      upsert_fields {:replace_all_except, [:id, :place_id, :is_business_claimed, :inserted_at]}

      # change __MODULE__.Changes.SetState
    end

    update :convert_to_opportunity do
      require_atomic? false
      change __MODULE__.Changes.ConvertToOpportunity
    end
  end

  policies do
    bypass AshOban.Checks.AshObanInteraction do
      authorize_if always()
    end

    bypass actor_attribute_equals(:role, :admin) do
      authorize_if always()
    end

    policy action_type(:read) do
      authorize_if always()
    end

    policy action_type([:create, :update, :destroy]) do
      forbid_if always()
    end
  end

  attributes do
    uuid_v7_primary_key :id
    attribute :place_id, :string, allow_nil?: false
    attribute :customer_id, :string
    attribute :maps_url, :string
    attribute :google_state, :string, allow_nil?: false
    attribute :google_country, :string, allow_nil?: false
    attribute :website, :string
    attribute :city, :string, allow_nil?: false
    attribute :phone_number, :string
    attribute :business_name, :string, allow_nil?: false
    attribute :street, :string, allow_nil?: false

    attribute :review_score, :float,
      default: 0.0,
      public?: true,
      constraints: [min: 0.0, max: 5.0],
      allow_nil?: false

    attribute :review_count, :integer, default: 0, allow_nil?: false
    attribute :postal_code, :string, allow_nil?: false
    attribute :full_address, :string, allow_nil?: false
    attribute :description, :string
    attribute :longitude, :float, allow_nil?: false
    attribute :latitude, :float, allow_nil?: false
    attribute :permanently_closed, :boolean, default: false
    attribute :temporarily_closed, :boolean, default: false
    attribute :result_rank, :integer
    attribute :categories, {:array, :string}, default: []
    attribute :additional_info, :map, default: %{}
    attribute :review_distribution, __MODULE__.ReviewDistribution
    attribute :review_tags, {:array, __MODULE__.ReviewTag}, default: []
    attribute :opening_hours, {:array, __MODULE__.OpeningHour}, default: []
    attribute :image_urls, {:array, :string}, default: []
    attribute :is_business_claimed, :boolean, default: true

    timestamps()
  end

  relationships do
    # belongs_to :state, Locations.State, allow_nil?: false

    has_one :state, Locations.State do
      no_attributes? true
      filter expr(code == parent(google_state) or name == parent(google_state))
    end

    has_many :reviews, __MODULE__.Review

    has_many :opportunity_exclusions, OpportunityExclusion do
      no_attributes? true
      filter expr(ilike(parent(business_name), "%#{name}%"))
    end

    has_one :related_opportunity, Opportunity, destination_attribute: :google_place_id
  end

  calculations do
    calculate :has_required_categories?, :boolean do
      calculation expr(primary_category in ^__MODULE__.Preparations.OpportunityCandidateFilter.storage_categories())
    end

    calculate :primary_category, :string, expr(at(categories, 0))
  end

  identities do
    identity :unique_google_place, [:place_id]
  end
end
