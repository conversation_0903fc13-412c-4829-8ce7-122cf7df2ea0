defmodule Boring.Acquisitions.OrgOpportunity.PresetFilterCounts do
  @moduledoc false
  use Ash.Resource.Calculation

  import Ash.Expr, only: [expr: 1]

  require Ash.Query

  @filter_order [
    :all,
    :follow_up,
    :interested,
    :offer_made,
    :interested_later,
    :no_answer,
    :trophies
  ]

  @impl true
  def init(opts) do
    {:ok, opts}
  end

  @impl true
  def load(_query, _opts, _ctx) do
    []
  end

  @impl true
  def calculate(_records, _opts, %{actor: actor, tenant: tenant}) do
    Boring.Acquisitions.OrgOpportunity
    |> Ash.Query.for_read(:read, %{}, actor: actor, tenant: tenant)
    |> Ash.aggregate([
      {:all, :count, []},
      {:follow_up, :count, query: [filter: expr(not is_nil(follow_up))]},
      {:interested, :count, query: [filter: expr(status == :interested)]},
      {:offer_made, :count, query: [filter: expr(status == :offer_made)]},
      {:interested_later, :count, query: [filter: expr(status == :interested_later)]},
      {:no_answer, :count, query: [filter: expr(status == :no_answer)]},
      {:trophies, :count, query: [filter: expr(favorite? == true)]}
    ])
    |> case do
      {:ok, result} ->
        {:ok, [prepare_filters(result)]}

      {:error, _} ->
        {:error, "Failed to calculate preset filter counts"}
    end
  end

  def prepare_filters(filter_counts) when is_map(filter_counts) do
    @filter_order
    |> Enum.map(&{&1, Map.get(filter_counts, &1)})
    |> Enum.map(&get_filter_details/1)
  end

  def get_filter_details({filter, count}) when is_atom(filter) and is_integer(count) do
    filter_details =
      case filter do
        :all -> %{color: "gray"}
        :follow_up -> %{color: "danger"}
        :interested -> %{color: "primary"}
        :offer_made -> %{color: "success"}
        :interested_later -> %{color: "gray"}
        :no_answer -> %{color: "gray"}
        :trophies -> %{color: "warning"}
        _ -> %{}
      end

    {filter, Map.put(filter_details, :count, count)}
  end
end
