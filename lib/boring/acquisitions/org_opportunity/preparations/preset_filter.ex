defmodule Boring.Acquisitions.OrgOpportunity.Preparations.PresetFilter do
  @moduledoc false
  use Ash.Resource.Preparation

  @impl true
  def prepare(query, _opts, _context) do
    preset_filter = Ash.Query.get_argument(query, :preset_filter)

    case preset_filter do
      :all ->
        query

      :follow_up ->
        Ash.Query.filter(query, not is_nil(follow_up))

      :interested ->
        Ash.Query.filter(query, status == :interested)

      :offer_made ->
        Ash.Query.filter(query, status == :offer_made)

      :interested_later ->
        Ash.Query.filter(query, status == :interested_later)

      :no_answer ->
        Ash.Query.filter(query, status == :no_answer)

      :trophies ->
        Ash.Query.filter(query, favorite? == true)

      _ ->
        query
    end
  end
end
