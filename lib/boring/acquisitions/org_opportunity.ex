defmodule Boring.Acquisitions.OrgOpportunity do
  @moduledoc false

  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Acquisitions,
    extensions: [AshStateMachine],
    notifiers: [Ash.Notifier.PubSub]

  require Ash.Query

  postgres do
    table "orgs_opportunities"
    repo Boring.Repo

    references do
      reference :opportunity, on_delete: :restrict
      reference :org, on_delete: :delete
    end
  end

  state_machine do
    initial_states [:new]
    default_initial_state :new
    state_attribute :status

    transitions do
      transition :update_status,
        from: [
          :new,
          :wants_too_much,
          :not_for_sale,
          :no_answer,
          :just_bought,
          :on_the_market,
          :interested_later
        ],
        to: [
          :interested,
          :not_for_sale,
          :no_answer,
          :just_bought,
          :wants_too_much,
          :on_the_market,
          :interested_later,
          :not_in_buybox
        ]

      transition :update_status,
        from: :not_in_buybox,
        to: [
          :new,
          :interested,
          :not_for_sale,
          :no_answer,
          :just_bought,
          :wants_too_much,
          :on_the_market,
          :interested_later
        ]

      transition :update_status,
        from: :interested,
        to: [
          :offer_made,
          :wants_too_much
        ]

      transition :update_status,
        from: :offer_made,
        to: [
          :offer_accepted,
          :offer_rejected
        ]

      transition :update_status,
        from: :offer_accepted,
        to: [
          :due_diligience_period,
          :canceled
        ]

      transition :update_status,
        from: :offer_rejected,
        to: [
          :offer_made,
          :wants_too_much,
          :canceled
        ]

      transition :update_status,
        from: :due_diligience_period,
        to: [
          :pending_close,
          :canceled
        ]

      transition :update_status,
        from: :pending_close,
        to: [
          :acquired,
          :canceled
        ]

      transition :update_status,
        from: :acquired,
        to: [
          :pending_close,
          :canceled
        ]

      transition :update_status,
        from: :canceled,
        to: [
          :acquired,
          :due_diligience_period,
          :interested,
          :interested_later,
          :just_bought,
          :new,
          :no_answer,
          :not_for_sale,
          :offer_accepted,
          :offer_made,
          :offer_rejected,
          :on_the_market,
          :pending_close,
          :wants_too_much
        ]
    end
  end

  actions do
    defaults [:read, update: :*]

    create :create do
      primary? true

      accept [:opportunity_id]
    end

    read :read_for_cards do
      pagination do
        required? false
        keyset? true
      end

      argument :statuses, {:array, :opportunity_status} do
        default []
      end

      argument :states, {:array, :string} do
        default []
      end

      argument :search_text, :ci_string do
        default ""
        constraints allow_empty?: true
      end

      argument :preset_filter, :atom do
        default :all

        constraints one_of: [
                      :all,
                      :new,
                      :follow_up,
                      :interested,
                      :offer_made,
                      :interested_later,
                      :no_answer,
                      :trophies
                    ]
      end

      prepare build(
                select: [
                  :id,
                  :status,
                  :follow_up,
                  :favorite?
                ],
                ensure_selected: [:opportunity_id, :org_id],
                load: [
                  :status,
                  :display_approx_follow_up,
                  :follow_up_status,
                  opportunity: [
                    :name,
                    :display_phone_number,
                    :state,
                    google_place: [:is_business_claimed, :review_count, :review_score]
                  ],
                  org: [:slug]
                ]
              )

      filter expr(
               contains(opportunity.name, ^arg(:search_text)) or
                 contains(opportunity.full_address, ^arg(:search_text))
             )

      prepare fn query, _ctx ->
        statuses = Ash.Query.get_argument(query, :statuses)

        if statuses == [] do
          query
        else
          Ash.Query.filter(
            query,
            status in ^statuses
          )
        end
      end

      prepare fn query, _ctx ->
        states = Ash.Query.get_argument(query, :states)

        if states == [] do
          query
        else
          Ash.Query.filter(
            query,
            opportunity.state_id in ^states
          )
        end
      end

      prepare __MODULE__.Preparations.PresetFilter
    end

    read :read_for_slide_over do
      prepare build(
                load: [
                  :status,
                  :display_follow_up_date,
                  :latest_completed_skip_trace,
                  :total_contacts,
                  contacts: [
                    :skip_traced?,
                    :name,
                    :email,
                    :phone_number,
                    contact: [
                      address: [:formatted]
                    ]
                  ],
                  opportunity: [
                    :name,
                    :display_phone_number,
                    :state,
                    :aerial_image_path,
                    :skip_trace_in_progress?,
                    files: [
                      :remote_name,
                      :local_name,
                      :presigned_get_url
                    ],
                    google_place: [
                      :review_score,
                      :review_count,
                      :is_business_claimed
                    ]
                  ]
                ]
              )
    end

    update :update_status do
      accept [:status]

      require_atomic? false

      change fn changeset, _context ->
        status = Ash.Changeset.get_attribute(changeset, :status)

        AshStateMachine.transition_state(changeset, status)
      end
    end

    update :favorite do
      accept []

      change set_attribute(:favorite?, true)
    end

    update :unfavorite do
      accept []

      change set_attribute(:favorite?, false)
    end

    update :update_follow_up do
      require_atomic? false
      accept []

      argument :time_value, :integer do
        allow_nil? false
        constraints min: 1
      end

      argument :time_unit, :atom do
        allow_nil? false
        constraints one_of: [:day, :week, :month]
      end

      change before_action(fn changeset, _ctx ->
               time_value = Ash.Changeset.get_argument(changeset, :time_value)
               time_unit = Ash.Changeset.get_argument(changeset, :time_unit)

               follow_up_date =
                 "Etc/UTC"
                 |> DateTime.now!()
                 |> DateTime.shift([{time_unit, time_value}])

               Ash.Changeset.force_change_attribute(changeset, :follow_up, follow_up_date)
             end)

      change load(:display_follow_up_date)
    end

    update :clear_follow_up do
      accept []
      change set_attribute(:follow_up, nil)
    end

    destroy :destroy do
      primary? true
    end
  end

  pub_sub do
    module PetalProWeb.Endpoint

    prefix "orgs"

    publish :create, [:_tenant] do
      event "opportunity_assigned"
      previous_values? false

      transform fn notification ->
        %{data: notification.data, actor: notification.actor}
      end
    end

    publish_all :update, [:_tenant] do
      event "opportunity_updated"
      previous_values? false

      transform fn notification ->
        %{data: notification.data, actor: notification.actor}
      end
    end

    publish :destroy, [:_tenant] do
      event "opportunity_unassigned"
      previous_values? false

      transform fn notification ->
        %{data: %{opportunity_id: notification.data.opportunity_id}, actor: notification.actor}
      end
    end
  end

  preparations do
    prepare build(load: [:opportunity])
  end

  multitenancy do
    strategy :attribute
    attribute :org_id
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :status, :opportunity_status do
      default :new
      public? true
      allow_nil? false
    end

    attribute :notes, :string, public?: true

    attribute :favorite?, :boolean do
      public? true
      default false
    end

    attribute :follow_up, :utc_datetime, public?: true

    timestamps(public?: true)
  end

  relationships do
    belongs_to :org, Boring.Orgs.Org

    belongs_to :opportunity, Boring.Acquisitions.Opportunity,
      allow_nil?: false,
      public?: true

    has_many :service_requests, Boring.Services.ServiceRequest do
      destination_attribute :opportunity_id
      source_attribute :opportunity_id
      sort updated_at: :desc
    end

    has_many :contacts, Boring.Acquisitions.OpportunityContact do
      destination_attribute :opportunity_id
      source_attribute :opportunity_id
    end
  end

  calculations do
    calculate :preset_filter_counts, :map do
      public? true

      calculation __MODULE__.PresetFilterCounts
    end

    calculate :display_follow_up_date, :string do
      public? true

      calculation fn records, _opts ->
        Enum.map(records, fn record ->
          if record.follow_up && DateTime.after?(record.follow_up, DateTime.utc_now()) do
            Calendar.strftime(record.follow_up, "%m/%d/%Y")
          end
        end)
      end
    end

    calculate :display_approx_follow_up, :string do
      public? true

      calculation fn records, _ctx ->
        Enum.map(records, fn record ->
          if record.follow_up do
            Boring.Cldr.DateTime.Relative.to_string!(record.follow_up,
              relative_to: DateTime.utc_now()
            )
          end
        end)
      end
    end

    calculate :follow_up_status,
              :atom,
              expr(
                cond do
                  is_nil(follow_up) -> :not_set
                  follow_up < now() -> :overdue
                  follow_up <= from_now(1, :day) -> :due
                  follow_up <= from_now(3, :day) -> :due_soon
                  true -> :due_later
                end
              ) do
      allow_nil? false
      public? true
      constraints one_of: [:overdue, :due_soon, :due_later, :not_set]
    end

    calculate :service_requests_pubsub_topic, :string do
      calculation expr("service_requests:#{org_id}:#{opportunity_id}")
    end

    calculate :contacts_pubsub_topic, :string do
      calculation expr("opportunity_contacts:#{org_id}:#{opportunity_id}")
    end
  end

  aggregates do
    max :latest_completed_skip_trace, :service_requests, :updated_at do
      filter expr(type == :skiptrace and not open?)
    end

    count :total_contacts, :contacts
  end

  identities do
    identity :tenant_unique_opportunity, :opportunity_id
  end
end
