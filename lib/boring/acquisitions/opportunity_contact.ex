defmodule Boring.Acquisitions.OpportunityContact do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Acquisitions,
    extensions: [AshArchival.Resource],
    authorizers: [Ash.Policy.Authorizer],
    notifiers: [Ash.Notifier.PubSub]

  alias Boring.Acquisitions.Opportunity

  postgres do
    table "opportunities_contacts"
    repo Boring.Repo

    references do
      reference :org, on_delete: :delete
      reference :opportunity, on_delete: :restrict
      reference :contact, on_delete: :restrict
    end

    base_filter_sql "(archived_at IS NULL)"
  end

  archive do
    exclude_read_actions [:read_archived, :read_all]
    base_filter? true
  end

  resource do
    base_filter expr(is_nil(archived_at))
  end

  actions do
    defaults [:read, :destroy]

    read :read_all

    read :read_archived do
      filter expr(not is_nil(archived_at))
    end

    create :create do
      primary? true
      accept [:role, :opportunity_id, :contact_id]

      argument :contact, :map

      change manage_relationship(:contact, on_lookup: :relate, on_no_match: :create),
        where: present(:contact)
    end

    update :update do
      primary? true
      accept [:role]
      require_atomic? false

      argument :contact, :map

      change manage_relationship(:contact, on_match: :update)
    end

    update :unarchive do
      change set_attribute(:archived_at, nil)
      atomic_upgrade_with :read_archived
    end
  end

  policies do
    policy action([:update, :destroy]) do
      authorize_if actor_attribute_equals(:role, :admin)
      authorize_unless expr(skip_traced?)
    end

    policy always() do
      authorize_if always()
    end
  end

  pub_sub do
    module PetalProWeb.Endpoint

    prefix "opportunity_contacts"

    transform fn notification ->
      %{data: notification.data, actor: notification.actor}
    end

    publish_all :create, [:org_id, :opportunity_id] do
      previous_values? false
    end

    publish_all :update, [:org_id, :opportunity_id] do
      previous_values? false
    end

    publish_all :destroy, [:org_id, :opportunity_id] do
      previous_values? false
    end
  end

  multitenancy do
    strategy :attribute
    attribute :org_id
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :role, :contact_role do
      public? true
    end

    timestamps()
  end

  relationships do
    belongs_to :org, Boring.Orgs.Org

    belongs_to :contact, Boring.Acquisitions.Contact do
      public? true
      allow_nil? false
    end

    belongs_to :opportunity, Opportunity do
      allow_nil? false
    end
  end

  calculations do
    calculate :name, :string, expr(contact.name)
    calculate :email, :ci_string, expr(contact.email)
    calculate :phone_number, :string, expr(contact.phone_number)
    calculate :skip_traced?, :boolean, expr(contact.skip_traced?)
  end

  identities do
    identity :tenant_unique_opportunity_contact, [:contact_id, :opportunity_id]
  end
end
