defmodule Boring.Acquisitions.Contact do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Acquisitions,
    extensions: [AshArchival.Resource]

  alias Boring.Accounts.User

  postgres do
    table "contacts"
    repo Boring.Repo

    references do
      reference :org, on_delete: :delete
      reference :created_by, on_delete: :restrict
      reference :updated_by, on_delete: :restrict
      reference :global_contact, on_delete: :nilify
    end

    identity_wheres_to_sql tenant_unique_email: "\"skip_traced?\" = false",
                           tenant_unique_phone_number: "\"skip_traced?\" = false"

    base_filter_sql "(archived_at IS NULL)"
  end

  archive do
    exclude_read_actions [:read_archived, :read_all]
    base_filter? true
  end

  actions do
    defaults [:read, :destroy, create: :*]

    read :read_all

    read :read_archived do
      filter expr(not is_nil(archived_at))
    end

    read :read_editable do
      filter expr(not skip_traced?)
    end

    create :create_skip_traced_contact do
      accept [:name, :email, :phone_number, :address, :global_contact_id]
      change set_attribute(:skip_traced?, true)
    end

    update :update do
      primary? true
      accept [:*]

      require_atomic? false
    end

    update :unarchive do
      change set_attribute(:archived_at, nil)
      atomic_upgrade_with :read_archived
    end
  end

  preparations do
    prepare build(load: [address: [:formatted]])
  end

  changes do
    change relate_actor(:created_by, allow_nil?: true), on: :create
    change relate_actor(:updated_by, allow_nil?: true)
  end

  validations do
    validate present([:name, :email, :phone_number, :address], at_least: 1)
  end

  multitenancy do
    strategy :attribute
    attribute :org_id
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :name, :string do
      public? true

      constraints trim?: true
    end

    attribute :email, :ci_string do
      public? true

      constraints max_length: 160, trim?: true
    end

    attribute :phone_number, :string do
      public? true
    end

    attribute :address, Boring.Locations.Address do
      public? true
    end

    attribute :skip_traced?, :boolean do
      default false
    end

    timestamps()
  end

  relationships do
    belongs_to :created_by, User do
      public? true
    end

    belongs_to :updated_by, User do
      public? true
    end

    belongs_to :org, Boring.Orgs.Org

    belongs_to :global_contact, Boring.Services.GlobalContact
  end

  identities do
    identity :tenant_unique_email, [:email], where: expr(not skip_traced?)
    identity :tenant_unique_phone_number, [:phone_number], where: expr(not skip_traced?)
    identity :tenant_unique_global_contact, [:global_contact_id]
  end
end
