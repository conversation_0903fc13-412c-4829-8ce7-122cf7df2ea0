defmodule Boring.Acquisitions.Changes.FetchAndSaveAerialImage do
  @moduledoc """
  This change attempts to fetch an aerial image from Mapbox and save it to the filesystem/object storage.
  If unable to fetch the image, it logs an error and returns the changeset unchanged.
  This is because the aerial image is not a critical piece of data and can be fetched later.
  """
  use Ash.Resource.Change

  alias Boring.Api.Mapbox

  require Logger

  @impl true
  def change(changeset, opts, _context) do
    zoom = opts[:zoom] || 18
    width = opts[:width] || 1280
    height = opts[:height] || 720
    style_id = opts[:style_id] || "satellite-v9"

    latitude = Ash.Changeset.get_attribute(changeset, :latitude)
    longitude = Ash.Changeset.get_attribute(changeset, :longitude)

    if latitude && longitude do
      mapbox_attrs = %{
        latitude: latitude,
        longitude: longitude,
        zoom: zoom,
        width: width,
        height: height,
        style_id: style_id
      }

      filename = generate_image_filename(mapbox_attrs)

      with {:ok, image_binary} <- fetch_mapbox_aerial_image(mapbox_attrs),
           :ok <- save_file(filename, image_binary) do
        Ash.Changeset.force_change_attribute(changeset, :aerial_image_path, filename)
      else
        {:error, reason} ->
          Logger.error("Error fetching aerial image: #{inspect(reason)}")
          changeset
      end
    else
      Logger.warning("Google place does not have latitude and longitude, skipping aerial image fetch")

      changeset
    end
  end

  defp fetch_mapbox_aerial_image(attrs) do
    with {:ok, mapbox_env} <- Mapbox.build(attrs),
         {:ok, mapbox_env} <- Mapbox.get_static_image(mapbox_env) do
      {:ok, mapbox_env.response.image}
    end
  end

  defp save_file(filename, image) do
    io().write(filename, image)
  end

  defp generate_image_filename(attrs) do
    lat = format_float(attrs.latitude)
    long = format_float(attrs.longitude)

    "mapbox-aerial-#{attrs.style_id}-lat_#{lat}-long_#{long}-z_#{attrs.zoom}-w_#{attrs.width}-h_#{attrs.height}.jpg"
  end

  defp format_float(float) do
    float
    |> Float.to_string()
    |> String.replace(".", "_")
  end

  defp io do
    Application.get_env(:boring, :mapbox)[:io_backend] || Boring.Api.Mapbox.FileOps
  end
end
