defmodule Boring.Acquisitions.Changes.ProcessOpenSkipTraceRequests do
  @moduledoc false
  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, context) do
    Ash.Changeset.after_action(changeset, fn _changeset, opportunity ->
      opportunity =
        Ash.load!(opportunity, :open_service_requests, Ash.Context.to_opts(context))

      processed_requests =
        opportunity.open_service_requests
        |> Enum.filter(fn req -> req.type == :skiptrace end)
        |> Enum.map(&Boring.Services.process_skip_trace(&1, Ash.Context.to_opts(context)))

      if Enum.all?(processed_requests, &match?({:ok, _}, &1)) do
        {:ok, opportunity}
      else
        {:error, "Could not process open service requests"}
      end
    end)
  end
end
