defmodule Boring.Acquisitions.OpportunityExclusion do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Acquisitions

  alias Boring.Acquisitions.Opportunity
  alias Boring.Google

  postgres do
    table "opportunity_exclusions"
    repo Boring.Repo
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end

  preparations do
    prepare build(load: [:matching_google_places, :matching_opportunities])
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :name, :string, allow_nil?: false, public?: true

    timestamps()
  end

  relationships do
    has_many :places, Google.Place do
      no_attributes? true
      filter expr(ilike(parent(name), "%#{business_name}%"))
    end

    has_many :opportunities, Opportunity do
      no_attributes? true
      filter expr(ilike(parent(name), "%#{name}%"))
    end
  end

  calculations do
    calculate :matching_google_places, :integer, expr(count(places))

    calculate :matching_opportunities, :integer, expr(count(opportunities))
  end
end
