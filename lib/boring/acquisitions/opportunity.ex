defmodule Boring.Acquisitions.Opportunity do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Acquisitions,
    extensions: [AshOban]

  alias Boring.Acquisitions.Changes.FetchAndSaveAerialImage
  alias Boring.Acquisitions.Contact
  alias Boring.Acquisitions.OpportunityExclusion
  alias Boring.Acquisitions.OrgOpportunity
  alias Boring.Google
  alias Boring.Locations
  alias Boring.Orgs
  alias Boring.Services.ServiceRequest

  postgres do
    table "opportunities"
    repo Boring.Repo

    references do
      reference :google_place, on_delete: :restrict
      reference :state, on_delete: :restrict
    end
  end

  oban do
    triggers do
      trigger :fetch_aerial_image do
        queue :aerial_images
        action :fetch_aerial_image
        where expr(is_nil(aerial_image_path))
        stream_batch_size 50
        state :active
        worker_module_name Boring.Acquisitions.Opportunity.AshOban.Worker.FetchAerialImage
        scheduler_module_name Boring.Acquisitions.Opportunity.AshOban.Scheduler.FetchAerialImage
      end
    end
  end

  actions do
    defaults [:read, :destroy]

    default_accept [
      :name,
      :full_address,
      :city,
      :street,
      :postal_code,
      :latitude,
      :longitude,
      :website,
      :map_url,
      :phone_number,
      :google_place_id,
      :state_id,
      :raw_skiptrace_data
    ]

    read :read_off_market_candidates do
      argument :ids, {:array, :uuid}, allow_nil?: false

      prepare build(
                load: [:assigned_to_org?, :google_place],
                sort: [
                  {:assigned_to_org?, :asc},
                  {"google_place.is_business_claimed", :asc},
                  {"google_place.website", :asc_nils_first},
                  {"google_place.review_count", :asc},
                  {"google_place.review_score", :asc}
                ]
              )

      filter expr(id in ^arg(:ids))
      filter expr(matches_exclusions? == false)
    end

    read :with_open_service_requests do
      filter expr(exists(open_service_requests))
      prepare build(load: :open_service_requests)
    end

    create :create do
      primary? true

      upsert? true
      upsert_identity :google_place_id

      upsert_fields {:replace_all_except, [:id, :aerial_image_path, :inserted_at, :google_place_id]}
    end

    update :update do
      primary? true
      require_atomic? false
    end

    update :assign_to_org do
      require_atomic? false
      argument :org_id, :uuid, allow_nil?: false
      change manage_relationship(:org_id, :orgs, type: :append)
    end

    update :fetch_aerial_image do
      require_atomic? false

      argument :primary_key, :map

      change FetchAndSaveAerialImage
    end

    update :update_global_contacts do
      require_atomic? false
      argument :global_contacts, {:array, :map}, default: []

      change manage_relationship(:global_contacts, type: :direct_control)
      change Boring.Acquisitions.Changes.ProcessOpenSkipTraceRequests
    end

    update :update_skip_traced_contacts do
      require_atomic? false
      argument :contacts, {:array, :map}, allow_nil?: false

      change manage_relationship(:contacts, :skip_traced_contacts,
               type: :direct_control,
               on_no_match: {:create, :create_skip_traced_contact},
               use_identities: [:tenant_unique_global_contact]
             )
    end
  end

  validations do
    validate {Boring.Utils.ValidatePhoneNumber, attribute: :phone_number, message: "is not a valid phone number"}
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :name, :string, allow_nil?: false
    attribute :full_address, :string, allow_nil?: false
    attribute :city, :string, allow_nil?: false
    attribute :street, :string, allow_nil?: false
    attribute :postal_code, :string, allow_nil?: false
    attribute :latitude, :float, allow_nil?: false
    attribute :longitude, :float, allow_nil?: false
    attribute :website, :string
    attribute :map_url, :string
    attribute :aerial_image_path, :string, allow_nil?: true
    attribute :phone_number, :string

    attribute :raw_skiptrace_data, :map do
      description "Raw data from the skip trace service"
      select_by_default? false
    end

    timestamps(type: :utc_datetime)
  end

  relationships do
    belongs_to :google_place, Google.Place, allow_nil?: false

    belongs_to :state, Locations.State, allow_nil?: false

    # has_one :state, Locations.State do
    #   no_attributes? true
    #   filter expr(code == parent(google_state) or name == parent(google_state))
    # end

    many_to_many :orgs, Orgs.Org, through: OrgOpportunity
    has_many :org_opportunities, OrgOpportunity

    has_many :opportunity_exclusions, OpportunityExclusion do
      no_attributes? true
      filter expr(ilike(parent(name), "%#{name}%"))
    end

    has_many :files, Boring.Acquisitions.CloudFile do
      filter expr(status == :processed)
    end

    has_many :opportunity_contacts, Boring.Acquisitions.OpportunityContact do
      sort updated_at: :desc
    end

    many_to_many :contacts, Contact, join_relationship: :opportunity_contacts

    many_to_many :skip_traced_contacts, Contact do
      join_relationship :opportunity_contacts
      filter expr(skip_traced?)
    end

    has_many :global_contacts, Boring.Services.GlobalContact

    has_one :demographics, Boring.Demographics.Demographic do
      no_attributes? true
      filter expr(state_id == parent(state_id) and city == parent(city))
    end

    has_many :skip_traces, ServiceRequest do
      filter expr(type == :skiptrace)
      sort updated_at: :desc
    end

    has_one :latest_completed_skip_trace, ServiceRequest do
      filter expr(type == :skiptrace and not open?)
      sort updated_at: :desc
    end

    has_many :open_service_requests, ServiceRequest do
      filter expr(open?)
      sort updated_at: :desc
    end
  end

  calculations do
    calculate :matches_exclusions?, :boolean, expr(exists(opportunity_exclusions, true))
    calculate :google_review_count, :integer, expr(google_place.review_count), public?: true
    calculate :google_review_score, :float, expr(google_place.review_score), public?: true
    calculate :google_categories, {:array, :string}, expr(google_place.categories)
    calculate :google_review_tags, {:array, :string}, expr(google_place.review_tags)

    calculate :google_review_distribution,
              Google.Place.ReviewDistribution,
              expr(google_place.reviews_distribution)

    calculate :google_opening_hours,
              {:array, Google.Place.OpeningHour},
              expr(google_place.opening_hours)

    calculate :display_phone_number,
              :string,
              {Boring.Utils.DisplayPhoneNumber, attribute_path: :phone_number}

    calculate :assigned_to_org?, :boolean, expr(exists(org_opportunities, true))
  end

  aggregates do
    max :last_assigned_at, :org_opportunities, :inserted_at

    exists :skip_trace_in_progress?, :skip_traces do
      filter expr(open?)
    end
  end

  identities do
    identity :google_place_id, [:google_place_id]
  end
end
