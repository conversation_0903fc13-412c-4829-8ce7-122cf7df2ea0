defmodule Boring.Acquisitions.CloudFile do
  @moduledoc """
    Used for storing metadata about files related to acquisitions in s3-compatible storage.
  """
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Acquisitions

  alias Boring.Api.S3Client

  postgres do
    table "cloud_files"
    repo Boring.Repo

    references do
      reference :org, on_delete: :restrict
      reference :opportunity, on_delete: :restrict
    end
  end

  actions do
    defaults [:read]

    create :create do
      primary? true
      accept [:opportunity_id, :remote_name, :local_name]
    end

    update :upload_complete do
      require_atomic? true
      primary? true
      accept [:local_name, :remote_name]

      change set_attribute(:status, :processed)
    end

    destroy :destroy do
      require_atomic? false
      primary? true

      change before_transaction(fn changeset, _ctx ->
               remote_name = Ash.Changeset.get_attribute(changeset, :remote_name)

               case delete_cloud_object(remote_name) do
                 {:ok, _} ->
                   changeset

                 {:error, reason} ->
                   Ash.Changeset.add_error(changeset,
                     field: :remote_name,
                     message: inspect(reason)
                   )
               end
             end)
    end
  end

  multitenancy do
    strategy :attribute
    attribute :org_id
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :remote_name, :string, allow_nil?: false
    attribute :local_name, :string, allow_nil?: false

    attribute :status, :atom do
      allow_nil? false
      default :pending

      constraints one_of: [
                    :pending,
                    :processed
                  ]
    end

    timestamps()
  end

  relationships do
    belongs_to :org, Boring.Orgs.Org
    belongs_to :opportunity, Boring.Acquisitions.Opportunity
  end

  calculations do
    calculate :presigned_get_url, :string do
      calculation fn records, _opts ->
        Enum.map(records, &presigned_get_url/1)
      end
    end
  end

  def presigned_get_url(record) do
    if record.status == :processed do
      safe_local_name = URI.encode(record.local_name)

      # means that the file downloaded will have the original name
      # not the name we use to store it in S3/Tigris
      query_params = [
        {"response-content-disposition", "attachment; filename=#{safe_local_name}"}
      ]

      :s3
      |> ExAws.Config.new()
      |> S3Client.presigned_url(
        :get,
        upload_bucket(),
        record.remote_name,
        expires_in: 3600,
        query_params: query_params
      )
      |> case do
        {:ok, url} -> url
        {:error, _} -> nil
      end
    end
  end

  defp delete_cloud_object(key) do
    config_overrides = [
      access_key_id: PetalPro.config([:tigris, :access_key_id]),
      secret_access_key: PetalPro.config([:tigris, :secret_access_key])
    ]

    S3Client.delete_object(
      upload_bucket(),
      key,
      config_overrides
    )
  end

  defp upload_bucket do
    PetalPro.config([:file_upload, :upload_bucket])
  end
end
