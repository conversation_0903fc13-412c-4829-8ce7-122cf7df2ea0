defmodule Boring.Acquisitions.Types.OpportunityStatus do
  @moduledoc false
  use Gettext, backend: PetalProWeb.Gettext

  use Ash.Type.Enum,
    values: [
      not_in_buybox: [
        description: "Not interested in the opportunity",
        label: gettext("Not in buybox")
      ],
      # lead, closed
      canceled: [
        description: "Deal no longer available or something fell through",
        label: gettext("Fall through / Canceled")
      ],
      # deal, closed
      acquired: [description: "Business acquired", label: gettext("Acquired")],
      # deal, open
      due_diligience_period: [
        description: "Due diligience period to verify the business is as advertised",
        label: gettext("Due diligience period")
      ],
      # deal, open
      interested: [description: "Interested in an offer", label: gettext("Interested")],
      interested_later: [
        description: "May be interested later",
        label: gettext("Interested later")
      ],
      # lead, closed
      just_bought: [
        description: "Just bought the business, not interested",
        label: gettext("Just bought")
      ],
      # lead, open
      new: [description: "Newly assigned opportunity"],
      # lead, open
      no_answer: [description: "Tried to contact, no answer", label: gettext("No answer")],
      # lead, closed
      # lead, closed
      not_for_sale: [description: "Not interested in selling", label: gettext("Not for sale")],
      # deal, open
      offer_accepted: [description: "Offer accepted", label: gettext("Offer accepted")],
      # deal, open
      offer_made: [description: "Offer submitted to the owner", label: gettext("Offer made")],
      # deal, open
      offer_rejected: [
        description: "Offer rejected.  Maybe submit another or close the deal",
        label: gettext("Offer rejected")
      ],
      # lead, closed
      on_the_market: [description: "Business is on the market", label: gettext("On the market")],
      # deal, open
      pending_close: [
        description: "Due diligence is done and pending close",
        label: gettext("Pending close")
      ],
      # lead, closed
      wants_too_much: [description: "Owner's price is too high", label: gettext("Wants too much")]
    ]
end
