defmodule Boring.Acquisitions.Generator do
  @moduledoc false
  use Ash.Generator

  import StreamData, only: [repeatedly: 1]
  import Util.Generator

  alias Boring.Acquisitions.Contact
  alias Boring.Acquisitions.OpportunityContact
  alias Boring.Acquisitions.OpportunityExclusion
  alias Boring.Orgs.Generator
  alias PetalPro.Accounts.UserSeeder

  def opportunity_contact(opts \\ []) do
    actor = opts[:actor] || once(:contact_creator, &UserSeeder.random_user/0)

    changeset_generator(OpportunityContact, :create,
      defaults: [],
      overrides: opts,
      actor: actor,
      tenant: opts[:tenant]
    )
  end

  def contact(opts \\ []) do
    if opts[:skip_traced?] do
      actor =
        opts[:actor] ||
          :contact_creator |> once(&UserSeeder.random_user/0) |> Enum.at(0) |> Map.get(:id)

      seed_generator(
        %Contact{
          skip_traced?: true,
          name: repeatedly(&Faker.Person.name/0),
          email: repeatedly(&Faker.Internet.email/0),
          phone_number: repeatedly(&random_us_phone_number/0),
          org_id: opts[:org_id],
          created_by_id: actor,
          updated_by_id: actor
        },
        overrides: opts
      )
    else
      raise "Not supported yet"
    end
  end

  def org_opportunity(overrides \\ []) do
    org_id =
      if overrides[:org_id] do
        overrides[:org_id]
      else
        org =
          generate(Generator.org())

        org.id
      end

    opportunity_id =
      repeatedly(fn -> overrides[:opportunity_id] || generate(opportunity()).id end)

    seed_generator(%Boring.Acquisitions.OrgOpportunity{
      status: overrides[:status] || :new,
      org_id: org_id,
      opportunity_id: opportunity_id,
      inserted_at: overrides[:inserted_at] || DateTime.utc_now()
    })
  end

  def opportunity_exclusion(overrides \\ []) do
    changeset_generator(
      OpportunityExclusion,
      :create,
      defaults: [
        name: repeatedly(&Faker.Company.name/0)
      ],
      overrides: overrides,
      authorize?: false
    )
  end

  def opportunity(overrides \\ []) do
    google_place = overrides[:google_place] || generate(Boring.Google.Generator.place())

    google_place =
      Boring.Google.convert_to_opportunity!(google_place,
        load: [related_opportunity: [state: [:name]]],
        authorize?: false
      )

    if overrides[:org_id] do
      Boring.Acquisitions.assign_opportunity!(
        %{opportunity_id: google_place.related_opportunity.id},
        tenant: overrides[:org_id],
        authorize?: false
      )
    end

    google_place.related_opportunity
  end
end
