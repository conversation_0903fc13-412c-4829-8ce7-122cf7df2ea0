defmodule Boring.Google do
  @moduledoc false
  use Ash.Domain,
    extensions: [AshPhoenix]

  resources do
    resource __MODULE__.PlaceExtract do
      define :list_place_extracts, action: :read
      define :list_active_place_extracts
      define :get_place_extract, action: :read, get_by: :id
      define :get_place_extract_by_actor_run_id, action: :read, get_by: :last_actor_run_id
      define :get_place_extract_by_state, action: :get_by_state, args: [:state]
      define :start_extract, action: :start_extract, args: [:extract]
      define :ingest_extract_data, action: :ingest, args: [:extract]
      define :create_place_extract, action: :create
      define :update_place_extract, action: :update
      define :delete_place_extract, action: :destroy
    end

    resource __MODULE__.Place do
      define :get_place, action: :read, get_by: :id
      define :list_places, action: :read
      define :list_places_by_place_ids, action: :list_by_place_ids, args: [:place_ids]
      define :list_opportunity_candidates
      define :convert_to_opportunity, action: :convert_to_opportunity

      define :create_place do
        action :create

        default_options bulk_options: [
                          stop_on_error?: false,
                          return_errors?: true,
                          notify?: true
                        ]
      end

      define :create_place_from_google do
        action :create_from_google

        default_options bulk_options: [
                          stop_on_error?: false,
                          return_errors?: true,
                          notify?: true
                        ]
      end

      define :update_place, action: :update
      define :delete_place, action: :destroy
    end

    resource __MODULE__.Place.Review
  end
end
