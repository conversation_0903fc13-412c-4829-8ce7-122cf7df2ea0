defmodule Boring.Orgs do
  @moduledoc false
  use Ash.Domain,
    extensions: [AshPhoenix]

  alias Boring.Types.UserOrId

  resources do
    resource __MODULE__.Org do
      define :list_orgs, action: :read
      define :get_org, action: :read, get_by: :slug
      define :get_org_by_id, action: :read, get_by: :id
      define :create_org, action: :create
      define :create_personal_org
      define :update_org, action: :update
      define :delete_org, action: :destroy
    end

    resource __MODULE__.Membership do
      define :create_membership do
        action :create
        args [:user, :role]

        custom_input :user, UserOrId do
          allow_nil? false

          transform do
            to :user_id
            using &UserOrId.to_id/1
          end
        end
      end
    end

    resource __MODULE__.Invitation
  end
end
