defmodule Boring.Features.Feature do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Features,
    authorizers: [Ash.Policy.Authorizer]

  postgres do
    table "features"
    repo Boring.Repo
  end

  actions do
    defaults [:read, :destroy]

    read :read_enabled do
      filter expr(enabled? == true)
    end

    read :read_disabled do
      filter expr(enabled? == false)
    end

    create :create do
      accept [:name, :enabled?]
      primary? true
    end

    update :update do
      accept [:name, :enabled?]
      primary? true
    end
  end

  policies do
    bypass AshOban.Checks.AshObanInteraction do
      authorize_if always()
    end

    bypass actor_attribute_equals(:role, :admin) do
      authorize_if always()
    end

    policy action_type(:read) do
      authorize_if always()
    end

    policy action_type([:create, :update, :destroy]) do
      forbid_if always()
    end
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :name, :string, allow_nil?: false
    attribute :enabled?, :boolean, default: false

    timestamps()
  end

  identities do
    identity :unique_name, [:name]
  end
end
