defmodule Boring.Services.ServiceRequest do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Services,
    authorizers: Ash.Policy.Authorizer,
    notifiers: [Ash.Notifier.PubSub],
    extensions: [<PERSON><PERSON><PERSON>]

  alias Boring.Acquisitions.Opportunity
  alias Boring.Checks.Simple.IsTenantActiveSubscriber
  alias Boring.Orgs.Org
  alias Boring.Services.Changes.CreateMeterEventHook

  postgres do
    table "service_requests"
    repo Boring.Repo

    references do
      reference :org, on_delete: :restrict
      reference :opportunity, on_delete: :restrict
      reference :created_by, on_delete: :restrict
    end
  end

  oban do
    triggers do
      trigger :run_remote_skip_trace do
        queue :skiptrace
        action :run_remote_skip_trace
        scheduler_cron false
        worker_module_name Boring.Services.ServiceRequest.AshOban.Worker.RunRemoteSkipTrace
        scheduler_module_name Boring.Services.ServiceRequest.AshOban.Scheduler.RunRemoteSkipTrace
      end
    end
  end

  actions do
    defaults [:read]

    read :read_open_by_type do
      argument :type, :service_request_type, allow_nil?: false

      filter expr(type == ^arg(:type))
      filter expr(open? == true)
    end

    read :read_by_org_opportunity do
      argument :org_opportunity_id, :uuid, allow_nil?: false

      filter expr(exists(opportunity.org_opportunities, id == ^arg(:org_opportunity_id)))
    end

    create :create do
      primary? true
      accept [:opportunity_id, :created_by_id, :type]

      change relate_actor(:created_by),
        where: absent(:created_by_id),
        only_when_valid?: true

      change Boring.Services.Changes.ProcessSkipTraceRequest,
        where: attribute_equals(:type, :skiptrace)
    end

    update :update_status do
      require_atomic? false

      accept [:status]
    end

    update :process_skip_trace do
      require_atomic? false

      change Boring.Services.Changes.CopyGlobalContacts
    end

    update :enqueue_remote_skip_trace do
      require_atomic? false

      change run_oban_trigger(:run_remote_skip_trace)
    end

    update :run_remote_skip_trace do
      require_atomic? false

      change Boring.Services.Changes.RunRemoteSkipTraceRequest
    end
  end

  policies do
    bypass AshOban.Checks.AshObanInteraction do
      authorize_if always()
    end

    bypass actor_attribute_equals(:role, :admin) do
      authorize_if always()
    end

    policy action_type(:read) do
      authorize_if relates_to_actor_via([:org, :users])
    end

    policy [action_type(:create), IsTenantActiveSubscriber] do
      authorize_if Boring.Services.Checks.Simple.NoActiveServiceRequestForType
    end

    policy [action(:enqueue_remote_skip_trace), IsTenantActiveSubscriber] do
      authorize_if always()
    end

    policy [action(:process_skip_trace), IsTenantActiveSubscriber] do
      authorize_if always()
    end
  end

  pub_sub do
    module PetalProWeb.Endpoint

    prefix "service_requests"

    publish :update_status, [:org_id, :opportunity_id] do
      event "service_request_closed"

      filter fn notification ->
        notification.data.status == :done
      end

      transform fn notification ->
        Map.take(notification.data, [:status, :type])
      end
    end
  end

  changes do
    change CreateMeterEventHook,
      only_when_valid?: true,
      on: :update,
      where: [changing(:status), attribute_equals(:status, :done)]
  end

  multitenancy do
    strategy :attribute
    attribute :org_id
    global? true
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :type, :service_request_type, allow_nil?: false

    attribute :status, :service_request_status do
      allow_nil? false
      default :new
      public? true
    end

    timestamps()
  end

  relationships do
    belongs_to :org, Org
    belongs_to :opportunity, Opportunity, allow_nil?: false
    belongs_to :created_by, Boring.Accounts.User, allow_nil?: false
  end

  calculations do
    calculate :open?, :boolean, expr(status in [:new, :in_progress]), public?: true
  end
end
