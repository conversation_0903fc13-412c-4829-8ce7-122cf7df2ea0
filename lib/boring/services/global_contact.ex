defmodule Boring.Services.GlobalContact do
  @moduledoc """
  Used to fulfill skip trace service requests.
  """

  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Services

  alias Boring.Accounts.User

  postgres do
    table "global_contacts"
    repo Boring.Repo

    references do
      reference :opportunity, on_delete: :delete
      reference :created_by, on_delete: :restrict
      reference :updated_by, on_delete: :restrict
    end
  end

  actions do
    defaults [:read, :create, :destroy]
    default_accept [:name, :email, :phone_number, :address, :opportunity_id]

    update :update do
      primary? true
      require_atomic? false
    end
  end

  changes do
    change relate_actor(:created_by, allow_nil?: true), on: :create
    change relate_actor(:updated_by, allow_nil?: true)
  end

  validations do
    validate present([:name, :email, :phone_number, :address], at_least: 1),
      message: "must provide at least one of name, email, phone number, or address"
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :name, :string do
      constraints trim?: true, min_length: 1, max_length: 160
    end

    attribute :address, Boring.Locations.Address

    attribute :email, :ci_string do
      constraints max_length: 160, trim?: true
    end

    attribute :phone_number, :string

    timestamps()
  end

  relationships do
    belongs_to :opportunity, Boring.Acquisitions.Opportunity do
      allow_nil? false
    end

    belongs_to :created_by, User
    belongs_to :updated_by, User
  end
end
