defmodule Boring.Services.Checks.Simple.NoActiveServiceRequestForType do
  @moduledoc false
  use Ash.Policy.SimpleCheck

  require Ash.Query

  def describe(_) do
    "doesn't have an active service request for this type"
  end

  def match?(_actor, %{subject: changeset}, _opts) do
    request_type = Ash.Changeset.get_attribute(changeset, :type)
    opportunity_id = Ash.Changeset.get_attribute(changeset, :opportunity_id)

    active_request_of_same_type? =
      Boring.Services.ServiceRequest
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(type == ^request_type)
      |> Ash.Query.filter(opportunity_id == ^opportunity_id)
      |> Ash.Query.filter(status in [:new, :in_progress])
      |> Ash.exists?(authorize?: false, tenant: changeset.tenant)

    not active_request_of_same_type?
  end

  def match?(_, _, _), do: false
end
