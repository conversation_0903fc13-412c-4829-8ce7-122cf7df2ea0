defmodule Boring.Services.Checks.Simple.FeatureEnabled do
  @moduledoc false
  use Ash.Policy.SimpleCheck

  def describe(_) do
    "feature flag for this service request type is enabled"
  end

  def match?(_actor, %{subject: changeset}, _opts) do
    request_type = Ash.Changeset.get_attribute(changeset, :type)

    case Boring.Features.get_feature!("service_request_#{request_type}") do
      %{enabled?: enabled} -> enabled
      _ -> false
    end
  end

  def match?(_, _, _), do: false
end
