defmodule Boring.Services.Changes.ProcessSkipTraceRequest do
  @moduledoc false
  use Ash.Resource.Change

  @skip_trace_validity_days 90

  @impl true
  def change(changeset, _opts, context) do
    opts =
      context
      |> Ash.Context.to_opts()
      |> Keyword.put(:authorize?, false)

    Ash.Changeset.after_action(changeset, fn _changeset, request ->
      request =
        Ash.load!(
          request,
          [opportunity: :latest_completed_skip_trace],
          opts
        )

      request
      |> Ash.Changeset.for_update(:enqueue_remote_skip_trace, %{})
      |> Ash.update(opts)

      # If opportunity was last skip traced less than 90 days ago
      # if still_valid?(request.opportunity.latest_completed_skip_trace) do
      #   request
      #   |> Ash.Changeset.for_update(:process_skip_trace, %{}, Ash.Context.to_opts(context))
      #   |> Ash.update()
      # else

      # end
    end)
  end

  def still_valid?(nil), do: false

  def still_valid?(%{updated_at: updated_at}) do
    result =
      updated_at
      |> DateTime.add(@skip_trace_validity_days, :day)
      |> DateTime.compare(DateTime.utc_now())

    result == :gt
  end
end
