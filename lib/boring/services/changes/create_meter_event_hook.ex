defmodule Boring.Services.Changes.CreateMeterEventHook do
  @moduledoc """
  An Ash Resource Change that creates a meter event when a service request is completed.

  This change is used in the `update` action of the `ServiceRequest` resource when
  the status is changed to `:done`. It performs the following steps:

  1. Loads the org and customer relationships
  2. Creates a meter event for the customer with the service request type and a value of 1
  3. Returns the original record if successful, or the error if creation fails

  This hook enables automatic usage-based billing for completed service requests.
  """
  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    Ash.Changeset.after_action(changeset, fn _changeset, record ->
      record = Ash.load!(record, [org: [:customer]], authorize?: false)

      case Boring.Billing.Meters.create_meter_event(record.org.customer, record.type, 1, authorize?: false) do
        {:ok, _meter_event} ->
          {:ok, record}

        {:error, _reason} = error ->
          error
      end
    end)
  end

  @impl true
  def batch_change(changesets, opts, context) do
    Enum.map(changesets, &change(&1, opts, context))
  end
end
