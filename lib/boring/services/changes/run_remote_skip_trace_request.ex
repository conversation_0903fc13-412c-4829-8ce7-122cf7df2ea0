defmodule Boring.Services.Changes.RunRemoteSkipTraceRequest do
  @moduledoc false
  use Ash.Resource.Change

  alias Boring.Api.SkiptraceService.Result

  @loads [
    opportunity: [
      :name,
      :street,
      :city,
      :postal_code,
      state: [:code]
    ]
  ]

  @impl true
  def change(changeset, _opts, context) do
    changeset
    |> run_skiptrace(context)
    |> maybe_set_status_to_done(context)
  end

  defp maybe_set_status_to_done(changeset, context) do
    opts =
      context
      |> Ash.Context.to_opts()
      |> Keyword.put(:authorize?, false)

    Ash.Changeset.after_action(changeset, fn _changeset, request ->
      if request.status == :done do
        {:ok, request}
      else
        request
        |> Ash.Changeset.for_update(:update_status, %{status: :done})
        |> Ash.update(opts)
      end
    end)
  end

  defp run_skiptrace(changeset, context) do
    Ash.Changeset.before_action(changeset, fn changeset ->
      opts =
        context
        |> Ash.Context.to_opts()
        |> Keyword.put(:authorize?, false)

      request =
        Ash.load!(changeset.data, @loads, opts)

      case Boring.Api.SkiptraceService.run_skiptrace(request) do
        {:ok, %Result{contacts: results, raw_result: raw_result}} when results != [] ->
          Boring.Acquisitions.update_opportunity(
            request.opportunity,
            %{raw_skiptrace_data: raw_result},
            opts
          )

          global_contacts = transform_results_to_contacts(results)

          Boring.Acquisitions.update_opportunity_global_contacts(
            request.opportunity,
            %{global_contacts: global_contacts},
            opts
          )

          changeset

        {:ok, %Result{contacts: [], raw_result: raw_result}} when not is_nil(raw_result) ->
          Boring.Acquisitions.update_opportunity(
            request.opportunity,
            %{raw_skiptrace_data: raw_result},
            opts
          )

          changeset

        _ ->
          changeset
      end
    end)
  end

  def transform_results_to_contacts(results) do
    Enum.map(results, fn result ->
      address =
        if result.address && address_valid?(result.address) do
          result.address
        end

      %{
        name: result.name,
        email: Enum.at(result.emails, 0),
        address: address,
        phone_number: Enum.at(result.phone_numbers, 0)
      }
    end)
  end

  defp address_valid?(address_parts) do
    not Enum.any?(address_parts, fn {_, value} ->
      is_nil(value) or value == ""
    end)
  end
end
