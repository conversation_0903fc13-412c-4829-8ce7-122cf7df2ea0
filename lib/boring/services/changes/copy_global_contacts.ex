defmodule Boring.Services.Changes.CopyGlobalContacts do
  @moduledoc false
  use Ash.Resource.Change

  require Logger

  @impl true
  def change(changeset, _opts, context) do
    Ash.Changeset.after_action(changeset, fn _changeset, request ->
      opts =
        Ash.Context.to_opts(context, authorize?: false, tenant: request.org_id)

      request =
        Ash.load!(request, [:org, opportunity: :global_contacts], opts)

      contact_data =
        Enum.map(request.opportunity.global_contacts, fn contact ->
          contact
          |> Map.take([:name, :email, :phone_number, :address])
          |> Map.put(:global_contact_id, contact.id)
        end)

      request.opportunity
      |> Ash.Changeset.for_update(
        :update_skip_traced_contacts,
        %{contacts: contact_data},
        opts
      )
      |> Ash.update(opts)
      |> case do
        {:ok, _opportunity} ->
          request
          |> Ash.Changeset.for_update(:update_status, %{status: :done})
          |> Ash.update(opts)

        {:error, _record} ->
          Logger.error("Could not update tenanted skip trace contacts")
          {:error, "Could not update tenanted skip trace contacts"}
      end
    end)
  end
end
