defmodule Boring.Services.Generator do
  @moduledoc false
  use Ash.Generator

  import StreamData, only: [repeatedly: 1]

  alias Boring.Acquisitions.Generator, as: AcquisitionsGen
  alias Boring.Orgs.Generator, as: OrgsGen
  alias Boring.Services.ServiceRequest
  alias Boring.Services.Types.ServiceRequestStatus
  alias Boring.Services.Types.ServiceRequestType
  alias PetalPro.Accounts.UserSeeder

  def service_request(overrides \\ []) do
    tenant =
      overrides[:tenant] ||
        once(:default_tenant_id, fn -> generate(OrgsGen.subscribed_org()).id end)

    changeset_generator(
      ServiceRequest,
      :create,
      uses: %{
        opportunity_generator: AcquisitionsGen.opportunity(),
        user_id: UserSeeder.confirmed_user().id
      },
      defaults: fn inputs ->
        opportunity = generate(inputs.opportunity_generator)

        [
          type: repeatedly(fn -> Enum.random(ServiceRequestType.values()) end),
          status: repeatedly(fn -> Enum.random(ServiceRequestStatus.values()) end),
          opportunity_id: opportunity.id,
          created_by_id: inputs.user_id
        ]
      end,
      overrides: Keyword.take(overrides, [:type, :status, :opportunity_id]),
      tenant: tenant
    )
  end

  @doc """
  Creates a service request by inserting directly into the database, bypassing Ash actions.
  This is useful for tests that need to create service requests without triggering
  automatic processing like skip trace execution.
  """
  def service_request_seed(overrides \\ []) do
    tenant = overrides[:org_id] || overrides[:tenant] || generate(OrgsGen.subscribed_org()).id

    seed_generator(
      fn inputs ->
        opportunity = generate(inputs.opportunity_generator)
        now = DateTime.utc_now()

        %ServiceRequest{
          id: overrides[:id] || Ash.UUID.generate(),
          type: overrides[:type] || :skiptrace,
          status: overrides[:status] || :new,
          opportunity_id: overrides[:opportunity_id] || opportunity.id,
          created_by_id: overrides[:created_by_id] || inputs.user_id,
          org_id: tenant,
          inserted_at: overrides[:inserted_at] || now,
          updated_at: overrides[:updated_at] || now
        }
      end,
      uses: %{
        opportunity_generator: AcquisitionsGen.opportunity(),
        user_id: UserSeeder.confirmed_user().id
      }
    )
  end
end
