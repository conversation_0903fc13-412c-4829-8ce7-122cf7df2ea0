defmodule Boring.Files.File do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Files

  alias Boring.Accounts.User

  postgres do
    table "files"
    repo Boring.Repo

    references do
      reference :author, on_delete: :restrict
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :url, :string, allow_nil?: false, public?: true
    attribute :name, :string, allow_nil?: false, public?: true

    attribute :archived, :boolean, default: false, public?: true

    timestamps()
  end

  relationships do
    belongs_to :author, User, public?: true
  end
end
