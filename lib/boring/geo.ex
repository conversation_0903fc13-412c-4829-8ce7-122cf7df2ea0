defmodule Boring.Geo do
  @moduledoc false
  use Ash.Domain,
    validate_config_inclusion?: false

  resources do
    resource __MODULE__.Circle do
      define :create_circle do
        action :create
        args [:center, :radius, :units]
      end

      define :get_circle_radius_in_units do
        action :get_radius_as_units
        args [:circle, :units]
      end

      define :get_circle_bbox do
        action :get_bounding_box
        args [:circle]
      end
    end
  end
end
