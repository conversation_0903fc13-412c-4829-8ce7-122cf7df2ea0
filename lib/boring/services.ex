defmodule Boring.Services do
  @moduledoc false
  use Ash.Domain

  alias Boring.Types.OpportunityOrId

  resources do
    resource __MODULE__.GlobalContact

    resource __MODULE__.ServiceRequest do
      define :list_service_requests, action: :read
      define :list_open_service_requests_by_type, args: [:type], action: :read_open_by_type

      define :read_by_org_opportunity,
        args: [:org_opportunity_id],
        action: :read_by_org_opportunity

      define :get_service_request, action: :read, get_by: :id

      define :create_service_request do
        action :create
        args [:opportunity, :type]

        custom_input :opportunity, OpportunityOrId do
          allow_nil? false

          transform do
            to :opportunity_id
            using &OpportunityOrId.to_id/1
          end
        end
      end

      define :process_skip_trace
    end
  end
end
