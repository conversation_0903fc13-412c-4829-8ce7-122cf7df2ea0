defmodule Boring.Demographics.Demographic do
  @moduledoc false
  use Ash.Resource,
    otp_app: :boring,
    data_layer: AshPostgres.DataLayer,
    domain: Boring.Demographics,
    authorizers: [Ash.Policy.Authorizer]

  alias Boring.Locations.State

  postgres do
    table "demographics"
    repo Boring.Repo

    references do
      reference :state, on_delete: :restrict
    end

    custom_indexes do
      index [:city, :state_id]
    end
  end

  actions do
    defaults [:read, :destroy, update: :*]

    create :create do
      primary? true
      accept [:*]
      upsert? true
      upsert_identity :unique_city_demographic
      upsert_fields {:replace_all_except, [:id, :state_id, :inserted_at]}

      argument :state_name, :string, allow_nil?: false

      change manage_relationship(:state_name, :state,
               value_is_key: :name,
               use_identities: [:state_name],
               type: :append
             ),
             only_when_valid?: true
    end
  end

  policies do
    bypass AshOban.Checks.AshObanInteraction do
      authorize_if always()
    end

    bypass actor_attribute_equals(:role, :admin) do
      authorize_if always()
    end

    policy action_type(:read) do
      authorize_if always()
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :geo_id, :string, public?: true
    attribute :city, :string, allow_nil?: false, public?: true
    attribute :population_estimate, :integer, allow_nil?: false, public?: true
    attribute :median_household_income_estimate, :money, allow_nil?: false, public?: true

    timestamps()
  end

  relationships do
    belongs_to :state, State, allow_nil?: false

    has_many :opportunities, Boring.Acquisitions.Opportunity do
      no_attributes? true

      filter expr(
               state_id == parent(state_id) and city == parent(city) and
                 google_place.has_required_categories? == true
             )
    end
  end

  aggregates do
    count :opportunity_count, :opportunities
  end

  identities do
    identity :unique_city_demographic, [:geo_id]
  end
end
