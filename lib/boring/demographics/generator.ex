defmodule Boring.Demographics.Generator do
  @moduledoc false
  use Ash.Generator

  import StreamData, only: [repeatedly: 1]

  alias Boring.Demographics.Demographic

  def demographic(overrides \\ []) do
    changeset_generator(
      Demographic,
      :create,
      defaults: [
        geo_id: repeatedly(fn -> Ash.UUIDv7.generate() end),
        city: overrides[:city],
        state_name: overrides[:state_name],
        population_estimate: repeatedly(fn -> Enum.random(2_500..1_000_000//1) end),
        median_household_income_estimate: repeatedly(fn -> Money.new!(:USD, Enum.random(25_000..150_000//1)) end)
      ],
      overrides: overrides,
      authorize?: false
    )
  end
end
