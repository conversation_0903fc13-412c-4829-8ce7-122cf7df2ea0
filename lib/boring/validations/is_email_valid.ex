defmodule Boring.Validations.IsEmailValid do
  @moduledoc false
  use Ash.Resource.Validation

  @impl true
  def validate(changeset, opts, _context) do
    attribute = opts[:attribute]
    email = Ash.Changeset.get_argument_or_attribute(changeset, attribute)

    if EmailChecker.valid?(to_string(email)) do
      :ok
    else
      {:error, field: attribute, message: "is invalid"}
    end
  end

  @impl true
  def atomic(changeset, opts, context) do
    validate(changeset, opts, context)
  end
end
