defmodule Boring.Utils.ValidatePhoneNumber do
  @moduledoc false

  use Ash.Resource.Validation

  alias Ash.Error.Changes.InvalidAttribute
  alias Boring.Utils.PhoneNumber, as: PhoneNumberUtils

  @opt_schema [
    attribute: [
      type: :atom,
      required: true,
      doc: "The attribute to check"
    ],
    message: [
      type: :string,
      required: true,
      doc: "The message that will be placed on the field in the case of failure"
    ],
    regions: [
      type: {:list, {:or, [:string, nil]}},
      required: false,
      doc: "The valid region codes for the phone number"
    ]
  ]

  @impl true
  def init(opts) do
    case Spark.Options.validate(opts, @opt_schema) do
      {:ok, opts} ->
        {:ok, opts}

      {:error, error} ->
        {:error, Exception.message(error)}
    end
  end

  @impl true
  def validate(changeset, opts, _ctx) do
    case Ash.Changeset.fetch_argument_or_change(changeset, opts[:attribute]) do
      {:ok, changing_to} when not is_nil(changing_to) ->
        case string_value(changing_to, opts) do
          {:ok, changing_to} ->
            regions = opts[:regions]
            validate_string_for_regions(changing_to, regions, opts)

          {:error, error} ->
            {:error, error}
        end

      _ ->
        :ok
    end
  end

  @impl true
  def atomic(changeset, opts, context) do
    validate(changeset, opts, context)
  end

  @impl true
  def describe(opts) do
    [
      message: opts[:message],
      vars: [field: opts[:attribute], match: opts[:match]]
    ]
  end

  defp string_value(value, opts) do
    {:ok, to_string(value)}
  rescue
    _ ->
      {:error, exception(value, opts)}
  end

  defp validate_string_for_regions(phone_number, regions, opts) do
    case PhoneNumberUtils.parse(phone_number, regions) do
      {:ok, _parsed} -> :ok
      {:error, _} -> {:error, exception(phone_number, opts)}
    end
  end

  defp exception(value, opts) do
    [value: value, field: opts[:attribute]]
    |> with_description(opts)
    |> InvalidAttribute.exception()
  end
end
