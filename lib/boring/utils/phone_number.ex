defmodule Boring.Utils.PhoneNumber do
  @moduledoc """
  Utility functions for working with phone numbers. Currently Boring only allows US numbers, with AU/KR numbers for testing purposes.
  """

  alias ExPhoneNumber.Model.PhoneNumber

  @type ex_phone_number :: %PhoneNumber{}

  @doc """
  Parses a phone number entered into a `tel` form field into the correct format for its region. If it can be successfully parsed for one of the regions in the list, returns the parsed number. Otherwise returns false if number could not be validated for any of the regions. Note that "nil" in the regions list will default to US.
  """
  @spec parse(phone_number_input :: String.t()) ::
          {:ok, ex_phone_number()} | {:error, String.t()}
  def parse(phone_number_input, regions \\ nil) do
    do_parse(phone_number_input, regions || [nil, "US", "AU"])
  end

  @spec parse!(phone_number :: String.t(), default_region :: String.t()) ::
          ex_phone_number()
  def parse!(phone_number, default_regions \\ nil) do
    case parse(phone_number, default_regions) do
      {:ok, parsed} -> parsed
      {:error, _} -> raise ArgumentError, "Invalid phone number"
    end
  end

  @doc """
  Reformats a phone number string as the e164 international standard

      iex> Boring.Util.PhoneNumber.format_as_e164("(*************")
      "+19082718691"

      iex> Boring.Util.PhoneNumber.format_as_e164("(*************" <> <<0x202C::utf8>>)
      "+19082718691"
  """
  @spec format_as_e164(phone_number :: String.t() | PhoneNumber.t()) :: String.t() | nil
  def format_as_e164(phone_number) when is_binary(phone_number) do
    case parse(phone_number) do
      {:ok, %PhoneNumber{} = parsed} -> format_as_e164(parsed)
      {:error, _reason} -> nil
    end
  end

  def format_as_e164(phone_number) when is_struct(phone_number, PhoneNumber) do
    ExPhoneNumber.format(phone_number, :e164)
  end

  @doc """
  Reformats a phone number string as a national format, good for showing users

      iex> Boring.Util.PhoneNumber.format_as_national("+19082718691")
      "(*************"

      iex> Boring.Util.PhoneNumber.format_as_national("************")
      "(*************"

      iex> Boring.Util.PhoneNumber.format_as_national("************" <> <<0x202C::utf8>>)
      "(*************"
  """
  @spec format_as_national(phone_number :: String.t()) :: String.t() | nil
  def format_as_national(phone_number) when is_binary(phone_number) do
    case parse(phone_number) do
      %PhoneNumber{} = parsed -> format_as_national(parsed)
      _ -> nil
    end
  end

  def format_as_national(phone_number) when is_struct(phone_number, PhoneNumber) do
    ExPhoneNumber.format(phone_number, :national)
  end

  @doc """
  Mask a phone number, showing only the last three digits

      iex> Boring.Util.PhoneNumber.readact!("************")
      "***-***-*100"

      iex> Boring.Util.PhoneNumber.readact!("+610491570156")
      "*** *** 156"

  """
  @spec readact(phone_number_string :: String.t()) :: String.t()
  def readact(phone_number_string) do
    parsed =
      phone_number_string
      |> parse!()
      |> ExPhoneNumber.format(:domestic)

    {to_redact, to_show} = String.split_at(parsed, -3)

    redacted = Regex.replace(~r/[0-9]/, to_redact, "*")
    redacted <> to_show
  end

  defp do_parse(_phone_number, []), do: {:error, "Invalid phone number"}

  defp do_parse(phone_number, [region | rest]) do
    with {:ok, number} <- ExPhoneNumber.parse(phone_number, region),
         true <- ExPhoneNumber.is_valid_number?(number) do
      {:ok, number}
    else
      _ -> do_parse(phone_number, rest)
    end
  end
end
