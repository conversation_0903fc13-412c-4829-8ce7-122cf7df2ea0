defmodule Boring.Utils.DisplayPhoneNumber do
  @moduledoc """
  Provides calculations for returning phone numbers in their country's domestic/national format for display in the UI
  """
  use Ash.Resource.Calculation

  alias Boring.Utils.PhoneNumber, as: PhoneNumberUtils

  @impl true
  def init(opts) do
    if opts[:attribute_path] && is_atom(opts[:attribute_path]) do
      {:ok, opts}
    else
      {:error, "Expected a `attribute_path` option for which phone number field to format for display"}
    end
  end

  @impl true
  def load(_query, opts, _context) do
    [opts[:attribute_path]]
  end

  @impl true
  def calculate(records, opts, _) do
    do_calculate(records, opts[:attribute_path], [])
  end

  defp do_calculate([], _attribute_path, acc), do: {:ok, Enum.reverse(acc)}

  defp do_calculate([record | rest], attribute_path, acc) do
    unformatted_number = Map.get(record, attribute_path)

    if unformatted_number do
      case PhoneNumberUtils.parse(unformatted_number) do
        {:ok, parsed} ->
          # successfully parsed the phone number, so we format it as a national number
          do_calculate(rest, attribute_path, [PhoneNumberUtils.format_as_national(parsed) | acc])

        {:error, _} ->
          # failed to pass, so we just return the original value
          do_calculate(rest, attribute_path, [Map.get(record, attribute_path) | acc])
      end
    else
      # if the phone number is nil, we just return nil
      do_calculate(rest, attribute_path, [nil | acc])
    end
  end
end
