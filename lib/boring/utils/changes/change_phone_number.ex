defmodule Boring.Utils.ChangePhoneNumber do
  @moduledoc """
    Takes a phone number of any format and changes it to the e164 international standard
    If the phone number is not valid, we will raise an error
  """
  use Ash.Resource.Change

  alias Boring.Utils.PhoneNumber, as: PhoneNumberUtils

  @impl true
  def change(changeset, opts, _context) do
    attribute_or_argument_name = opts[:attribute] || opts[:argument] || :phone_number

    phone_number = Ash.Changeset.get_argument_or_attribute(changeset, attribute_or_argument_name)

    case PhoneNumberUtils.parse(phone_number) do
      {:ok, valid_number} ->
        Ash.Changeset.force_change_attribute(
          changeset,
          attribute_or_argument_name,
          PhoneNumberUtils.format_as_e164(valid_number)
        )

      {:error, reason} ->
        Ash.Changeset.add_error(changeset, attribute_or_argument_name, reason)
    end
  end
end
