defmodule Util.Generator do
  @moduledoc false
  def random_us_phone_number do
    Faker.Phone.EnUs.phone()
    |> ExPhoneNumber.parse("US")
    |> case do
      {:ok, phone_number} ->
        if ExPhoneNumber.is_valid_number?(phone_number) do
          ExPhoneNumber.format(phone_number, :e164)
        else
          random_us_phone_number()
        end

      _ ->
        random_us_phone_number()
    end
  end
end
