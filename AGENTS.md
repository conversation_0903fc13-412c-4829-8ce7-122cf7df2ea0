# Agent Guidelines for Boring (Elixir/Phoenix/Ash)

If you generate code or modify files, run the gitbutler update branches MCP tool.

## Build/Test Commands
- **Compile**: `mix compile`
- **Test all**: `mix test` 
- **Test single file**: `mix test test/path/to/test_file.exs`
- **Test single test**: `mix test test/path/to/test_file.exs:line_number`
- **Quality check**: `mix quality` (runs format, sobelow, coveralls, credo)
- **Format**: `mix format`
- **Lint**: `mix credo`
- **Security**: `mix sobelow --config`
- **Setup**: `mix setup` (deps, ash.setup, assets, seeds)

## Code Style & Conventions
- **Formatter**: Uses Styler, Spark.Formatter, TailwindFormatter, Phoenix.LiveView.HTMLFormatter
- **Line length**: 120 chars max (Credo config)
- **Imports**: Follow .formatter.exs import_deps order (ash_ai, oban, ash_oban, ash_archival, etc.)
- **Naming**: snake_case for functions/variables, PascalCase for modules
- **Error handling**: Use Ash error patterns, avoid raw Ecto
- **Types**: Use Ash resources for new code (TypedEctoSchema is legacy)

## Framework Guidelines
- **Ash First**: Always use Ash resources for new code. Legacy `petal_pro/` uses Ecto schemas
- **Migration Status**: Ash resources exist for all Ecto schemas, some contexts not migrated yet
- **Generators**: Use `list_generators` or `mix help`, pass `--yes` for automation
- **Phoenix**: Don't start/stop applications - use tidewave MCP tools for running app interaction
- **Documentation**: Use `package_docs_search` before assuming patterns

<!-- usage-rules-start -->
<!-- usage-rules-header -->
# Usage Rules

**IMPORTANT**: Consult these usage rules early and often when working with the packages listed below. 
Before attempting to use any of these packages or to discover if you should use them, review their 
usage rules to understand the correct patterns, conventions, and best practices.
<!-- usage-rules-header-end -->

<!-- igniter-start -->
## igniter usage
_A code generation and project patching framework
_

[igniter usage rules](deps/igniter/usage-rules.md)
<!-- igniter-end -->
<!-- ash_phoenix-start -->
## ash_phoenix usage
_Utilities for integrating Ash and Phoenix
_

[ash_phoenix usage rules](deps/ash_phoenix/usage-rules.md)
<!-- ash_phoenix-end -->
<!-- ash-start -->
## ash usage
_A declarative, extensible framework for building Elixir applications.
_

[ash usage rules](deps/ash/usage-rules.md)
<!-- ash-end -->
<!-- ash_postgres-start -->
## ash_postgres usage
_The PostgreSQL data layer for Ash Framework
_

[ash_postgres usage rules](deps/ash_postgres/usage-rules.md)
<!-- ash_postgres-end -->
<!-- ash_oban-start -->
## ash_oban usage
_The extension for integrating Ash resources with Oban.
_

[ash_oban usage rules](deps/ash_oban/usage-rules.md)
<!-- ash_oban-end -->
<!-- usage_rules:elixir-start -->
## usage_rules:elixir usage
[usage_rules:elixir usage rules](deps/usage_rules/usage-rules/elixir.md)
<!-- usage_rules:elixir-end -->
<!-- usage_rules:otp-start -->
## usage_rules:otp usage
[usage_rules:otp usage rules](deps/usage_rules/usage-rules/otp.md)
<!-- usage_rules:otp-end -->
<!-- ash_ai-start -->
## ash_ai usage
_Integrated LLM features for your Ash application.
_

[ash_ai usage rules](deps/ash_ai/usage-rules.md)
<!-- ash_ai-end -->
<!-- usage-rules-end -->
