defmodule PetalProWeb.AssignScopePlugTest do
  use PetalProWeb.ConnCase, async: true

  alias Boring.Accounts.User
  alias Boring.Orgs.Org
  alias PetalProWeb.AssignScopePlug

  test "assigns scope with actor when only current_user is present", %{conn: conn} do
    user = PetalPro.AccountsFixtures.confirmed_user_fixture()

    conn =
      conn
      |> init_test_session(%{})
      |> assign(:current_user, user)

    conn = AssignScopePlug.call(conn, [])

    assert %{} = scope = conn.assigns.scope
    assert scope.current_user.id == user.id

    # Just verify the scope is set correctly
    assert scope.current_user.__struct__ == User
  end

  test "assigns scope with actor and tenant when both current_user and current_org are present",
       %{conn: conn} do
    user = PetalPro.AccountsFixtures.confirmed_user_fixture()
    org = PetalPro.OrgsFixtures.org_fixture()

    conn =
      conn
      |> init_test_session(%{})
      |> assign(:current_user, user)
      |> assign(:current_org, org)

    conn = AssignScopePlug.call(conn, [])

    assert %{} = scope = conn.assigns.scope
    assert scope.current_user.id == user.id
    assert scope.current_tenant.id == org.id

    # Just verify the scope is set correctly
    assert scope.current_user.__struct__ == User
    assert scope.current_tenant.__struct__ == Org
  end

  test "does nothing when no current_user is present", %{conn: conn} do
    conn =
      conn
      |> init_test_session(%{})
      |> AssignScopePlug.call([])

    assert is_nil(conn.assigns.scope.current_user)
    assert is_nil(conn.assigns.scope.current_tenant)
  end
end
