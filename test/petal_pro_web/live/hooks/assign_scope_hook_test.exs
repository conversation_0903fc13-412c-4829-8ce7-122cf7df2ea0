defmodule PetalProWeb.AssignScopeHookTest do
  use PetalProWeb.ConnCase, async: true

  alias PetalProWeb.AssignScopeHook
  alias Phoenix.Component
  alias Phoenix.LiveView.Socket

  # Helper function to create a proper socket for testing
  defp build_socket(assigns \\ %{}) do
    socket = %Socket{}
    Enum.reduce(assigns, socket, fn {key, value}, acc -> Component.assign(acc, key, value) end)
  end

  test "assigns scope with actor when only current_user is present" do
    user = PetalPro.AccountsFixtures.confirmed_user_fixture()
    socket = build_socket(%{current_user: user})

    {:cont, socket} = AssignScopeHook.on_mount(:default, %{}, %{}, socket)

    assert %{} = scope = socket.assigns.scope
    assert scope.current_user.id == user.id
  end

  test "assigns scope with actor and tenant when both current_user and current_org are present" do
    user = PetalPro.AccountsFixtures.confirmed_user_fixture()
    org = PetalPro.OrgsFixtures.org_fixture()

    socket = build_socket(%{current_user: user, current_org: org})

    {:cont, socket} = AssignScopeHook.on_mount(:default, %{}, %{}, socket)

    assert %{} = scope = socket.assigns.scope
    assert scope.current_user.id == user.id
    assert scope.current_tenant.id == org.id
  end

  test "does nothing when current_user or current_org is not present" do
    socket = build_socket()

    {:cont, socket} = AssignScopeHook.on_mount(:default, %{}, %{}, socket)

    assert is_nil(socket.assigns.scope.current_user)
    assert is_nil(socket.assigns.scope.current_tenant)
  end
end
