defmodule PetalProWeb.OrgLayoutComponentTest do
  use PetalProWeb.ConnCase, async: true

  import PetalPro.BillingFixtures
  import PetalPro.OrgsFixtures
  import Phoenix.LiveViewTest

  alias PetalPro.Accounts.UserSeeder

  describe "subscription warning banner" do
    setup do
      # Create a fresh org and users for each test to avoid conflicts
      admin = UserSeeder.confirmed_user(%{is_onboarded: true})
      org = org_fixture(admin)

      # Create a regular member
      member = org_member_fixture(org, %{is_onboarded: true})

      %{conn: build_conn(), org: org, admin: admin, member: member}
    end

    test "shows warning banner when org has no active subscription for admin", %{
      conn: conn,
      org: org,
      admin: admin
    } do
      # Log in as the admin
      conn = log_in_user(conn, admin)

      # Create a route that uses the org layout
      {:ok, view, html} = live(conn, ~p"/app/org/#{org.slug}")

      # Check that the warning banner is displayed
      assert html =~ "This organization does not have an active subscription"

      assert html =~
               "As an organization admin, you can update the subscription to access all features"

      # Check that the update subscription button is displayed for admins
      assert has_element?(view, "a", "Update Subscription")
    end

    test "shows warning banner with appropriate message for non-admin users", %{
      conn: conn,
      org: org,
      member: member
    } do
      # Log in as a regular member
      conn = log_in_user(conn, member)

      # Create a route that uses the org layout
      {:ok, view, html} = live(conn, ~p"/app/org/#{org.slug}")

      # Check that the warning banner is displayed with the appropriate message
      assert html =~ "This organization does not have an active subscription"
      assert html =~ "Please contact your organization admin to update the subscription"

      # Check that the update subscription button is not displayed for non-admins
      refute has_element?(view, "a", "Update Subscription")
    end

    test "does not show warning banner when org has an active subscription", %{
      conn: conn,
      org: org,
      admin: admin
    } do
      # Log in as the admin
      conn = log_in_user(conn, admin)

      # Create a customer and active subscription for the org
      customer = billing_customer_fixture(%{source: :org, org_id: org.id})
      subscription_fixture(%{billing_customer_id: customer.id, status: "active"})

      # Create a route that uses the org layout
      {:ok, _view, html} = live(conn, ~p"/app/org/#{org.slug}")

      # Check that the warning banner is not displayed
      refute html =~ "This organization does not have an active subscription"
    end

    test "does not show warning banner when org has a trialing subscription", %{
      conn: conn,
      org: org,
      admin: admin
    } do
      # Log in as the admin
      conn = log_in_user(conn, admin)

      # Create a customer and trialing subscription for the org
      customer = billing_customer_fixture(%{source: :org, org_id: org.id})
      subscription_fixture(%{billing_customer_id: customer.id, status: "trialing"})

      # Create a route that uses the org layout
      {:ok, _view, html} = live(conn, ~p"/app/org/#{org.slug}")

      # Check that the warning banner is not displayed
      refute html =~ "This organization does not have an active subscription"
    end
  end
end
