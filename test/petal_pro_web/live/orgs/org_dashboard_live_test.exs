defmodule PetalProWeb.OrgDashboardLiveTest do
  use PetalProWeb.ConnCase, async: true
  use Ash.Generator

  import PetalPro.OrgsFixtures
  import Phoenix.LiveViewTest

  alias Boring.Acquisitions.Generator

  setup :register_and_sign_in_user

  describe "has role 'admin'" do
    test "can access and see name and settings", %{conn: conn, user: user} do
      org = org_fixture()
      membership_fixture(org, user, :admin)

      # assert html =~ org.name
      # assert has_element?(view, "#stacked-menu div", "Settings")

      {:ok, view, html} = live(conn, ~p"/app/org/#{org.slug}")

      assert html =~ org.name
      assert has_element?(view, "#stacked-menu div", "Settings")
    end
  end

  describe "has role 'member'" do
    test "can access but doesn't see settings", %{conn: conn, user: user} do
      org = org_fixture()
      membership_fixture(org, user, :member)

      {:ok, view, html} = live(conn, ~p"/app/org/#{org.slug}")

      assert html =~ org.name
      refute has_element?(view, "#stacked-menu span", "Settings")
    end

    test "won't let you edit the org", %{conn: conn, user: user} do
      org = org_fixture()
      membership_fixture(org, user, :member)

      assert {:error, {:redirect, %{flash: %{"error" => "You do not have permission to access this page."}}}} =
               live(conn, ~p"/app/org/#{org.slug}/edit")
    end
  end

  describe "is not a member" do
    test "redirects", %{conn: conn} do
      org = org_fixture()

      for route <- [~p"/app/org/#{org.slug}", ~p"/app/org/#{org.slug}/edit"] do
        assert {:error, {:redirect, %{flash: %{"error" => "You do not have permission to access this page."}}}} =
                 live(conn, route)
      end
    end
  end

  describe "organisation dashboard" do
    test "with no org opportunities, no cards should show", %{conn: conn, user: user} do
      org = org_fixture()
      membership_fixture(org, user, :admin)

      {:ok, view, _html} = live(conn, ~p"/app/org/#{org.slug}")

      refute has_element?(view, "[data-role=org-opportunity-card]")
    end

    test "if there are org opportunities, cards should show", %{conn: conn, user: user} do
      org = org_fixture()
      org_opportunity_one = [org_id: org.id] |> Generator.org_opportunity() |> generate()
      org_opportunity_two = [org_id: org.id] |> Generator.org_opportunity() |> generate()

      membership_fixture(org, user, :admin)

      {:ok, view, _html} = live(conn, ~p"/app/org/#{org.slug}")

      assert has_element?(
               view,
               "[data-role=org-opportunity-card][data-id=#{org_opportunity_one.id}]"
             )

      assert has_element?(
               view,
               "[data-role=org-opportunity-card][data-id=#{org_opportunity_two.id}]"
             )
    end
  end

  describe "handles nil values gracefully" do
    test "renders opportunity card with nil values", %{conn: conn, user: user} do
      org = org_fixture()
      membership_fixture(org, user, :admin)

      # Create an opportunity with nil values for optional fields
      opportunity = generate(Generator.opportunity())

      org_opportunity =
        [org_id: org.id, opportunity_id: opportunity.id]
        |> Generator.org_opportunity()
        |> generate()

      {:ok, view, _html} = live(conn, ~p"/app/org/#{org.slug}")

      # Verify the card renders without errors
      assert has_element?(view, "[data-role=org-opportunity-card][data-id=#{org_opportunity.id}]")

      # Verify required fields are present
      assert has_element?(
               view,
               "[data-role=org-opportunity-card] [data-role=opportunity-name]",
               opportunity.name
             )

      assert has_element?(
               view,
               "[data-role=org-opportunity-card] [data-role=opportunity-street]",
               opportunity.street
             )

      assert has_element?(
               view,
               "[data-role=org-opportunity-card] [data-role=opportunity-location]",
               "#{opportunity.city}, #{opportunity.state.name} #{opportunity.postal_code}"
             )

      # Verify optional fields are handled gracefully when nil
      refute has_element?(view, "[data-role=org-opportunity-card] [data-role=website-link]")
      assert has_element?(view, "[data-role=org-opportunity-card] [data-role=rating]")

      assert has_element?(
               view,
               "[data-role=org-opportunity-card] [data-role=image-not-available]",
               "Image not available"
             )
    end
  end
end
