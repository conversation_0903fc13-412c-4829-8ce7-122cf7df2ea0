defmodule PetalProWeb.EditEmailLiveTest do
  use PetalProWeb.ConnCase

  import Phoenix.LiveViewTest

  describe "when signed in" do
    setup :register_and_sign_in_user

    test "event update_email works", %{conn: conn, user: _user} do
      new_email = "<EMAIL>"
      {:ok, view, html} = live(conn, ~p"/app/users/edit-email")
      assert html =~ "Change your email"

      assert view
             |> form("#change_email_form", user: %{email: new_email})
             |> render_submit() =~
               "A link to confirm your e-mail change has been sent to the new address"

      assert_email_sent(subject: "Change email")
      assert_log("request_new_email", %{metadata: %{"new_email" => new_email}})
    end
  end

  describe "when signed out" do
    test "can't access the page", %{conn: conn} do
      conn
      |> live(~p"/app/users/edit-email")
      |> assert_route_protected()
    end
  end
end
