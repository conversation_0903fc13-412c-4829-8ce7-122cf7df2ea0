defmodule PetalProWeb.SubscribeSuccessLiveTest do
  use PetalProWeb.ConnCase, async: true

  import PetalPro.BillingFixtures
  import Phoenix.LiveViewTest

  setup :register_and_sign_in_user

  describe "live /app/subscribe/success" do
    setup %{user: user} do
      customer =
        billing_customer_fixture(%{source: :user, user_id: user.id})

      {:ok, customer: customer}
    end

    test "when subscription creation is successful, shows success page", %{
      conn: conn,
      customer: customer
    } do
      subscription_fixture(%{
        billing_customer_id: customer.id,
        plan_id: "something",
        provider_subscription_items: [
          %{price_id: "price1", product_id: "prod1"}
        ]
      })

      {:ok, view, _html} =
        live(conn, ~p"/app/subscribe/success?customer_id=#{customer.id}&plan_id=something")

      assert view
             |> element("#subscription-status")
             |> render() =~ "Welcome to GoFish!"

      assert view
             |> element("#subscription-status")
             |> render() =~ "Start Fishing!"
    end

    test "when waiting for the webhook to complete, show a spinner", %{
      conn: conn,
      customer: customer
    } do
      {:ok, view, _html} =
        live(conn, ~p"/app/subscribe/success?customer_id=#{customer.id}&plan_id=something")

      assert view
             |> element("#subscription-status")
             |> render() =~ "spinner"
    end

    test "check_subscription() will show success page when found",
         %{conn: conn, customer: customer} do
      {:ok, view, _html} =
        live(conn, ~p"/app/subscribe/success?customer_id=#{customer.id}&plan_id=something")

      subscription_fixture(%{
        billing_customer_id: customer.id,
        plan_id: "something",
        provider_subscription_items: [
          %{price_id: "price1", product_id: "prod1"}
        ]
      })

      send(view.pid, :check_subscription)

      assert view
             |> element("#subscription-status")
             |> render() =~ "Welcome to GoFish!"

      assert view
             |> element("#subscription-status")
             |> render() =~ "Start Fishing!"
    end

    test "when a subscription fails, show a failure message", %{conn: conn, customer: customer} do
      {:ok, view, _html} =
        live(conn, ~p"/app/subscribe/success?customer_id=#{customer.id}&plan_id=something")

      send(view.pid, :check_subscription)
      send(view.pid, :check_subscription)
      send(view.pid, :check_subscription)
      send(view.pid, :check_subscription)
      send(view.pid, :check_subscription)

      assert view
             |> element("#subscription-status")
             |> render() =~ "Subscription failed"

      assert view
             |> element("#subscription-status")
             |> render() =~ "Something has gone wrong. Please contact support"

      assert view
             |> element("#subscription-status")
             |> render() =~ "Go back"
    end

    test "shows welcome message for subscribers", %{
      conn: conn,
      user: user
    } do
      # Create a fresh customer with a subscription
      fresh_customer = billing_customer_fixture(%{source: :user, user_id: user.id})

      subscription_fixture(%{
        billing_customer_id: fresh_customer.id,
        plan_id: "something",
        provider_subscription_items: [
          %{price_id: "price1", product_id: "prod1"}
        ]
      })

      {:ok, view, _html} =
        live(conn, ~p"/app/subscribe/success?customer_id=#{fresh_customer.id}&plan_id=something")

      rendered = view |> element("#subscription-status") |> render()

      assert rendered =~ "Welcome to GoFish!"
      assert rendered =~ "You are ready to start fishing for off-market deals"
      assert rendered =~ "Start Fishing!"
    end
  end

  describe "live /app/org/:org_slug/subscribe/success" do
    setup %{org: org} do
      customer = billing_customer_fixture(%{source: :org, org_id: org.id})

      {:ok, customer: customer}
    end

    test "when subscription creation is successful, shows success page", %{
      conn: conn,
      customer: customer,
      org: org
    } do
      subscription_fixture(%{
        billing_customer_id: customer.id,
        plan_id: "something",
        provider_subscription_items: [
          %{price_id: "price1", product_id: "prod1"}
        ]
      })

      {:ok, view, _html} =
        live(
          conn,
          ~p"/app/org/#{org.slug}/subscribe/success?customer_id=#{customer.id}&plan_id=something"
        )

      assert view
             |> element("#subscription-status")
             |> render() =~ "Welcome to GoFish!"

      assert view
             |> element("#subscription-status")
             |> render() =~ "Start Fishing!"
    end

    test "when waiting for the webhook to complete, show a spinner", %{
      conn: conn,
      customer: customer,
      org: org
    } do
      {:ok, view, _html} =
        live(
          conn,
          ~p"/app/org/#{org.slug}/subscribe/success?customer_id=#{customer.id}&plan_id=something"
        )

      assert view
             |> element("#subscription-status")
             |> render() =~ "spinner"
    end

    test "check_subscription() will show success page when found",
         %{conn: conn, org: org, customer: customer} do
      {:ok, view, _html} =
        live(
          conn,
          ~p"/app/org/#{org.slug}/subscribe/success?customer_id=#{customer.id}&plan_id=something"
        )

      subscription_fixture(%{
        billing_customer_id: customer.id,
        plan_id: "something",
        provider_subscription_items: [
          %{price_id: "price1", product_id: "prod1"}
        ]
      })

      send(view.pid, :check_subscription)

      assert view
             |> element("#subscription-status")
             |> render() =~ "Welcome to GoFish!"

      assert view
             |> element("#subscription-status")
             |> render() =~ "Start Fishing!"
    end

    test "when a subscription fails, show a failure message", %{
      conn: conn,
      customer: customer,
      org: org
    } do
      {:ok, view, _html} =
        live(
          conn,
          ~p"/app/org/#{org.slug}/subscribe/success?customer_id=#{customer.id}&plan_id=something"
        )

      send(view.pid, :check_subscription)
      send(view.pid, :check_subscription)
      send(view.pid, :check_subscription)
      send(view.pid, :check_subscription)
      send(view.pid, :check_subscription)

      assert view
             |> element("#subscription-status")
             |> render() =~ "Subscription failed"

      assert view
             |> element("#subscription-status")
             |> render() =~ "Something has gone wrong. Please contact support"

      assert view
             |> element("#subscription-status")
             |> render() =~ "Go back"
    end

    test "shows welcome back message for returning customers", %{
      conn: conn,
      customer: customer,
      org: org
    } do
      # Create multiple subscriptions to make this a "returning" customer (total_subscriptions > 1)
      subscription_fixture(%{
        billing_customer_id: customer.id,
        plan_id: "old_plan",
        status: "canceled",
        provider_subscription_items: [
          %{price_id: "old_price", product_id: "old_prod"}
        ]
      })

      subscription_fixture(%{
        billing_customer_id: customer.id,
        plan_id: "something",
        provider_subscription_items: [
          %{price_id: "price1", product_id: "prod1"}
        ]
      })

      {:ok, view, _html} =
        live(
          conn,
          ~p"/app/org/#{org.slug}/subscribe/success?customer_id=#{customer.id}&plan_id=something"
        )

      rendered = view |> element("#subscription-status") |> render()

      assert rendered =~ "Welcome to GoFish!"
      assert rendered =~ "You are ready to start fishing for off-market deals"
      assert rendered =~ "Start Fishing!"
    end
  end
end
