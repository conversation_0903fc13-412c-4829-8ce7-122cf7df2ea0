defmodule PetalProWeb.Components.GoogleMapTest do
  use PetalProWeb.ConnCase, async: true

  import Phoenix.LiveViewTest

  alias PetalProWeb.Components.GoogleMap

  describe "GoogleMap component" do
    test "renders basic map without markers" do
      assigns = %{
        id: "test-map",
        map_options: %{
          center: %{lat: 37.7749, lng: -122.4194},
          zoom: 12
        }
      }

      html = render_component(&GoogleMap.google_map/1, assigns)

      assert html =~ ~s(id="test-map")
      assert html =~ ~s(phx-hook="GoogleMapHook")
    end

    test "renders map with markers" do
      assigns = %{
        id: "test-map-with-markers",
        map_options: %{
          center: %{lat: 37.7749, lng: -122.4194},
          zoom: 12
        },
        markers: [
          %{
            id: "marker-1",
            position: %{lat: 37.7749, lng: -122.4194},
            title: "Test Marker"
          }
        ]
      }

      html = render_component(&GoogleMap.google_map/1, assigns)

      assert html =~ ~s(id="test-map-with-markers")
      assert html =~ ~s(phx-hook="GoogleMapHook")
    end

    test "automatically adds marker library when markers are present" do
      assigns = %{
        id: "test-map",
        markers: [
          %{id: "marker-1", position: %{lat: 37.7749, lng: -122.4194}}
        ],
        libraries: %{}
      }

      html = render_component(&GoogleMap.google_map/1, assigns)

      # Check that the component renders with markers
      assert html =~ ~s(id="test-map")
      assert html =~ ~s(phx-hook="GoogleMapHook")
      assert html =~ ~s(data-config=)
    end

    test "includes markers in computed config" do
      markers = [
        %{
          id: "marker-1",
          position: %{lat: 37.7749, lng: -122.4194},
          title: "Test Marker",
          draggable: true
        },
        %{
          id: "marker-2",
          position: %{lat: 37.7849, lng: -122.4094},
          title: "Another Marker"
        }
      ]

      assigns = %{
        id: "test-map",
        markers: markers,
        libraries: %{}
      }

      html = render_component(&GoogleMap.google_map/1, assigns)

      # Check that the component renders with multiple markers
      assert html =~ ~s(id="test-map")
      assert html =~ ~s(phx-hook="GoogleMapHook")
      assert html =~ ~s(data-config=)
    end

    test "handles empty markers list" do
      assigns = %{
        id: "test-map",
        markers: [],
        libraries: %{}
      }

      html = render_component(&GoogleMap.google_map/1, assigns)

      # Check that the component renders without markers
      assert html =~ ~s(id="test-map")
      assert html =~ ~s(phx-hook="GoogleMapHook")
      assert html =~ ~s(data-config=)
    end

    test "preserves existing library configuration when adding marker library" do
      existing_libraries = %{
        places: %{
          placeFields: "free",
          eventHandlers: ["place_changed"]
        },
        drawing: %{
          drawingControl: true,
          eventHandlers: ["overlaycomplete"]
        }
      }

      assigns = %{
        id: "test-map",
        markers: [
          %{id: "marker-1", position: %{lat: 37.7749, lng: -122.4194}}
        ],
        libraries: existing_libraries
      }

      html = render_component(&GoogleMap.google_map/1, assigns)

      # Check that the component renders with library configuration
      assert html =~ ~s(id="test-map")
      assert html =~ ~s(phx-hook="GoogleMapHook")
      assert html =~ ~s(data-config=)
    end
  end
end
