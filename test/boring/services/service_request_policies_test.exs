defmodule Boring.Services.ServiceRequestPoliciesTest do
  use Boring.DataCase, async: true

  import Ash.Generator, only: [generate: 1]

  alias Boring.Acquisitions.Generator, as: AcquisitionsGen
  alias Boring.Orgs
  alias Boring.Services
  alias Boring.Services.Generator, as: ServicesGen
  alias PetalPro.Accounts.UserSeeder

  setup do: enable_feature("service_request_skiptrace")

  describe "service request policies" do
    test "admin user can perform all actions" do
      {org, _member} = setup_org_with_membership()
      user = UserSeeder.admin()
      opportunity = generate(AcquisitionsGen.opportunity())

      # Create a service request with :in_progress status to keep it "open"
      # Use seed generator to bypass Ash actions and avoid automatic processing
      service_request =
        generate(
          ServicesGen.service_request_seed(
            type: :skiptrace,
            status: :in_progress,
            opportunity_id: opportunity.id,
            org_id: org.id
          )
        )

      assert Services.can_get_service_request?(user, service_request.id,
               tenant: org,
               data: service_request
             )

      # Admin users bypass all policy checks and can create duplicate service requests
      assert Services.can_create_service_request?(
               user,
               opportunity.id,
               :skiptrace,
               tenant: org
             )
    end

    test "user cannot create service request when org has no active subscription" do
      {org, user} = setup_org_with_membership(subscription_status: "canceled")
      opportunity = generate(AcquisitionsGen.opportunity())

      refute Services.can_create_service_request?(user, opportunity.id, :skiptrace, tenant: org)
    end

    test "user cannot create service request when org has an active request of the same type" do
      {org, user} = setup_org_with_membership()
      opportunity = generate(AcquisitionsGen.opportunity())

      # Create a service request with :in_progress status to keep it "open"
      # Use seed generator to bypass Ash actions and avoid automatic processing
      _service_request =
        generate(
          ServicesGen.service_request_seed(
            type: :skiptrace,
            status: :in_progress,
            opportunity_id: opportunity.id,
            org_id: org.id
          )
        )

      refute Services.can_create_service_request?(
               user,
               opportunity.id,
               :skiptrace,
               tenant: org
             )
    end

    test "non-admin user can read service requests" do
      {org, user} = setup_org_with_membership()

      service_request =
        generate(ServicesGen.service_request(status: :new, actor: user, tenant: org))

      assert Services.can_get_service_request?(user, service_request.id,
               tenant: org,
               data: service_request
             )
    end
  end

  defp setup_org_with_membership(opts \\ []) do
    org = generate(Orgs.Generator.subscribed_org(opts))
    user = UserSeeder.random_user()
    generate(Orgs.Generator.org_membership(org_id: org.id, user_id: user.id))

    {org, user}
  end
end
