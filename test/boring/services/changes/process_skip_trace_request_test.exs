defmodule Boring.Services.Changes.ProcessSkipTraceRequestTest do
  use Boring.DataCase, async: true

  alias Boring.Services.Changes.ProcessSkipTraceRequest

  test "still_valid?/1" do
    # 89 days ago, still valid
    eighty_nine = DateTime.shift(DateTime.utc_now(), day: -89)
    assert ProcessSkipTraceRequest.still_valid?(%{updated_at: eighty_nine})

    # 90 days ago, not valid
    ninety = DateTime.shift(DateTime.utc_now(), day: -90)
    refute ProcessSkipTraceRequest.still_valid?(%{updated_at: ninety})

    # 91 days ago, not valid
    ninety_one = DateTime.shift(DateTime.utc_now(), day: -91)
    refute ProcessSkipTraceRequest.still_valid?(%{updated_at: ninety_one})
  end
end
