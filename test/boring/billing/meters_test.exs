defmodule Boring.Billing.MetersTest do
  use Boring.DataCase, async: true

  import Ash.Generator, only: [generate: 1]
  import Mimic

  alias Ash.Error.Invalid
  alias Boring.Billing.Generator, as: BillingGen
  alias Boring.Billing.Meters
  alias Boring.Billing.Meters.MeterEvent
  alias PetalPro.Billing.Providers.Stripe.Provider

  setup :verify_on_exit!

  describe "create_meter_event/4" do
    test "creates a meter event with a customer struct" do
      customer = generate(BillingGen.customer(source: :org))

      {:ok, meter_event} = Meters.create_meter_event(customer, :skiptrace, 5)

      assert %MeterEvent{} = meter_event
      assert meter_event.event == :skiptrace
      assert meter_event.value == 5
      assert meter_event.billing_customer_id == customer.id
      assert is_nil(meter_event.provider_identifier)
      assert is_nil(meter_event.provider_created_at)
    end

    test "validates event type" do
      # Create a customer for testing
      customer = generate(BillingGen.customer(source: :org))

      # Try to create a meter event with an invalid event type
      {:error, error} = Meters.create_meter_event(customer, :invalid_event, 1)

      # Verify it's an Invalid error with the right message
      assert %Invalid{} = error

      assert Enum.any?(error.errors, fn e ->
               e.field == :event && e.message =~ "is invalid"
             end)
    end

    test "validates value is positive" do
      # Create a customer for testing
      customer = generate(BillingGen.customer(source: :org))

      # Try to create a meter event with an invalid value (0)
      {:error, error_zero} = Meters.create_meter_event(customer, :skiptrace, 0)

      # Verify it's an Invalid error with the right message
      assert %Invalid{} = error_zero

      assert Enum.any?(error_zero.errors, fn e ->
               e.field == :value && e.message =~ "must be more than or equal to"
             end)

      # Try to create a meter event with an invalid value (-1)
      {:error, error_negative} =
        Meters.create_meter_event(customer, :skiptrace, -1)

      # Verify it's an Invalid error with the right message
      assert %Invalid{} = error_negative

      assert Enum.any?(error_negative.errors, fn e ->
               e.field == :value && e.message =~ "must be more than or equal to"
             end)
    end
  end

  describe "sync_meter_events/2" do
    test "sends meter event to Stripe and updates provider information" do
      # Create a customer for testing
      customer = generate(BillingGen.customer(source: :org))

      # Create a meter event
      {:ok, meter_event} = Meters.create_meter_event(customer, :skiptrace, 5)

      # Verify the provider_identifier is nil initially
      assert is_nil(meter_event.provider_identifier)
      assert is_nil(meter_event.provider_created_at)

      # Mock the Stripe API call for the sync action
      identifier = "me_#{Faker.String.base64(14)}"
      created_at = DateTime.utc_now()

      expect(Provider, :create_meter_event, fn params ->
        # Verify the parameters sent to Stripe
        assert params.event_name == "skiptrace"
        assert params.payload.stripe_customer_id == customer.provider_customer_id
        assert params.payload.value == 5
        assert is_integer(params.timestamp)

        # Return a successful response
        {:ok,
         %{
           created: DateTime.to_unix(created_at),
           event_name: "skiptrace",
           identifier: identifier,
           livemode: false,
           object: "billing.meter_event",
           payload: %{
             "stripe_customer_id" => customer.provider_customer_id,
             "value" => 5
           },
           timestamp: params.timestamp
         }}
      end)

      # Call the sync_meter_events action directly using Ash.update
      {:ok, %MeterEvent{} = updated_meter_event} =
        Ash.update(meter_event, %{}, action: :sync_meter_events)

      # Verify the meter event was updated with provider information
      assert updated_meter_event.provider_identifier == identifier
      assert updated_meter_event.provider_created_at == DateTime.truncate(created_at, :second)
    end

    test "handles Stripe API errors during sync" do
      # Create a customer for testing
      customer = generate(BillingGen.customer(source: :org))

      # Create a meter event
      {:ok, meter_event} = Meters.create_meter_event(customer, :skiptrace, 5)

      # Mock the Stripe API call to return an error
      expect(Provider, :create_meter_event, fn _params ->
        {:error, %{message: "Invalid API key provided"}}
      end)

      # Call the sync_meter_events action directly using Ash.update and expect an error
      {:error, error} =
        Ash.update(meter_event, %{}, action: :sync_meter_events)

      # Verify it's an Unknown error with the right message
      assert %Ash.Error.Unknown{} = error
    end
  end
end
