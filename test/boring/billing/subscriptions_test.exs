defmodule Boring.Billing.SubscriptionsTest do
  use Boring.DataCase, async: true

  import Ash.Generator, only: [generate: 1]

  alias Ash.Error.Invalid
  alias Boring.Billing.Generator, as: BillingGen
  alias Boring.Billing.Subscriptions
  alias Boring.Billing.Types.SubscriptionStatus

  describe "Subscriptions.create_subscription/2" do
    test "creates with valid params" do
      customer = generate(BillingGen.customer(source: :org))

      params = %{
        status: Enum.random(SubscriptionStatus.values()),
        plan_id: "stripe-test-plan-a-monthly",
        current_period_start: DateTime.utc_now(),
        provider_subscription_id: "sub_#{Faker.String.base64(25)}",
        provider_subscription_items: [
          %{
            price_id: "price_1OQj8pIWVkWpNCp74VstFtnd",
            product_id: "prod_PFDZyFfhgGUNOg"
          }
        ],
        billing_customer_id: customer.id
      }

      assert Subscriptions.create_subscription!(params)
    end

    test "raises error with invalid params" do
      customer = generate(BillingGen.customer(source: :org))

      params = %{
        status: Enum.random(SubscriptionStatus.values()),
        billing_customer_id: customer.id
      }

      assert_raise Ash.Error.Invalid, fn ->
        Subscriptions.create_subscription!(params)
      end
    end
  end

  describe "Subscriptions.get_subscription/2" do
    test "returns record with valid id" do
      subscription = generate(BillingGen.subscription())

      assert Subscriptions.get_subscription!(subscription.id)
    end

    test "raises with invalid id" do
      assert_raise Invalid, fn ->
        Subscriptions.get_subscription!(Ash.UUIDv7.generate())
      end
    end
  end

  describe "Subscriptions.get_subscription_by_provider_subscription_id/2" do
    test "returns record with valid id" do
      subscription = generate(BillingGen.subscription())

      subscription =
        Subscriptions.get_subscription_by_provider_subscription_id!(
          subscription.provider_subscription_id,
          authorize?: false
        )

      assert subscription
      assert Ash.Resource.loaded?(subscription, :customer)
    end

    test "raises with invalid id" do
      assert_raise Invalid, fn ->
        Subscriptions.get_subscription_by_provider_subscription_id!(
          "sub_#{Faker.String.base64(25)}",
          authorize?: false
        )
      end
    end
  end

  describe "Subscriptions.get_active_subscription_by_customer_id/2" do
    test "returns record with valid id and active subscription" do
      subscription = generate(BillingGen.subscription(status: "active"))

      assert Subscriptions.get_active_subscription_by_customer_id!(
               subscription.billing_customer_id,
               authorize?: false
             )
    end

    test "raises NotFound error with valid id and inactive subscription" do
      subscription = generate(BillingGen.subscription(status: "canceled"))

      assert_raise Invalid, fn ->
        Subscriptions.get_active_subscription_by_customer_id!(
          subscription.billing_customer_id,
          authorize?: false
        )
      end
    end

    test "raises with invalid id" do
      assert_raise Invalid, fn ->
        Subscriptions.get_active_subscription_by_customer_id!(
          Ash.UUIDv7.generate(),
          authorize?: false
        )
      end
    end
  end

  describe "Subscriptions.cancel_subscription/1" do
    test "cancels subscription" do
      subscription =
        [status: "active"]
        |> BillingGen.subscription()
        |> generate()

      assert subscription.status == "active"
      assert is_nil(subscription.canceled_at)

      canceled_subscription = Subscriptions.cancel_subscription!(subscription)

      assert canceled_subscription.status == "canceled"
      assert not is_nil(canceled_subscription.canceled_at)
    end
  end
end
