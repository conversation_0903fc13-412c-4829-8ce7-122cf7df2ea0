defmodule Boring.Billing.CustomersTest do
  use Boring.DataCase, async: true

  import Ash.Generator, only: [generate: 1]

  alias Ash.Error.Invalid
  alias Boring.Billing.Customers
  alias Boring.Billing.Generator, as: BillingGen
  alias Boring.Orgs.Generator, as: OrgsGen

  describe "Customers.entity/1" do
    test "returns the default billing entity from config" do
      assert PetalPro.config(:billing_entity) == Customers.entity!()
    end
  end

  describe "Customers.get_customer_by_source/3" do
    test "returns matching for org source and source_id" do
      customer = generate(BillingGen.customer(source: :org))
      assert customer.id == Customers.get_customer_by_source!(:org, customer.org_id).id
    end

    test "returns matching for user source and source_id" do
      customer = generate(BillingGen.customer(source: :user))
      assert customer.id == Customers.get_customer_by_source!(:user, customer.user_id).id
    end

    test "returns nothing when source_id are invalid" do
      assert_raise Invalid, fn ->
        Customers.get_customer_by_source!(:org, Ash.UUIDv7.generate())
      end
    end

    test "returns nothing when soruce and source_id types don't match" do
      customer = generate(BillingGen.customer(source: :org))

      assert_raise Invalid, fn ->
        Customers.get_customer_by_source!(:user, customer.org_id)
      end
    end
  end

  describe "Customers.get_customer_by_provider_customer_id/2" do
    test "returns when provider_customer_id is valid" do
      customer = generate(BillingGen.customer(source: :org))

      assert customer.id ==
               Customers.get_customer_by_provider_customer_id!(customer.provider_customer_id).id
    end

    test "returns nothing when provider_customer_id is invalid" do
      assert_raise Invalid, fn ->
        Customers.get_customer_by_provider_customer_id!("cus_#{Faker.String.base64(14)}")
      end
    end
  end

  describe "Customer.create_customer/2" do
    test "creates org source customer with valid params" do
      org = generate(OrgsGen.org())

      params = %{
        email: Faker.Internet.email(),
        provider: "stripe",
        provider_customer_id: "cus_#{Faker.String.base64(14)}",
        source_id: org.id,
        source: :org
      }

      customer = Customers.create_customer!(params, authorize?: false)

      assert customer.source_id == org.id
      assert customer.source == :org
      assert customer.org_id == org.id
      assert customer.user_id == nil
      assert Ash.Resource.loaded?(customer, [:org])
    end

    test "creates user source customer with valid params" do
      user = PetalPro.Accounts.UserSeeder.random_user()

      params = %{
        email: Faker.Internet.email(),
        provider: "stripe",
        provider_customer_id: "cus_#{Faker.String.base64(14)}",
        source_id: user.id,
        source: :user
      }

      customer = Customers.create_customer!(params, authorize?: false)

      assert customer.source_id == user.id
      assert customer.source == :user
      assert customer.user_id == user.id
      assert customer.org_id == nil
      assert Ash.Resource.loaded?(customer, [:user])
    end
  end
end
