defmodule Boring.Workers.ApifyGooglePlaceIngestWorkerTest do
  use Boring.DataCase, async: true

  alias Boring.Workers.ApifyGooglePlaceIngestWorker, as: Worker

  # Sample (pruned) data from Apify
  @data %{
    "imageUrls" => [
      "https://lh5.googleusercontent.com/p/AF1QipPiY-MUmWU5V1d68smBkI-jQCz9M3JYFv5JiilX=w1920-h1080-k-no"
    ],
    "reviewsCount" => 3,
    "scrapedAt" => "2025-02-11T02:32:33.206Z",
    "location" => %{"lat" => 33.4876136, "lng" => -90.1994413},
    "temporarilyClosed" => true,
    "cid" => "452542230817510584",
    "placeId" => "ChIJmSc5T_VtKoYRuJS94cLARwY",
    "claimThisBusiness" => true,
    "openingHours" => [
      %{"day" => "Monday", "hours" => "Open 24 hours"},
      %{"day" => "Tuesday", "hours" => "Open 24 hours"},
      %{"day" => "Wednesday", "hours" => "Open 24 hours"},
      %{"day" => "Thursday", "hours" => "Open 24 hours"},
      %{"day" => "Friday", "hours" => "Open 24 hours"},
      %{"day" => "Saturday", "hours" => "Open 24 hours"},
      %{"day" => "Sunday", "hours" => "Closed"}
    ],
    "city" => "Greenwood",
    "website" => "http://www.extraroomstoragems.com/",
    "reviews" => [
      %{
        "isLocalGuide" => false,
        "likesCount" => 0,
        "name" => "Carter Perry",
        "originalLanguage" => "en",
        "publishAt" => "2 years ago",
        "publishedAtDate" => "2023-01-11T01:08:21.719Z",
        "rating" => nil,
        "responseFromOwnerDate" => nil,
        "responseFromOwnerText" => nil,
        "reviewContext" => %{},
        "reviewDetailedRating" => %{},
        "reviewId" => "ChdDSUhNMG9nS0VJQ0FnSURCMk1MN3h3RRAB",
        "reviewImageUrls" => [],
        "reviewOrigin" => "Google",
        "reviewUrl" =>
          "https://www.google.com/maps/reviews/data=!4m8!14m7!1m6!2m5!1sChdDSUhNMG9nS0VJQ0FnSURCMk1MN3h3RRAB!2m1!1s0x0:0x647c0c2e1bd94b8!3m1!1s2@1:CIHM0ogKEICAgIDB2ML7xwE%7CCgwIhZj4nQYQuOTz1gI%7C?hl=en-US",
        "reviewerId" => "100278232853899543864",
        "reviewerNumberOfReviews" => 2,
        "reviewerPhotoUrl" =>
          "https://lh3.googleusercontent.com/a/ACg8ocKuJPoptYAn6q8PqoLANExcruMurrncud9JfoFKqY1TxI1A=s1920-c-rp-mo-br100",
        "reviewerUrl" => "https://www.google.com/maps/contrib/100278232853899543864?hl=en-US",
        "stars" => 5,
        "text" => "Great family owned business, the grounds are well kept and great size options as well!",
        "textTranslated" => nil,
        "translatedLanguage" => nil,
        "visitedIn" => nil
      }
    ],
    "street" => "119 Highlanddale Rd",
    "categories" => ["Storage facility"],
    "reviewsTags" => [],
    "reviewsDistribution" => %{
      "fiveStar" => 3,
      "fourStar" => 0,
      "oneStar" => 0,
      "threeStar" => 0,
      "twoStar" => 0
    },
    "description" => "A description",
    "countryCode" => "US",
    "permanentlyClosed" => false,
    "phone" => "(*************",
    "url" =>
      "https://www.google.com/maps/search/?api=1&query=Extra%20Room%20Storage&query_place_id=ChIJmSc5T_VtKoYRuJS94cLARwY",
    "rank" => 2,
    "postalCode" => "38930",
    "address" => "119 Highlanddale Rd, Greenwood, MS 38930",
    "totalScore" => 5,
    "state" => "Mississippi",
    "title" => "Extra Room Storage",
    "additionalInfo" => %{
      "Accessibility" => [
        %{"Wheelchair accessible entrance" => true},
        %{"Wheelchair accessible parking lot" => true}
      ],
      "Service options" => [%{"Onsite services" => true}]
    }
  }

  test "strips out nil and falsy values" do
    Enum.each(
      %{
        place_id: "placeId",
        customer_id: "cid",
        maps_url: "url",
        google_state: "state",
        google_country: "countryCode",
        review_count: "reviewsCount",
        website: "website",
        city: "city",
        phone_number: "phone",
        business_name: "title",
        street: "street",
        review_score: "totalScore",
        postal_code: "postalCode",
        full_address: "address",
        description: "description",
        latitude: "location",
        longitude: "location",
        categories: "categories",
        review_distribution: "reviewsDistribution",
        reviews: "reviews",
        review_tags: "reviewsTags",
        opening_hours: "openingHours",
        additional_info: "additionalInfo",
        result_rank: "rank",
        permanently_closed: "permanentlyClosed",
        temporarily_closed: "temporarilyClosed",
        image_urls: "imageUrls"
      },
      fn {local_key, remote_key} ->
        parsed = Worker.parse_place_data(%{@data | remote_key => nil}, %{state_id: nil})
        refute Map.has_key?(parsed, local_key)
      end
    )
  end

  test "parses data for insertion into the database" do
    expected = %{
      place_id: "ChIJmSc5T_VtKoYRuJS94cLARwY",
      customer_id: "452542230817510584",
      maps_url:
        "https://www.google.com/maps/search/?api=1&query=Extra%20Room%20Storage&query_place_id=ChIJmSc5T_VtKoYRuJS94cLARwY",
      google_state: "Mississippi",
      google_country: "US",
      review_count: 3,
      website: "http://www.extraroomstoragems.com/",
      city: "Greenwood",
      phone_number: "(*************",
      business_name: "Extra Room Storage",
      street: "119 Highlanddale Rd",
      review_score: 5,
      postal_code: "38930",
      full_address: "119 Highlanddale Rd, Greenwood, MS 38930",
      description: "A description",
      latitude: 33.4876136,
      longitude: -90.1994413,
      categories: ["Storage facility"],
      review_distribution: %{
        "five_star" => 3,
        "four_star" => 0,
        "one_star" => 0,
        "three_star" => 0,
        "two_star" => 0
      },
      reviews: [
        %{
          "is_local_guide" => false,
          "likes_count" => 0,
          "name" => "Carter Perry",
          "original_language" => "en",
          "publish_at" => "2 years ago",
          "published_at_date" => "2023-01-11T01:08:21.719Z",
          "rating" => nil,
          "response_from_owner_date" => nil,
          "response_from_owner_text" => nil,
          "review_context" => %{},
          "review_detailed_rating" => %{},
          "review_id" => "ChdDSUhNMG9nS0VJQ0FnSURCMk1MN3h3RRAB",
          "review_image_urls" => [],
          "review_origin" => "Google",
          "review_url" =>
            "https://www.google.com/maps/reviews/data=!4m8!14m7!1m6!2m5!1sChdDSUhNMG9nS0VJQ0FnSURCMk1MN3h3RRAB!2m1!1s0x0:0x647c0c2e1bd94b8!3m1!1s2@1:CIHM0ogKEICAgIDB2ML7xwE%7CCgwIhZj4nQYQuOTz1gI%7C?hl=en-US",
          "reviewer_id" => "100278232853899543864",
          "reviewer_number_of_reviews" => 2,
          "reviewer_photo_url" =>
            "https://lh3.googleusercontent.com/a/ACg8ocKuJPoptYAn6q8PqoLANExcruMurrncud9JfoFKqY1TxI1A=s1920-c-rp-mo-br100",
          "reviewer_url" => "https://www.google.com/maps/contrib/100278232853899543864?hl=en-US",
          "stars" => 5,
          "text" => "Great family owned business, the grounds are well kept and great size options as well!",
          "text_translated" => nil,
          "translated_language" => nil,
          "visited_in" => nil
        }
      ],
      review_tags: [],
      opening_hours: [
        %{"day" => "Monday", "hours" => "Open 24 hours"},
        %{"day" => "Tuesday", "hours" => "Open 24 hours"},
        %{"day" => "Wednesday", "hours" => "Open 24 hours"},
        %{"day" => "Thursday", "hours" => "Open 24 hours"},
        %{"day" => "Friday", "hours" => "Open 24 hours"},
        %{"day" => "Saturday", "hours" => "Open 24 hours"},
        %{"day" => "Sunday", "hours" => "Closed"}
      ],
      additional_info: %{
        "Accessibility" => [
          %{"Wheelchair accessible entrance" => true},
          %{"Wheelchair accessible parking lot" => true}
        ],
        "Service options" => [%{"Onsite services" => true}]
      },
      result_rank: 2,
      temporarily_closed: true,
      permanently_closed: false,
      image_urls: [
        "https://lh5.googleusercontent.com/p/AF1QipPiY-MUmWU5V1d68smBkI-jQCz9M3JYFv5JiilX=w1920-h1080-k-no"
      ],
      is_business_claimed: false,
      state_id: "MS"
    }

    assert Worker.parse_place_data(@data, %{state_id: "MS"}) == expected
  end
end
