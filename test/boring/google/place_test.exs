defmodule Boring.Google.PlaceTest do
  use Boring.DataCase, async: true

  import Ash.Generator, only: [generate: 1, generate_many: 2]

  alias Boring.Acquisitions
  alias Boring.Google
  alias PetalPro.Accounts.UserSeeder

  describe "policies" do
    test "admins can do all actions" do
      admin = UserSeeder.admin()
      places = generate_many(Google.Generator.place(), 10)
      place = Enum.at(places, 0)

      assert Ash.count!(Google.Place, actor: admin) == 10
      assert Google.can_create_place?(admin, %{})
      assert Google.can_update_place?(admin, place)
      assert Google.can_delete_place?(admin, place)
      assert Google.can_list_opportunity_candidates?(admin)
    end

    test "users can only read" do
      user = UserSeeder.random_user()
      places = generate_many(Google.Generator.place(), 10)
      place = Enum.at(places, 0)

      assert Ash.count!(Google.Place, actor: user) == 10
      refute Google.can_create_place?(user, %{})
      refute Google.can_update_place?(user, place)
      refute Google.can_delete_place?(user, place)
    end
  end

  describe "Boring.Google.update_place/1-2" do
    test "updates provided attributes" do
      place = generate(Google.Generator.place())

      original_name = place.business_name
      new_name = Faker.Company.name()

      updated =
        Google.update_place!(
          place,
          %{business_name: new_name},
          authorize?: false
        )

      assert original_name != updated.business_name
      assert new_name == updated.business_name
    end
  end

  describe "Boring.Google.delete_place/1-2" do
    test "deletes a place" do
      places = generate_many(Google.Generator.place(review_count: 1), 1)

      Google.delete_place!(places, authorize?: false, bulk_options: [return_errors?: true])
    end
  end

  describe "Boring.Google.get_place/1-2" do
    test "returns a place by id" do
      [place | _] = generate_many(Google.Generator.place(), 2)

      selected_place = Google.get_place!(place.id)

      assert selected_place.id == place.id
    end
  end

  describe "Boring.Google.list_opportunity_candidates/1-2" do
    test "no candidates returned when missing required categories" do
      generate(Google.Generator.place(categories: []))

      candidates = Google.list_opportunity_candidates!(authorize?: false)

      assert Enum.empty?(candidates)
    end

    test "no candidates returned when name matches OpportunityExclusion" do
      business_name = "ExtraSpace"
      generate(Acquisitions.Generator.opportunity_exclusion(name: business_name))
      generate(Google.Generator.place(business_name: business_name))

      candidates = Google.list_opportunity_candidates!()
      assert Enum.empty?(candidates)
    end

    test "no candidates returned when opportunities are associated" do
      place = generate(Google.Generator.place())
      generate(Acquisitions.Generator.opportunity(google_place: place))

      candidates = Google.list_opportunity_candidates!()
      assert Enum.empty?(candidates)
    end

    test "candidates returned when all criteria is met" do
      valid_candidate =
        [review_count: 1]
        |> Google.Generator.place()
        |> generate()
        |> Ash.load!([:opportunity_exclusions, :related_opportunity])

      returned_candidates = Google.list_opportunity_candidates!()
      returned_candidate = Enum.at(returned_candidates, 0)

      assert Enum.count(returned_candidates) == 1
      assert valid_candidate.id == returned_candidate.id
    end
  end

  describe "Boring.Google.convert_to_opportunity/1-2" do
    test "accepts valid AU/US phone numbers" do
      place = generate(Google.Generator.place(phone_number: "+61 412 345 678"))
      assert Boring.Google.convert_to_opportunity!(place, authorize?: false)

      opportunity = Ash.load!(place, :related_opportunity).related_opportunity
      assert opportunity.phone_number == "+61412345678"
    end

    test "drops invalid phone numbers" do
      place = generate(Google.Generator.place(phone_number: "**********"))
      assert Boring.Google.convert_to_opportunity!(place, authorize?: false)

      opportunity = Ash.load!(place, :related_opportunity).related_opportunity
      assert opportunity.phone_number == nil
    end
  end
end
