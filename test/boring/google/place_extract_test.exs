defmodule Boring.Google.PlaceExtractTest do
  use Boring.DataCase, async: true

  import Ash.Generator, only: [generate: 1]

  alias Boring.Google
  alias PetalPro.Accounts.UserSeeder

  describe "policies" do
    test "admins can do all actions" do
      admin = UserSeeder.admin()

      extract =
        Google.Generator.place_extract()
        |> generate()
        |> Ash.load!([:state])

      assert Google.can_create_place_extract?(admin, %{})
      assert Google.can_update_place_extract?(admin, extract)
      assert Google.can_delete_place_extract?(admin, extract)
      assert Google.can_list_place_extracts?(admin)
      assert Google.can_list_active_place_extracts?(admin)
      assert Google.can_get_place_extract?(admin, extract.id)
      assert Google.can_get_place_extract_by_actor_run_id?(admin, extract.last_actor_run_id)
      assert Google.can_get_place_extract_by_state?(admin, extract.state.name)
      assert Google.can_start_extract?(admin, extract)
      assert Google.can_ingest_extract_data?(admin, extract)
    end

    test "users cannot do anything" do
      user = UserSeeder.random_user()

      extract =
        Google.Generator.place_extract()
        |> generate()
        |> Ash.load!([:state])

      refute Google.can_create_place_extract?(user, %{})
      refute Google.can_update_place_extract?(user, extract)
      refute Google.can_delete_place_extract?(user, extract)
      refute Google.can_list_place_extracts?(user)
      refute Google.can_list_active_place_extracts?(user)
      refute Google.can_get_place_extract?(user, extract.id)
      refute Google.can_get_place_extract_by_actor_run_id?(user, extract.last_actor_run_id)
      refute Google.can_get_place_extract_by_state?(user, extract.state.name)
      refute Google.can_start_extract?(user, extract)
      refute Google.can_ingest_extract_data?(user, extract)
    end
  end
end
