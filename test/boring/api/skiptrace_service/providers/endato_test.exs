defmodule Boring.Api.SkiptraceService.Providers.EndatoTest do
  use Boring.DataCase, async: true
  use Mimic

  import Ash.Generator, only: [generate: 1]

  alias Boring.Acquisitions.Generator, as: AcquisitionsGen
  alias Boring.Api.Endato
  alias Boring.Api.SkiptraceService.Providers.Endato, as: EndatoProvider
  alias Boring.Api.SkiptraceService.Result
  alias Boring.Services.Generator, as: ServicesGen

  # Example response from Endato Property Search V2 API with individual owners (based on real API structure)
  @property_search_response_individuals [
    %{
      "poseidonId" => 404_699_668_359_961_000,
      "property" => %{
        "summary" => %{
          "originalApn" => "115021000000017001",
          "apn" => "115021000000017001",
          "purchasePrice" => %{
            "price" => nil,
            "date" => nil
          },
          "assessedValue" => %{
            "price" => 68_096,
            "year" => "2024"
          },
          "currentOwners" => [
            %{
              "isCorporationOrBusiness" => false,
              "name" => %{
                "companyCode" => nil,
                "companyName" => nil,
                "md5Hash" => "-5200771310587995220",
                "fullName" => "John Smith",
                "firstName" => "John",
                "middleName" => "",
                "lastName" => "Smith",
                "suffix" => "",
                "tahoeId" => "G7265777640265733778"
              },
              "transactionBatchDate" => "3/4/2024"
            }
          ],
          "previousOwners" => [
            %{
              "transactionBatchDate" => "8/24/2022",
              "name" => %{
                "companyCode" => nil,
                "companyName" => nil,
                "md5Hash" => "7532853420995397157",
                "fullName" => "Jane Doe",
                "firstName" => "Jane",
                "middleName" => "",
                "lastName" => "Doe",
                "suffix" => "",
                "tahoeId" => "G-5938731845396562185"
              }
            }
          ],
          "address" => %{
            "md5Hash" => "2337292303581311707",
            "houseNumber" => "123",
            "streetDirection" => "",
            "streetName" => "Main",
            "streetType" => "St",
            "unitType" => "",
            "unit" => "",
            "streetPostDirection" => "",
            "city" => "Springfield",
            "state" => "IL",
            "county" => "Sangamon",
            "zipCode" => "62701",
            "zip4" => "1234",
            "latitude" => "37.952",
            "longitude" => "-92.267",
            "addressType" => "COMMERCIAL",
            "careOfName" => "",
            "cityStateZipSource" => "",
            "optOutIndicator" => "",
            "deliveryPointValidationCode" => "AS01",
            "deliveryPointValidationVacantIndicator" => "",
            "addressLine1" => "123 Main ST",
            "addressLine2" => "Springfield , IL 62701-1234",
            "fullAddress" => "123 Main ST ; Springfield , IL 62701-1234"
          }
        }
      }
    }
  ]

  # Example response with mixed individual and business owners (based on real API structure)
  @property_search_response_mixed [
    %{
      "poseidonId" => 1_023_315_101_507_285_000,
      "property" => %{
        "summary" => %{
          "originalApn" => "*********",
          "apn" => "*********",
          "purchasePrice" => %{
            "price" => 1_750_000,
            "date" => "12/29/2014"
          },
          "assessedValue" => %{
            "price" => 5_128_243,
            "year" => "2023"
          },
          "currentOwners" => [
            %{
              "isCorporationOrBusiness" => false,
              "name" => %{
                "companyCode" => nil,
                "companyName" => nil,
                "md5Hash" => "-762249306050880328",
                "fullName" => "Alice Johnson",
                "firstName" => "Alice",
                "middleName" => "",
                "lastName" => "Johnson",
                "suffix" => "",
                "tahoeId" => "G-6663542170969669069"
              },
              "transactionBatchDate" => "7/19/2024"
            },
            %{
              "isCorporationOrBusiness" => false,
              "name" => %{
                "companyCode" => nil,
                "companyName" => nil,
                "md5Hash" => "8913233775210390090",
                "fullName" => "Bob Wilson",
                "firstName" => "Bob",
                "middleName" => "",
                "lastName" => "Wilson",
                "suffix" => "",
                "tahoeId" => nil
              },
              "transactionBatchDate" => "7/19/2024"
            }
          ],
          "currentOwnerMetaData" => %{
            "mailingAddresses" => [
              %{
                "addressLine1" => "456 Business BLVD",
                "city" => "Chicago",
                "state" => "IL",
                "zipCode" => "60601"
              }
            ]
          },
          "previousOwners" => [
            %{
              "transactionBatchDate" => "11/4/2004",
              "name" => %{
                "companyCode" => nil,
                "companyName" => "Storage Company LLC",
                "md5Hash" => "-6779857960866408530",
                "fullName" => "Storage Company LLC",
                "firstName" => "Storage",
                "middleName" => "",
                "lastName" => "Company LLC",
                "suffix" => "",
                "tahoeId" => nil
              }
            }
          ],
          "address" => %{
            "md5Hash" => "106413147932153793",
            "houseNumber" => "456",
            "streetDirection" => "",
            "streetName" => "Business",
            "streetType" => "Blvd",
            "unitType" => "",
            "unit" => "",
            "streetPostDirection" => "",
            "city" => "Chicago",
            "state" => "IL",
            "county" => "Cook",
            "zipCode" => "60601",
            "zip4" => "5678",
            "latitude" => "0",
            "longitude" => "0",
            "addressType" => nil,
            "careOfName" => "",
            "cityStateZipSource" => "",
            "optOutIndicator" => "",
            "deliveryPointValidationCode" => "AS01",
            "deliveryPointValidationVacantIndicator" => "",
            "addressLine1" => "456 Business BLVD",
            "addressLine2" => "Chicago , IL 60601-5678",
            "fullAddress" => "456 Business BLVD ; Chicago , IL 60601-5678"
          }
        }
      }
    }
  ]

  setup do
    Mimic.copy(Endato)
    Mimic.copy(PetalPro)
    :ok
  end

  # Example Contact Enrichment response (based on real API response format)
  @contact_id_response %{
    "name" => %{
      "firstName" => "John",
      "middleName" => "",
      "lastName" => "Smith"
    },
    "age" => "35",
    "phones" => [
      %{
        "number" => "(*************",
        "type" => "mobile",
        "isConnected" => true,
        "firstReportedDate" => "1/1/2020",
        "lastReportedDate" => "4/1/2025"
      },
      %{
        "number" => "(*************",
        "type" => "landline",
        "isConnected" => false,
        "firstReportedDate" => "6/1/2019",
        "lastReportedDate" => "12/1/2023"
      }
    ],
    "emails" => [
      %{
        "email" => "<EMAIL>",
        "isValidated" => true,
        "isBusiness" => false
      },
      %{
        "email" => "<EMAIL>",
        "isValidated" => false,
        "isBusiness" => true
      }
    ],
    "addresses" => [
      %{
        "firstReportedDate" => "2/2/2020",
        "lastReportedDate" => "4/1/2025",
        "street" => "123 Main St",
        "unit" => "",
        "city" => "Springfield",
        "state" => "IL",
        "zip" => "62701"
      }
    ]
  }

  setup do
    stub(PetalPro, :config, fn
      :endato ->
        [api_token: "fake_ap_password", api_token_name: "fake_ap_name"]

      [:skiptrace, :providers] ->
        [
          Boring.Api.SkiptraceService.Providers.Endato
        ]

      # Handle other config calls that might occur during test setup
      _other ->
        []
    end)

    # Create test fixtures
    opportunity = generate(AcquisitionsGen.opportunity())
    service_request = generate(ServicesGen.service_request_seed(opportunity_id: opportunity.id))

    %{service_request: %{service_request | opportunity: opportunity}}
  end

  describe "run_skiptrace/1" do
    test "returns valid results for individual owners with contact enrichment", %{
      service_request: service_request
    } do
      # Mock property search
      stub(Endato, :property_search, fn _params ->
        {:ok,
         %Req.Response{
           status: 200,
           body: %{"propertyV2Records" => @property_search_response_individuals}
         }}
      end)

      # Mock contact enrichment for each owner
      stub(Endato, :contact_id, fn person_id ->
        case person_id do
          "G7265777640265733778" ->
            {:ok, %Req.Response{status: 200, body: %{"person" => @contact_id_response}}}

          "G-5938731845396562185" ->
            {:ok,
             %Req.Response{
               status: 200,
               body: %{
                 "person" => %{
                   "name" => %{
                     "firstName" => "Jane",
                     "middleName" => "",
                     "lastName" => "Doe"
                   },
                   "age" => "42",
                   "phones" => [
                     %{
                       "number" => "************",
                       "type" => "mobile",
                       "isConnected" => true
                     }
                   ],
                   "emails" => [
                     %{
                       "email" => "<EMAIL>",
                       "isValidated" => true,
                       "isBusiness" => false
                     }
                   ],
                   "addresses" => [
                     %{
                       "street" => "123 Main ST",
                       "unit" => "",
                       "city" => "Springfield",
                       "state" => "IL",
                       "zip" => "62701"
                     }
                   ]
                 }
               }
             }}

          _ ->
            {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
        end
      end)

      assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
      assert length(results) == 1

      # Check current owner with enriched data
      current_owner = Enum.find(results, &(&1.name == "John Smith"))
      assert current_owner
      assert current_owner.address.street == "123 Main St"
      assert current_owner.address.city == "Springfield"
      assert current_owner.address.state == "IL"
      # Only validated emails are returned
      assert current_owner.emails == ["<EMAIL>"]
      # Only connected phone numbers are returned
      assert current_owner.phone_numbers == ["(*************"]
      assert current_owner.age == "35"
    end

    test "returns only individual owners from mixed results", %{service_request: service_request} do
      stub(Endato, :property_search, fn _params ->
        {:ok,
         %Req.Response{
           status: 200,
           body: %{"propertyV2Records" => @property_search_response_mixed}
         }}
      end)

      # Mock contact enrichment for individual owners only
      stub(Endato, :contact_id, fn person_id ->
        case person_id do
          "G-6663542170969669069" ->
            {:ok,
             %Req.Response{
               status: 200,
               body: %{
                 "person" => %{
                   "name" => %{
                     "firstName" => "Alice",
                     "middleName" => "",
                     "lastName" => "Johnson"
                   },
                   "age" => "45",
                   "phones" => [
                     %{
                       "number" => "************",
                       "type" => "mobile",
                       "isConnected" => true
                     }
                   ],
                   "emails" => [
                     %{
                       "email" => "<EMAIL>",
                       "isValidated" => true,
                       "isBusiness" => false
                     }
                   ],
                   "addresses" => [
                     %{
                       "street" => "456 Business BLVD",
                       "unit" => "",
                       "city" => "Chicago",
                       "state" => "IL",
                       "zip" => "60601"
                     }
                   ]
                 }
               }
             }}

          _ ->
            {:ok, %Req.Response{status: 404, body: %{"error" => "Not found"}}}
        end
      end)

      assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
      assert length(results) == 2

      # Should include both Alice Johnson and Bob Wilson (current owners only)
      # Storage Company LLC is a previous owner and should not be included
      names = Enum.map(results, & &1.name)
      assert "Alice Johnson" in names
      assert "Bob Wilson" in names
      refute "Storage Company LLC" in names

      # Alice Johnson has enriched data (tahoeId present)
      alice = Enum.find(results, &(&1.name == "Alice Johnson"))
      assert alice.emails == ["<EMAIL>"]
      assert alice.phone_numbers == ["************"]
      assert alice.age == "45"

      # Bob Wilson has no enriched data (tahoeId was nil) but should have mailing address
      bob = Enum.find(results, &(&1.name == "Bob Wilson"))
      assert bob.emails == []
      assert bob.phone_numbers == []
      assert bob.age == nil

      # Bob should have the mailing address from currentOwnerMetaData
      assert bob.address == %{
               street: "456 Business BLVD",
               city: "Chicago",
               state: "IL",
               postal_code: "60601"
             }

      assert alice.address == %{
               street: "456 Business BLVD",
               city: "Chicago",
               state: "IL",
               postal_code: "60601"
             }
    end

    test "handles API errors gracefully", %{service_request: service_request} do
      stub(Endato, :property_search, fn _params ->
        {:error, "API error"}
      end)

      assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
    end

    test "handles non-200 HTTP status codes", %{service_request: service_request} do
      stub(Endato, :property_search, fn _params ->
        {:ok, %Req.Response{status: 400, body: %{"error" => "Bad request"}}}
      end)

      assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
    end

    test "handles empty response body", %{service_request: service_request} do
      stub(Endato, :property_search, fn _params ->
        {:ok, %Req.Response{status: 200, body: %{"propertyV2Records" => []}}}
      end)

      assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
    end

    test "handles malformed response structure", %{service_request: service_request} do
      stub(Endato, :property_search, fn _params ->
        {:ok, %Req.Response{status: 200, body: %{"propertyV2Records" => [%{"invalid" => "structure"}]}}}
      end)

      assert {:ok, %Result{contacts: []}} = EndatoProvider.run_skiptrace(service_request)
    end

    test "handles contact enrichment failures gracefully", %{service_request: service_request} do
      stub(Endato, :property_search, fn _params ->
        {:ok,
         %Req.Response{
           status: 200,
           body: %{"propertyV2Records" => @property_search_response_individuals}
         }}
      end)

      # Mock contact enrichment to fail
      stub(Endato, :contact_id, fn _person_id ->
        {:error, "Contact enrichment failed"}
      end)

      # Should return owners with minimal data when contact enrichment fails
      assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
      assert length(results) == 1

      owner = Enum.at(results, 0)
      assert owner.name == "John Smith"
      assert owner.emails == []
      assert owner.phone_numbers == []
      assert owner.age == nil
      assert owner.address == nil
    end

    test "handles partial contact enrichment failures", %{service_request: service_request} do
      stub(Endato, :property_search, fn _params ->
        {:ok,
         %Req.Response{
           status: 200,
           body: %{"propertyV2Records" => @property_search_response_individuals}
         }}
      end)

      # Mock contact enrichment to succeed for one owner and fail for another
      stub(Endato, :contact_id, fn person_id ->
        case person_id do
          "G7265777640265733778" ->
            {:ok, %Req.Response{status: 200, body: %{"person" => @contact_id_response}}}

          "G-5938731845396562185" ->
            {:error, "Contact enrichment failed"}
        end
      end)

      assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)

      assert length(results) == 1

      # Should only include the owner with successful enrichment
      assert Enum.at(results, 0).name == "John Smith"
    end

    test "handles invalid address format", %{service_request: service_request} do
      stub(Endato, :property_search, fn _params ->
        {:ok,
         %Req.Response{
           status: 200,
           body: %{"propertyV2Records" => @property_search_response_individuals}
         }}
      end)

      # Should return owners with minimal data when using test fixtures
      assert {:ok, %Result{contacts: results}} = EndatoProvider.run_skiptrace(service_request)
      assert length(results) == 1

      owner = Enum.at(results, 0)
      assert owner.name == "John Smith"
      assert owner.emails == []
      assert owner.phone_numbers == []
      assert owner.age == nil
      assert owner.address == nil
    end
  end

  describe "integration with SkiptraceService" do
    test "provider is properly configured" do
      providers = PetalPro.config([:skiptrace, :providers])
      assert EndatoProvider in providers
    end
  end
end
