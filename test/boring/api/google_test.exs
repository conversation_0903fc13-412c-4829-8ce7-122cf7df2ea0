defmodule Boring.Api.GoogleTest do
  use ExUnit.Case, async: true

  import Mimic

  alias Boring.Api.Google
  alias Boring.Api.Google.Geocode.GeocodeResponse
  alias Boring.Api.Google.Places.TextSearchResponse

  # Standard mock response for Places API
  @places_response %{
    "places" => [
      %{
        "id" => "ChIJN1t_tDeuEmsRUsoyG83frY4",
        "displayName" => %{
          "text" => "Pizza Palace",
          "languageCode" => "en"
        },
        "formattedAddress" => "123 Main St, New York, NY 10001, USA",
        "location" => %{
          "latitude" => 40.7128,
          "longitude" => -74.0060
        },
        "rating" => 4.5,
        "priceLevel" => "PRICE_LEVEL_MODERATE",
        "businessStatus" => "OPERATIONAL"
      }
    ]
  }

  # Standard mock response for Geocoding API
  @geocode_response %{
    "results" => [
      %{
        "address_components" => [
          %{
            "long_name" => "1600",
            "short_name" => "1600",
            "types" => ["street_number"]
          },
          %{
            "long_name" => "Amphitheatre Parkway",
            "short_name" => "Amphitheatre Pkwy",
            "types" => ["route"]
          }
        ],
        "formatted_address" => "1600 Amphitheatre Parkway, Mountain View, CA 94043, USA",
        "geometry" => %{
          "location" => %{
            "lat" => 37.4224764,
            "lng" => -122.0842499
          },
          "location_type" => "ROOFTOP",
          "viewport" => %{
            "northeast" => %{
              "lat" => 37.4238253802915,
              "lng" => -122.0829009197085
            },
            "southwest" => %{
              "lat" => 37.4211274197085,
              "lng" => -122.0855988802915
            }
          }
        },
        "place_id" => "ChIJ2eUgeAK6j4ARbn5u_wAGqWA",
        "types" => ["street_address"]
      }
    ],
    "status" => "OK"
  }

  # Copy modules for mocking
  setup do
    Mimic.copy(FakeReq)
    Mimic.copy(PetalPro)
    :ok
  end

  # This allows doctests to run by mocking the HTTP requests
  setup :verify_on_exit!

  # Set up global mocks for doctests
  setup do
    stub(FakeReq, :request, fn req, _opts ->
      url = to_string(req.url)

      cond do
        # Places API requests
        String.contains?(url, "places") ->
          {:ok, %Req.Response{status: 200, body: @places_response}}

        # Geocoding API requests
        String.contains?(url, "geocode") or String.contains?(url, "maps/api/geocode") ->
          {:ok, %Req.Response{status: 200, body: @geocode_response}}

        # Default fallback
        true ->
          {:ok, %Req.Response{status: 200, body: @geocode_response}}
      end
    end)

    stub(PetalPro, :config, fn [:google_maps, :api_token] -> "fake_google_api_key_for_tests" end)

    :ok
  end

  # Import the module to test
  doctest Google

  describe "text_search/2" do
    test "returns Places API search results" do
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :json) == %{
                 "textQuery" => "pizza restaurants",
                 "maxResultCount" => 20
               }

        {:ok, %Req.Response{status: 200, body: @places_response}}
      end)

      {:ok, response} = Google.text_search("pizza restaurants")

      assert %TextSearchResponse{} = response
      assert length(response.places) == 1
      assert Enum.at(response.places, 0).display_name.text == "Pizza Palace"
    end

    test "includes custom options in request body" do
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :json) == %{
                 "textQuery" => "hotels",
                 "maxResultCount" => 10,
                 "openNow" => true,
                 "regionCode" => "US"
               }

        {:ok, %Req.Response{status: 200, body: @places_response}}
      end)

      {:ok, response} =
        Google.text_search("hotels", max_result_count: 10, open_now: true, region_code: "US")

      assert %TextSearchResponse{} = response
    end

    test "uses custom field mask" do
      expect(FakeReq, :request, fn _req, opts ->
        # Check that the field mask header is set correctly
        headers = Keyword.get(opts, :headers, [])
        field_mask_header = Enum.find(headers, fn {key, _value} -> key == "X-Goog-FieldMask" end)
        {_, field_mask} = field_mask_header

        assert field_mask == "places.id,places.displayName,places.location"

        {:ok, %Req.Response{status: 200, body: @places_response}}
      end)

      custom_fields = ["places.id", "places.displayName", "places.location"]
      {:ok, _response} = Google.text_search("coffee", fields: custom_fields)
    end

    test "handles API error responses" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:ok,
         %Req.Response{
           status: 400,
           body: %{
             "error" => %{
               "code" => 400,
               "message" => "Invalid request"
             }
           }
         }}
      end)

      {:error, error} = Google.text_search("invalid query")

      assert error["error"]["code"] == 400
      assert error["error"]["message"] == "Invalid request"
    end

    test "handles network errors" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "connection_refused"}}
      end)

      result = Google.text_search("pizza")

      assert result == {:error, %{reason: "connection_refused"}}
    end
  end

  describe "text_search!/2" do
    test "returns Places API search results directly" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:ok, %Req.Response{status: 200, body: @places_response}}
      end)

      response = Google.text_search!("pizza restaurants")

      assert %TextSearchResponse{} = response
      assert length(response.places) == 1
    end

    test "raises an exception when there's an error" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "connection_refused"}}
      end)

      assert_raise RuntimeError, fn ->
        Google.text_search!("pizza")
      end
    end
  end

  describe "geocode/2" do
    test "returns geocoding results for an address" do
      expect(FakeReq, :request, fn _req, opts ->
        params = Keyword.get(opts, :params)
        assert Keyword.get(params, :address) == "1600 Amphitheatre Parkway, Mountain View, CA"
        assert Keyword.get(params, :key) == "fake_google_api_key_for_tests"

        {:ok, %Req.Response{status: 200, body: @geocode_response}}
      end)

      {:ok, response} = Google.geocode("1600 Amphitheatre Parkway, Mountain View, CA")

      assert %GeocodeResponse{} = response
      assert response.status == "OK"
      assert length(response.results) == 1

      assert Enum.at(response.results, 0).formatted_address ==
               "1600 Amphitheatre Parkway, Mountain View, CA 94043, USA"
    end

    test "includes custom options in query parameters" do
      expect(FakeReq, :request, fn _req, opts ->
        params = Keyword.get(opts, :params)
        assert Keyword.get(params, :address) == "New York, NY"
        assert Keyword.get(params, :region) == "us"
        assert Keyword.get(params, :language) == "en"
        assert Keyword.get(params, :key) == "fake_google_api_key_for_tests"

        {:ok, %Req.Response{status: 200, body: @geocode_response}}
      end)

      {:ok, response} = Google.geocode("New York, NY", region: "us", language: "en")

      assert %GeocodeResponse{} = response
    end

    test "handles API error responses" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "results" => [],
             "status" => "ZERO_RESULTS"
           }
         }}
      end)

      {:ok, response} = Google.geocode("Invalid Address")

      assert %GeocodeResponse{} = response
      assert response.status == "ZERO_RESULTS"
      assert response.results == []
    end

    test "handles network errors" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "timeout"}}
      end)

      result = Google.geocode("Test Address")

      assert result == {:error, %{reason: "timeout"}}
    end
  end

  describe "geocode!/2" do
    test "returns geocoding results directly" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:ok, %Req.Response{status: 200, body: @geocode_response}}
      end)

      response = Google.geocode!("1600 Amphitheatre Parkway, Mountain View, CA")

      assert %GeocodeResponse{} = response
      assert length(response.results) == 1
    end

    test "raises an exception when there's an error" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "timeout"}}
      end)

      assert_raise RuntimeError, fn ->
        Google.geocode!("Test Address")
      end
    end
  end

  describe "reverse_geocode/2" do
    test "returns reverse geocoding results for coordinates" do
      expect(FakeReq, :request, fn _req, opts ->
        params = Keyword.get(opts, :params)
        assert Keyword.get(params, :latlng) == "37.4224764,-122.0842499"
        assert Keyword.get(params, :key) == "fake_google_api_key_for_tests"

        {:ok, %Req.Response{status: 200, body: @geocode_response}}
      end)

      {:ok, response} = Google.reverse_geocode({37.4224764, -122.0842499})

      assert %GeocodeResponse{} = response
      assert response.status == "OK"
      assert length(response.results) == 1
    end

    test "includes custom options in query parameters" do
      expect(FakeReq, :request, fn _req, opts ->
        params = Keyword.get(opts, :params)
        # Note: floating point precision
        assert Keyword.get(params, :latlng) == "40.7128,-74.006"
        assert Keyword.get(params, :language) == "en"
        assert Keyword.get(params, :result_type) == "street_address"
        assert Keyword.get(params, :key) == "fake_google_api_key_for_tests"

        {:ok, %Req.Response{status: 200, body: @geocode_response}}
      end)

      {:ok, response} =
        Google.reverse_geocode({40.7128, -74.0060}, language: "en", result_type: "street_address")

      assert %GeocodeResponse{} = response
    end

    test "handles API error responses" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "results" => [],
             "status" => "ZERO_RESULTS"
           }
         }}
      end)

      {:ok, response} = Google.reverse_geocode({0.0, 0.0})

      assert %GeocodeResponse{} = response
      assert response.status == "ZERO_RESULTS"
    end

    test "handles network errors" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "connection_refused"}}
      end)

      result = Google.reverse_geocode({37.4224764, -122.0842499})

      assert result == {:error, %{reason: "connection_refused"}}
    end
  end

  describe "reverse_geocode!/2" do
    test "returns reverse geocoding results directly" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:ok, %Req.Response{status: 200, body: @geocode_response}}
      end)

      response = Google.reverse_geocode!({37.4224764, -122.0842499})

      assert %GeocodeResponse{} = response
      assert length(response.results) == 1
    end

    test "raises an exception when there's an error" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "connection_refused"}}
      end)

      assert_raise RuntimeError, fn ->
        Google.reverse_geocode!({37.4224764, -122.0842499})
      end
    end
  end
end
