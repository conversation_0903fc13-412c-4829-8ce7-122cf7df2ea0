defmodule Boring.Api.ApifyTest do
  use ExUnit.Case, async: true

  import Mimic

  alias Boring.Api.Apify

  # Copy modules for mocking
  setup do
    Mimic.copy(FakeReq)
    Mimic.copy(PetalPro)
    :ok
  end

  # This allows doctests to run by mocking the HTTP requests
  setup :verify_on_exit!

  # Mock the PetalPro.config function to return a fake API token
  setup do
    stub(PetalPro, :config, fn [:apify, :api_token] -> "fake_api_token" end)
    :ok
  end

  # Import the module to test
  doctest Apify

  # Standard mock response for actor runs
  @actor_runs_response %{
    "data" => %{
      "count" => 2,
      "desc" => false,
      "items" => [
        %{
          "id" => "run1",
          "actId" => "actor1",
          "status" => "SUCCEEDED"
        },
        %{
          "id" => "run2",
          "actId" => "actor2",
          "status" => "RUNNING"
        }
      ],
      "limit" => 1000,
      "offset" => 0,
      "total" => 2
    }
  }

  describe "list_runs/0" do
    test "returns a list of actor runs" do
      # Mock the response based on the OpenAPI spec
      # The first argument to Req.request is a Req.Request struct created by new(url: url)
      # The second argument is the options passed to request
      expect(FakeReq, :request, fn _req, _opts ->
        # We don't need to validate the request details here since we're testing the Apify module's behavior
        {:ok, %Req.Response{status: 200, body: @actor_runs_response}}
      end)

      {:ok, response} = Apify.list_runs()

      assert response.status == 200
      assert length(response.body["data"]["items"]) == 2
      assert Enum.at(response.body["data"]["items"], 0)["id"] == "run1"
    end

    test "handles API error responses" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:ok,
         %Req.Response{
           status: 400,
           body: %{
             "error" => %{
               "type" => "invalid_parameter",
               "message" => "Invalid parameter value"
             }
           }
         }}
      end)

      {:ok, response} = Apify.list_runs()

      assert response.status == 400
      assert response.body["error"]["type"] == "invalid_parameter"
    end

    test "handles network errors" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "connection_refused"}}
      end)

      result = Apify.list_runs()

      assert result == {:error, %{reason: "connection_refused"}}
    end
  end

  describe "list_runs!/0" do
    test "returns a list of actor runs directly" do
      # Mock the response based on the OpenAPI spec
      # The first argument to Req.request is a Req.Request struct created by new(url: url)
      # The second argument is the options passed to request
      expect(FakeReq, :request, fn _req, _opts ->
        # We don't need to validate the request details here since we're testing the Apify module's behavior
        {:ok, %Req.Response{status: 200, body: @actor_runs_response}}
      end)

      response = Apify.list_runs!()

      assert response.status == 200
      assert length(response.body["data"]["items"]) == 2
      assert Enum.at(response.body["data"]["items"], 0)["id"] == "run1"
    end

    test "raises an exception when there's an error" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "connection_refused"}}
      end)

      assert_raise MatchError, fn ->
        Apify.list_runs!()
      end
    end
  end

  describe "run_task_async/2" do
    test "runs a task asynchronously" do
      # Mock the response based on the OpenAPI spec
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :json) == %{query: "search term"}
        assert Keyword.get(opts, :path_params) == [task_id: "my_task_id"]

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "data" => %{
               "id" => "task_run_id",
               "actId" => "actor_id",
               "taskId" => "my_task_id",
               "status" => "READY"
             }
           }
         }}
      end)

      {:ok, response} = Apify.run_task_async("my_task_id", %{query: "search term"})

      assert response.status == 200
      assert response.body["data"]["id"] == "task_run_id"
      assert response.body["data"]["taskId"] == "my_task_id"
    end

    test "handles task not found error" do
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [task_id: "nonexistent_task"]

        {:ok,
         %Req.Response{
           status: 404,
           body: %{
             "error" => %{
               "type" => "record_not_found",
               "message" => "Task with id 'nonexistent_task' was not found"
             }
           }
         }}
      end)

      {:ok, response} = Apify.run_task_async("nonexistent_task", %{})

      assert response.status == 404
      assert response.body["error"]["type"] == "record_not_found"
    end

    test "handles network errors" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "timeout"}}
      end)

      result = Apify.run_task_async("my_task_id", %{})

      assert result == {:error, %{reason: "timeout"}}
    end
  end

  describe "run_task_async!/2" do
    test "runs a task asynchronously and returns the response directly" do
      # Mock the response based on the OpenAPI spec
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :json) == %{query: "search term"}
        assert Keyword.get(opts, :path_params) == [task_id: "my_task_id"]

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "data" => %{
               "id" => "task_run_id",
               "actId" => "actor_id",
               "taskId" => "my_task_id",
               "status" => "READY"
             }
           }
         }}
      end)

      response = Apify.run_task_async!("my_task_id", %{query: "search term"})

      assert response.status == 200
      assert response.body["data"]["id"] == "task_run_id"
      assert response.body["data"]["taskId"] == "my_task_id"
    end

    test "raises an exception when there's an error" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "timeout"}}
      end)

      assert_raise MatchError, fn ->
        Apify.run_task_async!("my_task_id", %{})
      end
    end
  end

  describe "run_task_sync/2" do
    test "runs a task synchronously and returns dataset items" do
      # Mock the response based on the OpenAPI spec
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :json) == %{query: "search term"}
        assert Keyword.get(opts, :path_params) == [task_id: "my_task_id"]

        {:ok,
         %Req.Response{
           status: 200,
           body: [
             %{"title" => "Result 1", "url" => "https://example.com/1"},
             %{"title" => "Result 2", "url" => "https://example.com/2"}
           ]
         }}
      end)

      {:ok, response} = Apify.run_task_sync("my_task_id", %{query: "search term"})

      assert response.status == 200
      assert length(response.body) == 2
      assert Enum.at(response.body, 0)["title"] == "Result 1"
    end
  end

  describe "run_task_sync!/2" do
    test "runs a task synchronously and returns dataset items directly" do
      # Mock the response based on the OpenAPI spec
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :json) == %{query: "search term"}
        assert Keyword.get(opts, :path_params) == [task_id: "my_task_id"]

        {:ok,
         %Req.Response{
           status: 200,
           body: [
             %{"title" => "Result 1", "url" => "https://example.com/1"},
             %{"title" => "Result 2", "url" => "https://example.com/2"}
           ]
         }}
      end)

      response = Apify.run_task_sync!("my_task_id", %{query: "search term"})

      assert response.status == 200
      assert length(response.body) == 2
      assert Enum.at(response.body, 0)["title"] == "Result 1"
    end
  end

  describe "resurrect_run/1" do
    test "resurrects a previously finished actor run" do
      # Mock the response based on the OpenAPI spec
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [run_id: "my_run_id"]

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "data" => %{
               "id" => "my_run_id",
               "actId" => "actor_id",
               "status" => "RUNNING"
             }
           }
         }}
      end)

      {:ok, response} = Apify.resurrect_run("my_run_id")

      assert response.status == 200
      assert response.body["data"]["id"] == "my_run_id"
      assert response.body["data"]["status"] == "RUNNING"
    end
  end

  describe "resurrect_run!/1" do
    test "resurrects a previously finished actor run and returns the response directly" do
      # Mock the response based on the OpenAPI spec
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [run_id: "my_run_id"]

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "data" => %{
               "id" => "my_run_id",
               "actId" => "actor_id",
               "status" => "RUNNING"
             }
           }
         }}
      end)

      response = Apify.resurrect_run!("my_run_id")

      assert response.status == 200
      assert response.body["data"]["id"] == "my_run_id"
      assert response.body["data"]["status"] == "RUNNING"
    end
  end

  describe "abort_run/1" do
    test "aborts a running actor run" do
      # Mock the response based on the OpenAPI spec
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [run_id: "my_run_id"]
        # Note: query_params is not a standard Req option, it should be params
        assert Keyword.get(opts, :params) == [gracefully: true]

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "data" => %{
               "id" => "my_run_id",
               "actId" => "actor_id",
               "status" => "ABORTING"
             }
           }
         }}
      end)

      {:ok, response} = Apify.abort_run("my_run_id")

      assert response.status == 200
      assert response.body["data"]["id"] == "my_run_id"
      assert response.body["data"]["status"] == "ABORTING"
    end

    test "handles run not found error" do
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [run_id: "nonexistent_run"]

        {:ok,
         %Req.Response{
           status: 404,
           body: %{
             "error" => %{
               "type" => "record_not_found",
               "message" => "Actor run with id 'nonexistent_run' was not found"
             }
           }
         }}
      end)

      {:ok, response} = Apify.abort_run("nonexistent_run")

      assert response.status == 404
      assert response.body["error"]["type"] == "record_not_found"
    end

    test "handles run already finished error" do
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [run_id: "finished_run"]

        {:ok,
         %Req.Response{
           status: 400,
           body: %{
             "error" => %{
               "type" => "invalid_parameter",
               "message" => "Cannot abort run that is not running"
             }
           }
         }}
      end)

      {:ok, response} = Apify.abort_run("finished_run")

      assert response.status == 400
      assert response.body["error"]["type"] == "invalid_parameter"
    end
  end

  describe "abort_run!/1" do
    test "aborts a running actor run and returns the response directly" do
      # Mock the response based on the OpenAPI spec
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [run_id: "my_run_id"]
        # Note: query_params is not a standard Req option, it should be params
        assert Keyword.get(opts, :params) == [gracefully: true]

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "data" => %{
               "id" => "my_run_id",
               "actId" => "actor_id",
               "status" => "ABORTING"
             }
           }
         }}
      end)

      response = Apify.abort_run!("my_run_id")

      assert response.status == 200
      assert response.body["data"]["id"] == "my_run_id"
      assert response.body["data"]["status"] == "ABORTING"
    end

    test "raises an exception when there's an error" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "connection_refused"}}
      end)

      assert_raise MatchError, fn ->
        Apify.abort_run!("my_run_id")
      end
    end
  end

  describe "get_run_dataset/2" do
    test "gets dataset items from an actor run" do
      # Mock the response based on the OpenAPI spec
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [run_id: "my_run_id"]
        assert Keyword.get(opts, :params) == [limit: 10, offset: 20]

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "data" => %{
               "items" => [
                 %{"title" => "Result 1", "url" => "https://example.com/1"},
                 %{"title" => "Result 2", "url" => "https://example.com/2"}
               ],
               "total" => 2,
               "offset" => 20,
               "limit" => 10,
               "count" => 2
             }
           }
         }}
      end)

      {:ok, response} = Apify.get_run_dataset("my_run_id", limit: 10, offset: 20)

      assert response.status == 200
      assert length(response.body["data"]["items"]) == 2
      assert Enum.at(response.body["data"]["items"], 0)["title"] == "Result 1"
    end

    test "handles run not found error" do
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [run_id: "nonexistent_run"]

        {:ok,
         %Req.Response{
           status: 404,
           body: %{
             "error" => %{
               "type" => "record_not_found",
               "message" => "Actor run with id 'nonexistent_run' was not found"
             }
           }
         }}
      end)

      {:ok, response} = Apify.get_run_dataset("nonexistent_run")

      assert response.status == 404
      assert response.body["error"]["type"] == "record_not_found"
    end

    test "handles dataset not ready error" do
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [run_id: "running_run"]

        {:ok,
         %Req.Response{
           status: 400,
           body: %{
             "error" => %{
               "type" => "dataset_not_ready",
               "message" => "Dataset for this run is not ready yet"
             }
           }
         }}
      end)

      {:ok, response} = Apify.get_run_dataset("running_run")

      assert response.status == 400
      assert response.body["error"]["type"] == "dataset_not_ready"
    end

    test "handles network errors" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "timeout"}}
      end)

      result = Apify.get_run_dataset("my_run_id")

      assert result == {:error, %{reason: "timeout"}}
    end
  end

  describe "get_run_dataset!/2" do
    test "gets dataset items from an actor run and returns the response directly" do
      # Mock the response based on the OpenAPI spec
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :path_params) == [run_id: "my_run_id"]
        assert Keyword.get(opts, :params) == [limit: 10, offset: 20]

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "data" => %{
               "items" => [
                 %{"title" => "Result 1", "url" => "https://example.com/1"},
                 %{"title" => "Result 2", "url" => "https://example.com/2"}
               ],
               "total" => 2,
               "offset" => 20,
               "limit" => 10,
               "count" => 2
             }
           }
         }}
      end)

      response = Apify.get_run_dataset!("my_run_id", limit: 10, offset: 20)

      assert response.status == 200
      assert length(response.body["data"]["items"]) == 2
      assert Enum.at(response.body["data"]["items"], 0)["title"] == "Result 1"
    end

    test "raises an exception when there's an error" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "timeout"}}
      end)

      assert_raise MatchError, fn ->
        Apify.get_run_dataset!("my_run_id")
      end
    end
  end

  describe "new/1" do
    test "creates a new Req request with Apify API configuration" do
      request = Apify.new(url: "/custom-endpoint")

      assert is_struct(request, Req.Request)
      assert request.options[:base_url] == "https://api.apify.com/v2"
      assert request.url.path == "/custom-endpoint"
      assert request.options[:auth] == {:bearer, "fake_api_token"}
    end

    test "merges custom options with default options" do
      request = Apify.new(url: "/custom-endpoint", params: [limit: 10])

      assert is_struct(request, Req.Request)
      assert request.options[:base_url] == "https://api.apify.com/v2"
      assert request.url.path == "/custom-endpoint"
      assert request.options[:params] == [limit: 10]
    end
  end
end
