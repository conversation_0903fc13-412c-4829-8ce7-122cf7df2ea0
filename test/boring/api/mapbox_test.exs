defmodule Boring.Api.MapboxTest do
  use Boring.DataCase, async: true

  alias <PERSON><PERSON>Error.Invalid
  alias Boring.Api.HttpClient
  alias Boring.Api.Mapbox
  alias Boring.Api.Mapbox.Env
  alias Boring.Api.Mapbox.Request

  describe "build/2" do
    test "returns request when given longitude & latitude" do
      Application.put_env(:boring, :mapbox,
        access_token: "YOUR_MAPBOX_ACCESS_TOKEN",
        username: "mapbox"
      )

      assert {:ok,
              %Env{
                request: %Request{
                  latitude: 37.7749,
                  longitude: -122.4194,
                  zoom: 16,
                  width: 1024,
                  height: 1024,
                  style_id: "satellite-v9",
                  url:
                    "https://api.mapbox.com/styles/v1/mapbox/satellite-v9/static/pin-s-l+FF0000(-122.4194,37.7749)/-122.4194,37.7749,16/1024x1024?access_token=YOUR_MAPBOX_ACCESS_TOKEN"
                },
                response: nil
              }} =
               Mapbox.build(%{
                 latitude: 37.7749,
                 longitude: -122.4194
               })
    end

    test "return error when env vars missing" do
      Application.put_env(:boring, :mapbox, [])

      assert {:error,
              %Invalid{
                errors: [
                  %Ash.Error.Changes.InvalidAttribute{
                    message: "Mapbox access token not set in application config"
                  }
                  | _errors
                ]
              }} =
               Mapbox.build(%{
                 latitude: 37.7749,
                 longitude: -122.4194
               })
    end
  end

  describe "get_static_image/1" do
    test "successful request to api" do
      Application.put_env(:boring, :mapbox,
        access_token: "YOUR_MAPBOX_ACCESS_TOKEN",
        username: "mapbox"
      )

      expect(HttpClient, :get, fn _url ->
        {:ok,
         %Req.Response{
           status: 200,
           body: "<some-binary-data>"
         }}
      end)

      env = %Env{
        request: %Request{
          url:
            "https://api.mapbox.com/styles/v1/mapbox/satellite-v9/static/pin-s-l+FF0000(-122.4194,37.7749)/-122.4194,37.7749,16/1024x1024?access_token=YOUR_MAPBOX_ACCESS_TOKEN"
        },
        response: nil
      }

      assert {:ok,
              %Env{
                response: %Boring.Api.Mapbox.Response{
                  image: "<some-binary-data>"
                }
              }} = Mapbox.get_static_image(env)
    end

    test "http client side failure" do
      Application.put_env(:boring, :mapbox,
        access_token: "YOUR_MAPBOX_ACCESS_TOKEN",
        username: "mapbox"
      )

      expect(HttpClient, :get, fn _url ->
        {:error, "some error"}
      end)

      env = %Env{
        request: %Request{
          url:
            "https://api.mapbox.com/styles/v1/mapbox/satellite-v9/static/pin-s-l+FF0000(-122.4194,37.7749)/-122.4194,37.7749,16/1024x1024?access_token=YOUR_MAPBOX_ACCESS_TOKEN"
        },
        response: nil
      }

      assert {:error,
              %Invalid{
                errors: [%Ash.Error.Changes.InvalidChanges{message: "some error"}]
              }} = Mapbox.get_static_image(env)
    end

    test "http server side error" do
      Application.put_env(:boring, :mapbox,
        access_token: "YOUR_MAPBOX_ACCESS_TOKEN",
        username: "mapbox"
      )

      expect(HttpClient, :get, fn _url ->
        {:ok,
         %Req.Response{
           status: 422,
           body: %{"message" => "Width must be between 1-1280."}
         }}
      end)

      env = %Env{
        request: %Request{
          url:
            "https://api.mapbox.com/styles/v1/mapbox/satellite-v9/static/pin-s-l+FF0000(-122.4194,37.7749)/-122.4194,37.7749,16/1024x1024000?access_token=YOUR_MAPBOX_ACCESS_TOKEN"
        },
        response: nil
      }

      assert {:error,
              %{
                errors: [
                  %{
                    field: :response,
                    message: ~s(Error with 422 status and body: %{"message" => "Width must be between 1-1280."}),
                    value: %Req.Response{
                      body: %{"message" => "Width must be between 1-1280."},
                      status: 422
                    }
                  }
                ]
              }} = Mapbox.get_static_image(env)
    end
  end
end
