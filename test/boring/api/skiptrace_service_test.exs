defmodule Boring.Api.SkiptraceServiceTest do
  use Boring.DataCase, async: true
  use Mimic

  import Ash.Generator, only: [generate: 1]

  alias Boring.Acquisitions.Generator, as: AcquisitionsGen
  alias Boring.Api.SkiptraceService
  alias Boring.Api.SkiptraceService.Result
  alias Boring.Services.Generator, as: ServicesGen
  alias Boring.Test.Mocks.MockSkiptraceProvider
  alias Boring.Test.Mocks.MockSkiptraceProviderFailing

  setup do
    Mimic.copy(MockSkiptraceProvider)
    Mimic.copy(MockSkiptraceProviderFailing)
    Mimic.copy(PetalPro)
    :ok
  end

  setup do
    stub(PetalPro, :config, fn
      [:skiptrace, :providers] ->
        [MockSkiptraceProviderFailing, MockSkiptraceProvider]

      _other ->
        []
    end)

    # Create test fixtures
    opportunity = generate(AcquisitionsGen.opportunity())
    service_request = generate(ServicesGen.service_request_seed(opportunity_id: opportunity.id))
    service_request_with_opportunity = %{service_request | opportunity: opportunity}

    stub(MockSki<PERSON>raceProvider, :run_skiptrace, fn service_request ->
      # Check if this is the test opportunity based on street address
      case service_request.opportunity.street do
        "123 Main St" ->
          {:ok,
           %Result{
             contacts: [
               %Result.Contact{
                 name: "<PERSON> Doe",
                 emails: ["<EMAIL>"],
                 phone_numbers: ["************"]
               }
             ],
             raw_result: nil
           }}

        _ ->
          {:ok, %Result{contacts: [], raw_result: nil}}
      end
    end)

    stub(MockSkiptraceProviderFailing, :run_skiptrace, fn _service_request ->
      {:ok, %Result{contacts: [], raw_result: nil}}
    end)

    %{service_request: service_request_with_opportunity}
  end

  doctest SkiptraceService

  describe "run_skiptrace/1" do
    test "returns results when a provider returns valid results", %{
      service_request: service_request
    } do
      # Update the opportunity street to match our test case
      updated_opportunity = %{service_request.opportunity | street: "123 Main St"}
      updated_service_request = %{service_request | opportunity: updated_opportunity}

      assert {:ok, %Result{contacts: [result]}} =
               SkiptraceService.run_skiptrace(updated_service_request)

      assert result.name == "John Doe"
      assert result.emails == ["<EMAIL>"]
      assert result.phone_numbers == ["************"]
    end

    test "returns empty list when no provider returns valid results", %{
      service_request: service_request
    } do
      # Use the service_request with its default opportunity street (won't match our test case)
      assert {:ok, %Result{contacts: []}} = SkiptraceService.run_skiptrace(service_request)
    end

    test "tries multiple providers until one succeeds", %{service_request: service_request} do
      # Configure to use both providers with the failing one first
      stub(PetalPro, :config, fn [:skiptrace, :providers] ->
        [MockSkiptraceProviderFailing, MockSkiptraceProvider]
      end)

      # Update the opportunity street to match our test case
      updated_opportunity = %{service_request.opportunity | street: "123 Main St"}
      updated_service_request = %{service_request | opportunity: updated_opportunity}

      assert {:ok, %Result{contacts: [result]}} =
               SkiptraceService.run_skiptrace(updated_service_request)

      assert result.name == "John Doe"
      assert result.emails == ["<EMAIL>"]
      assert result.phone_numbers == ["************"]
    end
  end
end
