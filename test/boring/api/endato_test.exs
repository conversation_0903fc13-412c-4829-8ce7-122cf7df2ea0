defmodule Boring.Api.EndatoTest do
  use ExUnit.Case, async: true

  import Mimic

  alias Boring.Api.Endato

  # Example response from Endato Property Search V2 API (based on real API structure)
  @property_search_response [
    %{
      "poseidonId" => 404_699_668_359_961_000,
      "property" => %{
        "summary" => %{
          "originalApn" => "115021000000017001",
          "apn" => "115021000000017001",
          "purchasePrice" => %{
            "price" => nil,
            "date" => nil
          },
          "assessedValue" => %{
            "price" => 68_096,
            "year" => "2024"
          },
          "currentOwners" => [
            %{
              "isCorporationOrBusiness" => false,
              "name" => %{
                "companyCode" => nil,
                "companyName" => nil,
                "md5Hash" => "-5200771310587995220",
                "fullName" => "<PERSON> Smith",
                "firstName" => "<PERSON>",
                "middleName" => "",
                "lastName" => "Smith",
                "suffix" => "",
                "tahoeId" => "G7265777640265733778"
              },
              "transactionBatchDate" => "3/4/2024"
            }
          ],
          "currentOwnerMetaData" => %{
            "etalCode" => "",
            "etalCodeDescription" => "",
            "ownershipeRightsCode" => nil,
            "ownershipeRightsCodeDescription" => "",
            "relationshipTypeCode" => "",
            "relationshipTypeCodeDescription" => "",
            "ownerOccupancyCode" => "",
            "ownerOccupancyCodeDescription" => "",
            "mailingAddresses" => [
              %{
                "md5Hash" => "6247041653609689566",
                "houseNumber" => "456",
                "streetDirection" => "",
                "streetName" => "Oak",
                "streetType" => "Ave",
                "unitType" => "",
                "unit" => "",
                "streetPostDirection" => "",
                "city" => "Chicago",
                "state" => "IL",
                "county" => "Cook",
                "zipCode" => "60601",
                "zip4" => "5678",
                "latitude" => nil,
                "longitude" => nil,
                "addressType" => nil,
                "careOfName" => "",
                "cityStateZipSource" => "",
                "optOutIndicator" => "",
                "deliveryPointValidationCode" => "AS01",
                "deliveryPointValidationVacantIndicator" => "",
                "addressLine1" => "456 Oak AVE",
                "addressLine2" => "Chicago , IL 60601-5678",
                "fullAddress" => "456 Oak AVE ; Chicago , IL 60601-5678"
              }
            ]
          },
          "previousOwners" => [
            %{
              "transactionBatchDate" => "8/24/2022",
              "name" => %{
                "companyCode" => nil,
                "companyName" => nil,
                "md5Hash" => "7532853420995397157",
                "fullName" => "Jane Doe",
                "firstName" => "Jane",
                "middleName" => "",
                "lastName" => "Doe",
                "suffix" => "",
                "tahoeId" => "G-5938731845396562185"
              }
            }
          ],
          "principals" => [],
          "address" => %{
            "md5Hash" => "2337292303581311707",
            "houseNumber" => "123",
            "streetDirection" => "",
            "streetName" => "Main",
            "streetType" => "St",
            "unitType" => "",
            "unit" => "",
            "streetPostDirection" => "",
            "city" => "Springfield",
            "state" => "IL",
            "county" => "Sangamon",
            "zipCode" => "62701",
            "zip4" => "1234",
            "latitude" => "37.952",
            "longitude" => "-92.267",
            "addressType" => "COMMERCIAL",
            "careOfName" => "",
            "cityStateZipSource" => "",
            "optOutIndicator" => "",
            "deliveryPointValidationCode" => "AS01",
            "deliveryPointValidationVacantIndicator" => "",
            "addressLine1" => "123 Main ST",
            "addressLine2" => "Springfield , IL 62701-1234",
            "fullAddress" => "123 Main ST ; Springfield , IL 62701-1234"
          },
          "priceHistories" => [],
          "propertyDetails" => %{
            "type" => "COMMERCIAL",
            "apn" => "115021000000017001",
            "block" => " ",
            "lot" => " ",
            "livingArea" => "5760",
            "lotSize" => "174240",
            "beds" => "0",
            "baths" => "0.0",
            "yearBuilt" => "2006",
            "totalRooms" => "0",
            "squareFootage" => "5760"
          },
          "propertyValue" => %{
            "totalValue" => nil,
            "landValue" => nil,
            "improvementValue" => nil,
            "calculatedValueSourceCode" => "",
            "assessedValue" => 68_096,
            "assessedLandValue" => "6720",
            "assessedImprovementValue" => "61376",
            "marketValue" => 212_800,
            "marketLandValue" => "21000",
            "marketImprovementValue" => "191800",
            "appraisedTotalValue" => nil,
            "appraisedLandValue" => "",
            "appraisedImprovementValue" => "",
            "taxAmount" => 3226.73,
            "taxYear" => 2023,
            "assessedYear" => 2024,
            "taxAreaCode" => "PR6W",
            "taxTotalRatePercent" => "",
            "taxableImprovementValue" => "",
            "taxableLandValue" => "",
            "taxableOtherValue" => "",
            "netTaxableAmount" => ""
          },
          "propertyIdentification" => %{
            "fipsCode" => "29169",
            "apnUnformatted" => "115021000000017001",
            "apnSequenceNumber" => "1",
            "compositePropertyLinkageKey" => "291691150210000000170010001",
            "originalApn" => "115021000000017001",
            "taxAccountNumber" => "",
            "onlineFormattedParcelId" => "115021000000017001",
            "alternateParcelId" => "",
            "landUseCode" => "336",
            "landUseCodeDescription" => "MINI WAREHOUSE",
            "countyUseDescr" => "716",
            "stateUseDescr" => "",
            "mobileHomeIndicator" => "",
            "zoningCode" => "",
            "zoningCodeDescription" => "",
            "propertyIndicatorCode" => "20",
            "propertyIndicatorCodeDescription" => "COMMERCIAL",
            "numberOfBuildings" => "3",
            "viewCode" => "",
            "locationInfluenceCode" => "",
            "foreclosureStageCode" => "",
            "lastForeclosureTransactionDate" => ""
          },
          "isOwnerOccupied" => false,
          "score" => 9
        },
        "assessorRecords" => nil,
        "recorderRecords" => nil,
        "openLienRecords" => nil
      }
    }
  ]

  # Copy modules for mocking
  setup do
    Mimic.copy(FakeReq)
    Mimic.copy(PetalPro)
    :ok
  end

  # This allows doctests to run by mocking the HTTP requests
  setup :verify_on_exit!

  # Mock the PetalPro.config function to return fake credentials
  setup do
    stub(PetalPro, :config, fn
      :endato -> [api_token: "fake_api_token", api_token_name: "fake_api_token_name"]
    end)

    :ok
  end

  describe "property_search/2" do
    test "searches for properties by address" do
      search_params = [
        address_line1: "1140 Oak St",
        address_line2: "Brandon, MS"
      ]

      expect(FakeReq, :request, fn _req, opts ->
        expected_json = %{"AddressLine1" => "1140 Oak St", "AddressLine2" => "Brandon, MS"}
        assert Keyword.get(opts, :json) == expected_json
        assert Keyword.get(opts, :method) == :post
        assert {"galaxy-search-type", "PropertyV2"} in Keyword.get(opts, :headers, [])

        {:ok, %Req.Response{status: 200, body: @property_search_response}}
      end)

      {:ok, response} = Endato.property_search(search_params)

      assert response.status == 200
      assert length(response.body) == 1
      assert Enum.at(response.body, 0)["poseidonId"] == 404_699_668_359_961_000

      assert get_in(response.body, [
               Access.at(0),
               "property",
               "summary",
               "address",
               "addressLine1"
             ]) == "123 Main ST"
    end

    test "searches for properties by owner name" do
      search_params = [
        address_line1: "123 Main St",
        address_line2: "Springfield, IL",
        first_name: "John",
        last_name: "Doe"
      ]

      expect(FakeReq, :request, fn _req, opts ->
        expected_json = %{
          "AddressLine1" => "123 Main St",
          "AddressLine2" => "Springfield, IL",
          "FirstName" => "John",
          "LastName" => "Doe"
        }

        assert Keyword.get(opts, :json) == expected_json
        assert Keyword.get(opts, :method) == :post
        assert {"galaxy-search-type", "PropertyV2"} in Keyword.get(opts, :headers, [])

        {:ok, %Req.Response{status: 200, body: @property_search_response}}
      end)

      {:ok, response} = Endato.property_search(search_params)

      assert response.status == 200
      assert length(response.body) == 1
    end

    test "handles API error responses" do
      search_params = [
        address_line1: "Invalid Address",
        address_line2: "Invalid City, ST"
      ]

      expect(FakeReq, :request, fn _req, _opts ->
        {:ok,
         %Req.Response{
           status: 400,
           body: %{
             "error" => %{
               "type" => "invalid_parameter",
               "message" => "Invalid address format"
             }
           }
         }}
      end)

      {:ok, response} = Endato.property_search(search_params)

      assert response.status == 400
      assert response.body["error"]["type"] == "invalid_parameter"
    end

    test "handles network errors" do
      search_params = [
        address_line1: "123 Main St",
        address_line2: "Springfield, IL"
      ]

      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "connection_refused"}}
      end)

      result = Endato.property_search(search_params)

      assert result == {:error, %{reason: "connection_refused"}}
    end
  end

  describe "property_search!/2" do
    test "searches for properties and returns response directly" do
      search_params = [
        address_line1: "1140 Oak St",
        address_line2: "Brandon, MS"
      ]

      expect(FakeReq, :request, fn _req, opts ->
        expected_json = %{"AddressLine1" => "1140 Oak St", "AddressLine2" => "Brandon, MS"}
        assert Keyword.get(opts, :json) == expected_json
        assert Keyword.get(opts, :method) == :post
        assert {"galaxy-search-type", "PropertyV2"} in Keyword.get(opts, :headers, [])

        {:ok, %Req.Response{status: 200, body: @property_search_response}}
      end)

      response = Endato.property_search!(search_params)

      assert response.status == 200
      assert length(response.body) == 1
      assert Enum.at(response.body, 0)["poseidonId"] == 404_699_668_359_961_000
    end
  end

  describe "contact_enrichment/2" do
    test "enriches contact information by name and address" do
      search_params = [
        first_name: "John",
        last_name: "Doe",
        address: %{
          address_line1: "123 Main St",
          address_line2: "Springfield, IL"
        }
      ]

      expect(FakeReq, :request, fn _req, opts ->
        expected_json = %{
          "FirstName" => "John",
          "LastName" => "Doe",
          "Address" => %{
            "AddressLine1" => "123 Main St",
            "AddressLine2" => "Springfield, IL"
          }
        }

        assert Keyword.get(opts, :json) == expected_json
        assert Keyword.get(opts, :method) == :post
        assert {"galaxy-search-type", "DevAPIContactEnrich"} in Keyword.get(opts, :headers, [])

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "name" => "John Doe",
             "age" => 35,
             "phones" => ["************"],
             "emails" => ["<EMAIL>"]
           }
         }}
      end)

      {:ok, response} = Endato.contact_enrichment(search_params)

      assert response.status == 200
      assert response.body["name"] == "John Doe"
      assert response.body["age"] == 35
      assert response.body["phones"] == ["************"]
      assert response.body["emails"] == ["<EMAIL>"]
    end

    test "handles API error responses" do
      search_params = [
        first_name: "Invalid",
        last_name: "Person"
      ]

      expect(FakeReq, :request, fn _req, _opts ->
        {:ok, %Req.Response{status: 404, body: %{"error" => "Person not found"}}}
      end)

      {:ok, response} = Endato.contact_enrichment(search_params)

      assert response.status == 404
      assert response.body["error"] == "Person not found"
    end
  end

  describe "contact_enrichment!/2" do
    test "enriches contact information and returns response directly" do
      search_params = [
        first_name: "Jane",
        last_name: "Smith",
        phone: "************"
      ]

      expect(FakeReq, :request, fn _req, opts ->
        expected_json = %{
          "FirstName" => "Jane",
          "LastName" => "Smith",
          "Phone" => "************"
        }

        assert Keyword.get(opts, :json) == expected_json
        assert Keyword.get(opts, :method) == :post
        assert {"galaxy-search-type", "DevAPIContactEnrich"} in Keyword.get(opts, :headers, [])

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "name" => "Jane Smith",
             "age" => 28,
             "phones" => ["************"],
             "emails" => ["<EMAIL>"]
           }
         }}
      end)

      response = Endato.contact_enrichment!(search_params)

      assert response.status == 200
      assert response.body["name"] == "Jane Smith"
    end
  end

  describe "contact_id/2" do
    test "enriches contact by person ID" do
      person_id = "G12342134234234"

      expect(FakeReq, :request, fn _req, opts ->
        expected_json = %{"PersonId" => person_id}
        assert Keyword.get(opts, :json) == expected_json
        assert Keyword.get(opts, :method) == :post
        assert {"galaxy-search-type", "DevAPIContactID"} in Keyword.get(opts, :headers, [])

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "name" => %{
               "firstName" => "John",
               "middleName" => "Michael",
               "lastName" => "Doe"
             },
             "age" => "35",
             "phones" => [
               %{
                 "number" => "(*************",
                 "type" => "mobile",
                 "isConnected" => true
               }
             ],
             "emails" => [
               %{
                 "email" => "<EMAIL>",
                 "isValidated" => true,
                 "isBusiness" => false
               }
             ]
           }
         }}
      end)

      {:ok, response} = Endato.contact_id(person_id)

      assert response.status == 200
      assert response.body["name"]["firstName"] == "John"
      assert response.body["name"]["lastName"] == "Doe"
      assert response.body["age"] == "35"
    end

    test "handles API error responses" do
      person_id = "invalid_id"

      expect(FakeReq, :request, fn _req, _opts ->
        {:ok, %Req.Response{status: 404, body: %{"error" => "Person not found"}}}
      end)

      {:ok, response} = Endato.contact_id(person_id)

      assert response.status == 404
      assert response.body["error"] == "Person not found"
    end

    test "handles network errors" do
      person_id = "G12342134234234"

      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "connection_refused"}}
      end)

      result = Endato.contact_id(person_id)

      assert result == {:error, %{reason: "connection_refused"}}
    end
  end

  describe "contact_id!/2" do
    test "enriches contact by person ID and returns response directly" do
      person_id = "G12342134234234"

      expect(FakeReq, :request, fn _req, opts ->
        expected_json = %{"PersonId" => person_id}
        assert Keyword.get(opts, :json) == expected_json
        assert Keyword.get(opts, :method) == :post
        assert {"galaxy-search-type", "DevAPIContactID"} in Keyword.get(opts, :headers, [])

        {:ok,
         %Req.Response{
           status: 200,
           body: %{
             "name" => %{
               "firstName" => "Alice",
               "lastName" => "Johnson"
             },
             "age" => "28",
             "phones" => [
               %{
                 "number" => "************",
                 "type" => "mobile",
                 "isConnected" => true
               }
             ],
             "emails" => [
               %{
                 "email" => "<EMAIL>",
                 "isValidated" => true,
                 "isBusiness" => false
               }
             ]
           }
         }}
      end)

      response = Endato.contact_id!(person_id)

      assert response.status == 200
      assert response.body["name"]["firstName"] == "Alice"
      assert response.body["name"]["lastName"] == "Johnson"
    end
  end

  describe "request/2" do
    test "makes a request with search_type in options" do
      expect(FakeReq, :request, fn _req, opts ->
        assert Keyword.get(opts, :method) == :post
        assert Keyword.get(opts, :json) == %{"test" => "data"}
        assert {"galaxy-search-type", "PropertyV2"} in Keyword.get(opts, :headers, [])

        {:ok, %Req.Response{status: 200, body: %{"success" => true}}}
      end)

      {:ok, response} =
        Endato.request("/PropertyV2Search",
          method: :post,
          json: %{"test" => "data"},
          search_type: "PropertyV2"
        )

      assert response.status == 200
      assert response.body["success"] == true
    end

    test "raises error when search_type is missing" do
      assert_raise ArgumentError, "search_type is required for Endato API requests", fn ->
        Endato.request("/PropertyV2Search", method: :post, json: %{"test" => "data"})
      end
    end

    test "handles network errors" do
      expect(FakeReq, :request, fn _req, _opts ->
        {:error, %{reason: "timeout"}}
      end)

      result =
        Endato.request("/PropertyV2Search",
          method: :post,
          json: %{"test" => "data"},
          search_type: "PropertyV2"
        )

      assert result == {:error, %{reason: "timeout"}}
    end
  end

  describe "request!/2" do
    test "makes a request and returns response directly" do
      expect(FakeReq, :request!, fn _req, opts ->
        assert Keyword.get(opts, :method) == :post
        assert Keyword.get(opts, :json) == %{"test" => "data"}
        assert {"galaxy-search-type", "PropertyV2"} in Keyword.get(opts, :headers, [])

        %Req.Response{status: 200, body: %{"success" => true}}
      end)

      response =
        Endato.request!("/PropertyV2Search",
          method: :post,
          json: %{"test" => "data"},
          search_type: "PropertyV2"
        )

      assert response.status == 200
      assert response.body["success"] == true
    end

    test "raises error when search_type is missing" do
      assert_raise ArgumentError, "search_type is required for Endato API requests", fn ->
        Endato.request!("/PropertyV2Search", method: :post, json: %{"test" => "data"})
      end
    end
  end

  describe "new/1" do
    test "creates a new Req request with Endato API configuration" do
      request = Endato.new(url: "/PropertyV2Search")

      assert is_struct(request, Req.Request)
      assert request.options[:base_url] == "https://devapi.enformion.com"
      assert request.url.path == "/PropertyV2Search"

      # Check that authentication headers are present
      assert request.headers["galaxy-ap-name"] == ["fake_api_token_name"]
      assert request.headers["galaxy-ap-password"] == ["fake_api_token"]
      assert request.headers["content-type"] == ["application/json"]
    end

    test "merges custom options with default options" do
      request = Endato.new(url: "/PropertyV2Search", params: [limit: 10])

      assert is_struct(request, Req.Request)
      assert request.options[:base_url] == "https://devapi.enformion.com"
      assert request.url.path == "/PropertyV2Search"
      assert request.options[:params] == [limit: 10]
    end
  end
end
