defmodule Boring.ServicesTest do
  use Boring.DataCase, async: true

  import Ash.Generator, only: [generate: 1, generate_many: 2]

  alias Boring.Acquisitions.Generator, as: AcquisitionsGen
  alias Boring.Api.SkiptraceService
  alias Boring.Api.SkiptraceService.Result
  alias Boring.Orgs
  alias Boring.Services
  alias Boring.Services.Generator, as: ServicesGen
  alias PetalPro.Accounts.UserSeeder

  setup do
    enable_feature("service_request_skiptrace")

    # Generate an org with a valid active subscription and a current member
    org = generate(Orgs.Generator.subscribed_org())
    user = UserSeeder.random_user()
    generate(Orgs.Generator.org_membership(org_id: org.id, user_id: user.id))

    %{org: org, user: user}
  end

  test "global multitenancy is enabled for service requests" do
    assert Boring.Test.multitenancy_enabled?(Services.ServiceRequest)
    assert Boring.Test.multitenancy_global?(Services.ServiceRequest)
  end

  describe "list_service_requests/0" do
    test "returns all service requests", data do
      count_of_requests = 10

      generate_many(ServicesGen.service_request(tenant: data.org), count_of_requests)

      results = Services.list_service_requests!(actor: data.user)
      assert length(results) == count_of_requests
    end
  end

  describe "list_open_service_requests_by_type/1" do
    test "returns all service requests", data do
      type = :skiptrace
      count_of_requests = 5

      # Use seed generator to bypass Ash actions and keep service requests "open"
      generate_many(
        ServicesGen.service_request_seed(type: type, status: :new, org_id: data.org.id),
        count_of_requests
      )

      results =
        Services.list_open_service_requests_by_type!(type, actor: data.user)

      assert length(results) == count_of_requests
    end
  end

  describe "get_service_request/1" do
    test "returns a service requests", data do
      service_request = generate(ServicesGen.service_request(tenant: data.org))

      assert Services.get_service_request!(service_request.id, actor: data.user)
    end
  end

  describe "create_service_request/2" do
    test "creates a service request with opportunity record", data do
      opportunity = generate(AcquisitionsGen.opportunity())

      assert Services.create_service_request!(opportunity, :skiptrace,
               actor: data.user,
               tenant: data.org
             )
    end

    test "creates a service request with opportunity id", data do
      opportunity = generate(AcquisitionsGen.opportunity())

      assert Services.create_service_request!(opportunity.id, :skiptrace,
               actor: data.user,
               tenant: data.org
             )
    end

    test "fetches skip trace data from the SkiptraceService", data do
      Mimic.copy(SkiptraceService)

      opportunity = generate(AcquisitionsGen.opportunity())

      # Mock the SkiptraceService to return contact data
      expect(SkiptraceService, :run_skiptrace, fn _service_request ->
        {:ok,
         %Result{
           contacts: [
             %Result.Contact{
               name: "Test Person",
               emails: ["<EMAIL>"],
               phone_numbers: [],
               age: nil,
               address: %{
                 street: nil,
                 city: nil,
                 state: nil,
                 postal_code: nil
               },
               is_current_owner?: true,
               is_corporation_or_business?: false
             }
           ],
           raw_result: nil
         }}
      end)

      service_request =
        opportunity.id
        |> Services.create_service_request!(:skiptrace,
          actor: data.user,
          tenant: data.org
        )
        |> Ash.reload!(
          actor: data.user,
          tenant: data.org
        )

      assert service_request.status == :done

      %{opportunity_contacts: ocs} =
        Ash.load!(opportunity, [opportunity_contacts: :contact], tenant: data.org)

      assert length(ocs) == 1
      assert hd(ocs).contact.name == "Test Person"
      assert hd(ocs).contact.skip_traced? == true

      verify!()
    end
  end
end
