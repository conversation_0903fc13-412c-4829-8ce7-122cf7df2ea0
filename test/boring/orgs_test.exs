defmodule Boring.OrgsTest do
  use Boring.DataCase, async: false

  import Ash.Generator, only: [generate: 1, generate_many: 2]

  alias Ash.Error.Invalid
  alias Boring.Acquisitions
  alias Boring.Acquisitions.Generator, as: AcquisitionsGen
  alias Boring.Billing
  alias Boring.Orgs
  alias PetalPro.Accounts.UserSeeder

  describe "policies" do
    def setup_users do
      %{
        admin: UserSeeder.admin(),
        confirmed_user: UserSeeder.random_user(%{confirmed_at: Timex.to_naive_datetime(DateTime.utc_now())}),
        unconfirmed_user: UserSeeder.random_user()
      }
    end

    test "admins can read all orgs" do
      admin = UserSeeder.admin()
      generate_many(Orgs.Generator.org(), 10)

      orgs = Orgs.list_orgs!(actor: admin)

      assert Enum.count(orgs) == 10
    end

    test "users can read all membership orgs" do
      users = setup_users()
      generate_many(Orgs.Generator.org(), 10)
      generate_many(Orgs.Generator.org(actor: users.confirmed_user), 5)

      orgs = Orgs.list_orgs!(actor: users.confirmed_user)

      assert Enum.count(orgs) == 5
    end

    test "non-member users cannot read orgs" do
      users = setup_users()
      generate_many(Orgs.Generator.org(), 10)

      orgs = Orgs.list_orgs!(actor: users.confirmed_user)

      assert Enum.empty?(orgs)
    end

    test "admins and confirmed users can create new orgs" do
      users = setup_users()
      assert Orgs.can_create_org?(users.admin)
      assert Orgs.can_create_org?(users.confirmed_user)
      refute Orgs.can_create_org?(users.unconfirmed_user)
    end

    test "admin and org admins can update orgs" do
      users = setup_users()

      org = generate(Orgs.Generator.org(actor: users.confirmed_user))

      assert Orgs.can_update_org?(users.admin, org)
      assert Orgs.can_update_org?(users.confirmed_user, org)
      refute Orgs.can_update_org?(users.unconfirmed_user, org)
    end

    test "admin and org admins can delete orgs" do
      users = setup_users()

      org = generate(Orgs.Generator.org(actor: users.confirmed_user))

      assert Orgs.can_delete_org?(users.admin, org)
      assert Orgs.can_delete_org?(users.confirmed_user, org)
      refute Orgs.can_delete_org?(users.unconfirmed_user, org)

      PetalPro.Orgs.create_membership(org, users.unconfirmed_user, :member)
      refute Orgs.can_delete_org?(users.unconfirmed_user, org)
    end
  end

  describe "Boring.Orgs.create_org/1-2" do
    test "org sets slug from org name" do
      users = setup_users()
      company_name = Faker.Company.name()
      org = Orgs.create_org!(%{name: company_name}, actor: users.confirmed_user)

      assert org.name == company_name
      assert org.slug == Slug.slugify(company_name)
    end

    test "returns error when org slug is already taken" do
      users = setup_users()
      existing_org = generate(Orgs.Generator.org())

      assert_raise Invalid, ~r/has already been taken/, fn ->
        Orgs.create_org!(%{name: existing_org.name}, actor: users.confirmed_user)
      end
    end

    test "adds actor as org admin when actor is present" do
      %{confirmed_user: actor} = setup_users()

      org =
        %{name: Faker.Company.name()}
        |> Orgs.create_org!(actor: actor)
        |> Ash.load!(:memberships)

      [membership | _] = org.memberships
      assert membership.user_id == actor.id
      assert membership.role == :admin
    end
  end

  describe "Boring.Orgs.update_org/2-3" do
    test "org slug is updated when name is changed" do
      existing_org = generate(Orgs.Generator.org())

      updated = Orgs.update_org!(existing_org, %{name: Faker.Company.name()}, authorize?: false)

      assert updated.name != existing_org.name
      assert updated.slug != existing_org.slug
    end

    test "returns error when org slug is taken" do
      orgs = generate_many(Orgs.Generator.org(), 2)

      existing_org = Enum.at(orgs, 0)
      updating_org = Enum.at(orgs, 1)

      assert_raise Invalid, ~r/has already been taken/, fn ->
        Orgs.update_org!(
          updating_org,
          %{name: existing_org.name},
          authorize?: false
        )
      end
    end
  end

  describe "Boring.Orgs.delete_org/2-3" do
    test "destroys and existing org" do
      orgs = generate(Orgs.Generator.org())

      assert :ok == Boring.Orgs.delete_org!(orgs, authorize?: false)
    end
  end

  describe "Boring.Orgs.get_org/1-3" do
    test "returns org with matching slug" do
      org = generate(Orgs.Generator.org())

      assert Orgs.get_org!(org.slug, authorize?: false)

      assert_raise Invalid, ~r/record not found/, fn ->
        Orgs.get_org!("fake-slug", authorize?: false)
      end
    end
  end

  describe "Boring.Orgs.get_org_by_id/1-3" do
    test "returns org with matching id" do
      org = generate(Orgs.Generator.org())

      assert Orgs.get_org_by_id!(org.id, authorize?: false)

      assert_raise Invalid, ~r/record not found/, fn ->
        Orgs.get_org_by_id!(Faker.UUID.v4(), authorize?: false)
      end
    end
  end

  describe "manual opportunity assignment" do
    setup [:create_org, :create_opportunities, :create_active_subscription]

    test "assigns opportunities to org using Acquisitions.assign_opportunity", %{
      org: org,
      opportunities: opportunities
    } do
      opportunity_params = [
        %{opportunity_id: opportunities.assignable.id},
        %{opportunity_id: opportunities.non_assignable.id}
      ]

      Acquisitions.assign_opportunity!(opportunity_params,
        tenant: org,
        authorize?: false
      )

      updated_org = Ash.load!(org, :opportunities)
      assert length(updated_org.opportunities) == 2
    end
  end

  describe "remaining_opportunity_entitlement" do
    setup [:create_org]

    test "calculates using opportunities_assigned_this_interval", %{org: org} do
      # Default is 10 opportunities
      create_active_subscription(%{org: org})

      # Get the initial count to account for any existing org_opportunities
      initial_entitlement = Ash.calculate!(org, :remaining_opportunity_entitlement)

      # Create org_opportunities outside the billing period (30 days ago)
      thirty_days_ago = DateTime.add(DateTime.utc_now(), -30, :day)

      generate_many(
        AcquisitionsGen.org_opportunity(org_id: org.id, inserted_at: thirty_days_ago),
        2
      )

      # Older opportunities are not counted
      assert initial_entitlement == Ash.calculate!(org, :remaining_opportunity_entitlement)

      generate_many(AcquisitionsGen.org_opportunity(org_id: org.id), 4)
      assert initial_entitlement - 4 == Ash.calculate!(org, :remaining_opportunity_entitlement)

      generate_many(AcquisitionsGen.org_opportunity(org_id: org.id), 3)
      assert initial_entitlement - 7 == Ash.calculate!(org, :remaining_opportunity_entitlement)

      generate_many(AcquisitionsGen.org_opportunity(org_id: org.id), 4)
      assert initial_entitlement - 11 == Ash.calculate!(org, :remaining_opportunity_entitlement)
    end
  end

  defp create_org(_context) do
    org = generate(Orgs.Generator.org())

    [org: org]
  end

  defp create_active_subscription(%{org: org}) do
    customer = generate(Billing.Generator.customer(source: :org, org_id: org.id))

    # Create subscription with current period dates
    now = DateTime.utc_now()
    period_start = DateTime.add(now, -15, :day)
    period_end = DateTime.add(now, 15, :day)

    generate(
      Billing.Generator.subscription(
        billing_customer_id: customer.id,
        status: "active",
        current_period_start: period_start,
        current_period_end_at: period_end
      )
    )

    [subscriber: org]
  end

  defp create_opportunities(_context) do
    [
      opportunities: %{
        assignable: Acquisitions.Generator.opportunity(),
        non_assignable: Acquisitions.Generator.opportunity()
      }
    ]
  end
end
