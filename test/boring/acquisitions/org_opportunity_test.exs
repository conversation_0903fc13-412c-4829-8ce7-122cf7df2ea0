defmodule Boring.Acquisitions.OrgOpportunityTest do
  use Boring.DataCase, async: true

  import Ash.Generator, only: [generate: 1]

  alias Boring.Acquisitions

  describe "Boring.Acquisitions.favorite_org_opportunity/2" do
    test "sets favorite? to true" do
      opportunity = generate(Acquisitions.Generator.org_opportunity())

      favorited_opportunity =
        Acquisitions.favorite_org_opportunity!(opportunity,
          tenant: opportunity.org_id
        )

      assert favorited_opportunity.favorite? == true
    end
  end

  describe "Boring.Acquisitions.unfavorite_org_opportunity/2" do
    test "sets favorite? to false" do
      opportunity = generate(Acquisitions.Generator.org_opportunity())

      opportunity =
        Acquisitions.favorite_org_opportunity!(opportunity,
          tenant: opportunity.org_id
        )

      assert opportunity.favorite? == true

      unfavorited_opportunity =
        Acquisitions.unfavorite_org_opportunity!(opportunity,
          tenant: opportunity.org_id
        )

      assert unfavorited_opportunity.favorite? == false
    end
  end
end
