defmodule Boring.Acquisitions.OpportunityTest do
  use Boring.DataCase, async: true

  import Ash.Generator, only: [generate: 1]

  alias Boring.Acquisitions.Generator, as: AcquisitionsGen
  alias Boring.Orgs.Generator, as: OrgsGen
  alias Boring.Services.Generator, as: ServicesGen
  alias PetalPro.Accounts.UserSeeder

  describe "update_global_contacts/3" do
    setup do
      enable_feature("service_request_skiptrace")

      org = generate(OrgsGen.subscribed_org())
      opportunity = [org_id: org.id] |> AcquisitionsGen.opportunity() |> generate()

      # Use seed generator to bypass Ash actions and avoid automatic processing
      service_request =
        [org_id: org.id, opportunity_id: opportunity.id, status: :new, type: :skiptrace]
        |> ServicesGen.service_request_seed()
        |> generate()

      admin = UserSeeder.admin()

      %{org: org, opportunity: opportunity, service_request: service_request, admin: admin}
    end

    test "updates the global contacts", data do
      new_contacts = [%{name: "Test", phone_number: "**********", email: "<EMAIL>"}]

      Boring.Acquisitions.update_opportunity_global_contacts!(
        data.opportunity,
        %{global_contacts: new_contacts},
        actor: data.admin
      )

      opportunity = Ash.load!(data.opportunity, :global_contacts)
      assert length(opportunity.global_contacts) == 1
      assert hd(opportunity.global_contacts).name == "Test"
    end

    test "copies the global contacts to the tenant", data do
      # Set up some previously skip-traced data which should be overwritten by the new data
      old_global =
        [skip_traced?: true, org_id: data.org.id]
        |> AcquisitionsGen.contact()
        |> generate()

      [contact_id: old_global.id, opportunity_id: data.opportunity.id, tenant: data.org]
      |> AcquisitionsGen.opportunity_contact()
      |> generate()

      # And then update the global contacts
      Boring.Acquisitions.update_opportunity_global_contacts!(
        data.opportunity,
        %{
          global_contacts: [
            %{
              name: "New Owner",
              email: "<EMAIL>",
              phone_number: "**********"
            }
          ]
        },
        actor: data.admin
      )

      [global_contact] =
        data.opportunity
        |> Ash.load!(:global_contacts, authorize?: false)
        |> Map.fetch!(:global_contacts)

      opp_contacts =
        Ash.load!(data.opportunity, [opportunity_contacts: :contact], tenant: data.org).opportunity_contacts

      assert length(opp_contacts) == 1

      opp_contact = hd(opp_contacts)
      assert opp_contact.contact.name == "New Owner"
      assert opp_contact.contact.global_contact_id == global_contact.id
      assert opp_contact.contact.skip_traced?
    end

    test "can run as a system action with no actor", data do
      Boring.Acquisitions.update_opportunity_global_contacts!(
        data.opportunity,
        %{
          global_contacts: [
            %{
              name: "New Owner",
              email: "<EMAIL>",
              phone_number: "**********"
            }
          ]
        },
        authorize?: false
      )

      opp_contacts =
        Ash.load!(data.opportunity, [opportunity_contacts: :contact], tenant: data.org).opportunity_contacts

      assert length(opp_contacts) == 1
    end
  end
end
