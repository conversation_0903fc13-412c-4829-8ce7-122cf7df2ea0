defmodule PetalProWeb.Features.SignupTest do
  use PetalProWeb.FeatureCase, async: true

  import PetalPro.AccountsFixtures

  test "users can create an account", %{session: session} do
    session
    |> visit(~p"/auth/register")
    |> assert_has("h2", text: "Register")
    |> fill_in("Name", with: "Bob")
    |> fill_in("Email", with: "<EMAIL>")
    |> fill_in("Password", with: "password")
    |> submit()
    |> assert_has("h2", text: "Please confirm your email")
    |> assert_has("#flash_group", text: "Account created successfully!")
    |> assert_path("/auth/confirm")
  end

  test "users get onboarded if user.is_onboarded is false", %{session: session} do
    user = confirmed_user_fixture(%{is_onboarded: false})

    user = Boring.Accounts.get_user_by_id!(user.id, actor: user, load: [:orgs])
    org = Enum.at(user.orgs, 0)

    session
    |> visit(~p"/auth/sign-in")
    |> fill_in("Email", with: user.email)
    |> fill_in("Password", with: "password")
    |> click_button("Sign in")
    |> assert_has("h3", text: "Welcome!")
    |> assert_has("#flash_group", text: "You are now signed in")
    |> fill_in("What is your name?", with: "Jerry", exact: false)
    |> check("Allow marketing notifications")
    |> click_button("Submit")
    |> assert_has("#flash_group", text: "Thank you!")
    |> assert_path("/app/org/#{org.slug}")
  end

  test "users don't get onboarded if user.is_onboarded is true", %{session: session} do
    user = confirmed_user_fixture(%{is_onboarded: true})

    user = Boring.Accounts.get_user_by_id!(user.id, actor: user, load: [:orgs])
    org = Enum.at(user.orgs, 0)

    session
    |> visit(~p"/auth/sign-in")
    |> fill_in("Email", with: user.email)
    |> fill_in("Password", with: "password")
    |> click_button("Sign in")
    |> assert_has("#flash_group", text: "You are now signed in")
    |> assert_path("/app/org/#{org.slug}")
  end
end
