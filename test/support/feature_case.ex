defmodule PetalProWeb.FeatureCase do
  @moduledoc false
  use ExUnit.CaseTemplate

  alias Ecto.Adapters.SQL.Sandbox

  using do
    quote do
      use PetalProWeb, :verified_routes

      import PetalProWeb.FeatureCase
      import PhoenixTest
    end
  end

  setup tags do
    pid = Sandbox.start_owner!(Boring.Repo, shared: not tags[:async])
    on_exit(fn -> Sandbox.stop_owner(pid) end)

    {:ok, session: Phoenix.ConnTest.build_conn()}
  end
end
