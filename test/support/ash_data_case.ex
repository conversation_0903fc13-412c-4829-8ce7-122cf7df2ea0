defmodule Boring.DataCase do
  @moduledoc false
  use ExUnit.CaseTemplate

  alias Ecto.Adapters.SQL.Sandbox

  using do
    quote do
      use Mimic
      use Oban.Testing, repo: Boring.Repo

      import Boring.DataCase

      alias Boring.Repo
    end
  end

  setup tags do
    pid = Sandbox.start_owner!(Boring.Repo, shared: not tags[:async])
    on_exit(fn -> Sandbox.stop_owner(pid) end)
    :ok
  end

  def enable_feature(string) do
    Boring.Features.create_feature!(string, %{enabled?: true}, authorize?: false)
    :ok
  end
end
