defmodule Boring.Test.Mocks.MockSkiptraceProviderFailing do
  @moduledoc """
  Mock implementation of the SkiptraceService.Provider behaviour that returns empty results.
  Used for testing the fallback behavior of the SkiptraceService.
  """
  @behaviour Boring.Api.SkiptraceService.Provider

  @impl true
  def run_skiptrace(_service_request) do
    # Return empty list instead of error to match the updated behavior
    {:ok, []}
  end
end
