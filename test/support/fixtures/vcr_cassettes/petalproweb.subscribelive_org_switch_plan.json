[{"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/subscriptions/sub_1OQrGyIWVkWpNCp709Y6qtlO", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"sub_1OQrGyIWVkWpNCp709Y6qtlO\",\n  \"object\": \"subscription\",\n  \"application\": null,\n  \"application_fee_percent\": null,\n  \"automatic_tax\": {\n    \"enabled\": true\n  },\n  \"billing_cycle_anchor\": **********,\n  \"billing_thresholds\": null,\n  \"cancel_at\": null,\n  \"cancel_at_period_end\": false,\n  \"canceled_at\": null,\n  \"cancellation_details\": {\n    \"comment\": null,\n    \"feedback\": null,\n    \"reason\": null\n  },\n  \"collection_method\": \"charge_automatically\",\n  \"created\": **********,\n  \"currency\": \"aud\",\n  \"current_period_end\": 1706101156,\n  \"current_period_start\": **********,\n  \"customer\": \"cus_PFDXVnCJHqastp\",\n  \"days_until_due\": null,\n  \"default_payment_method\": null,\n  \"default_source\": null,\n  \"default_tax_rates\": [],\n  \"description\": null,\n  \"discount\": null,\n  \"ended_at\": null,\n  \"items\": {\n    \"object\": \"list\",\n    \"data\": [\n      {\n        \"id\": \"si_PFLyQcSVGgyLvO\",\n        \"object\": \"subscription_item\",\n        \"billing_thresholds\": null,\n        \"created\": **********,\n        \"metadata\": {},\n        \"plan\": {\n          \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n          \"object\": \"plan\",\n          \"active\": true,\n          \"aggregate_usage\": null,\n          \"amount\": 199,\n          \"amount_decimal\": \"199\",\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"interval\": \"month\",\n          \"interval_count\": 1,\n          \"livemode\": false,\n          \"metadata\": {},\n          \"nickname\": null,\n          \"product\": \"prod_PFDZyFfhgGUNOg\",\n          \"tiers_mode\": null,\n          \"transform_usage\": null,\n          \"trial_period_days\": null,\n          \"usage_type\": \"licensed\"\n        },\n        \"price\": {\n          \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n          \"object\": \"price\",\n          \"active\": true,\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"custom_unit_amount\": null,\n          \"livemode\": false,\n          \"lookup_key\": null,\n          \"metadata\": {},\n          \"nickname\": null,\n          \"product\": \"prod_PFDZyFfhgGUNOg\",\n          \"recurring\": {\n            \"aggregate_usage\": null,\n            \"interval\": \"month\",\n            \"interval_count\": 1,\n            \"trial_period_days\": null,\n            \"usage_type\": \"licensed\"\n          },\n          \"tax_behavior\": \"inclusive\",\n          \"tiers_mode\": null,\n          \"transform_quantity\": null,\n          \"type\": \"recurring\",\n          \"unit_amount\": 199,\n          \"unit_amount_decimal\": \"199\"\n        },\n        \"quantity\": 1,\n        \"subscription\": \"sub_1OQrGyIWVkWpNCp709Y6qtlO\",\n        \"tax_rates\": []\n      }\n    ],\n    \"has_more\": false,\n    \"total_count\": 1,\n    \"url\": \"/v1/subscription_items?subscription=sub_1OQrGyIWVkWpNCp709Y6qtlO\"\n  },\n  \"latest_invoice\": \"in_1OQrGyIWVkWpNCp7Of1uSwJe\",\n  \"livemode\": false,\n  \"metadata\": {},\n  \"next_pending_invoice_item_invoice\": null,\n  \"on_behalf_of\": null,\n  \"pause_collection\": null,\n  \"payment_settings\": {\n    \"payment_method_options\": null,\n    \"payment_method_types\": null,\n    \"save_default_payment_method\": \"off\"\n  },\n  \"pending_invoice_item_interval\": null,\n  \"pending_setup_intent\": null,\n  \"pending_update\": null,\n  \"plan\": {\n    \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n    \"object\": \"plan\",\n    \"active\": true,\n    \"aggregate_usage\": null,\n    \"amount\": 199,\n    \"amount_decimal\": \"199\",\n    \"billing_scheme\": \"per_unit\",\n    \"created\": **********,\n    \"currency\": \"aud\",\n    \"interval\": \"month\",\n    \"interval_count\": 1,\n    \"livemode\": false,\n    \"metadata\": {},\n    \"nickname\": null,\n    \"product\": \"prod_PFDZyFfhgGUNOg\",\n    \"tiers_mode\": null,\n    \"transform_usage\": null,\n    \"trial_period_days\": null,\n    \"usage_type\": \"licensed\"\n  },\n  \"quantity\": 1,\n  \"schedule\": null,\n  \"start_date\": **********,\n  \"status\": \"active\",\n  \"test_clock\": null,\n  \"transfer_data\": null,\n  \"trial_end\": null,\n  \"trial_settings\": {\n    \"end_behavior\": {\n      \"missing_payment_method\": \"create_invoice\"\n    }\n  },\n  \"trial_start\": null\n}", "headers": {"Server": "nginx", "Date": "Wed, 27 Dec 2023 08:37:01 GMT", "Content-Type": "application/json", "Content-Length": "4011", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET,HEAD,PUT,PATCH,POST,DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "report-uri https://q.stripe.com/csp-report?p=v1%2Fsubscriptions%2F%3Asubscription_exposed_id; block-all-mixed-content; default-src 'none'; base-uri 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'", "Request-Id": "req_Ex2rnkeMtohPde", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "customer=cus_PFDXVnCJHqastp&flow_data%5Btype%5D=subscription_update_confirm&flow_data%5Bafter_completion%5D%5Btype%5D=redirect&flow_data%5Bafter_completion%5D%5Bredirect%5D%5Breturn_url%5D=http%3A%2F%2Flocalhost%3A4002%2Fapp%2Fsubscribe&flow_data%5Bsubscription_update_confirm%5D%5Bitems%5D%5B0%5D%5Bid%5D=si_PFLyQcSVGgyLvO&flow_data%5Bsubscription_update_confirm%5D%5Bitems%5D%5B0%5D%5Bprice%5D=price_1OQj8pIWVkWpNCp74VstFtnd&flow_data%5Bsubscription_update_confirm%5D%5Bitems%5D%5B0%5D%5Bquantity%5D=1&flow_data%5Bsubscription_update_confirm%5D%5Bsubscription%5D=sub_1OQrGyIWVkWpNCp709Y6qtlO", "url": "https://api.stripe.com/v1/billing_portal/sessions", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Idempotency-Key": "2uia6pmhf7cfbk14gg0001m3", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "post", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"bps_1ORsbpIWVkWpNCp7mOOFGAi0\",\n  \"object\": \"billing_portal.session\",\n  \"configuration\": \"bpc_1LwAeVIWVkWpNCp7GeTAHWm3\",\n  \"created\": 1703666221,\n  \"customer\": \"cus_PFDXVnCJHqastp\",\n  \"flow\": {\n    \"after_completion\": {\n      \"hosted_confirmation\": null,\n      \"redirect\": {\n        \"return_url\": \"http://localhost:4002/app/subscribe\"\n      },\n      \"type\": \"redirect\"\n    },\n    \"subscription_cancel\": null,\n    \"subscription_update\": null,\n    \"subscription_update_confirm\": {\n      \"discounts\": null,\n      \"items\": [\n        {\n          \"id\": \"si_PFLyQcSVGgyLvO\",\n          \"price\": \"price_1OQj8pIWVkWpNCp74VstFtnd\",\n          \"quantity\": 1\n        }\n      ],\n      \"subscription\": \"sub_1OQrGyIWVkWpNCp709Y6qtlO\"\n    },\n    \"type\": \"subscription_update_confirm\"\n  },\n  \"livemode\": false,\n  \"locale\": null,\n  \"on_behalf_of\": null,\n  \"return_url\": null,\n  \"url\": \"https://billing.stripe.com/p/session/test_YWNjdF8xS1FOeVVJV1ZrV3BOQ3A3LF9QR1BRM1NWMnNIQzI4akpCSnVkZGNPakZuTEh4WVJL0100cRffn36H/flow\"\n}", "headers": {"Server": "nginx", "Date": "Wed, 27 Dec 2023 08:37:01 GMT", "Content-Type": "application/json", "Content-Length": "1010", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET,HEAD,PUT,PATCH,POST,DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "report-uri https://q.stripe.com/csp-report?p=v1%2Fbilling_portal%2Fsessions; block-all-mixed-content; default-src 'none'; base-uri 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'", "Idempotency-Key": "2uia6pmhf7cfbk14gg0001m3", "Original-Request": "req_oUDUYNEBDE1jsh", "Request-Id": "req_oUDUYNEBDE1jsh", "Stripe-Should-Retry": "false", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}]