[{"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "email=user-576460752303413243%40example.com", "url": "https://api.stripe.com/v1/customers", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Idempotency-Key": "2uia6pqhij461c90ao000a66", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "post", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"cus_PGPQldKXTgk4Ih\",\n  \"object\": \"customer\",\n  \"address\": null,\n  \"balance\": 0,\n  \"created\": 1703666223,\n  \"currency\": null,\n  \"default_source\": null,\n  \"delinquent\": false,\n  \"description\": null,\n  \"discount\": null,\n  \"email\": \"<EMAIL>\",\n  \"invoice_prefix\": \"019C7AD7\",\n  \"invoice_settings\": {\n    \"custom_fields\": null,\n    \"default_payment_method\": null,\n    \"footer\": null,\n    \"rendering_options\": null\n  },\n  \"livemode\": false,\n  \"metadata\": {},\n  \"name\": null,\n  \"next_invoice_sequence\": 1,\n  \"phone\": null,\n  \"preferred_locales\": [],\n  \"shipping\": null,\n  \"tax_exempt\": \"none\",\n  \"test_clock\": null\n}", "headers": {"Server": "nginx", "Date": "Wed, 27 Dec 2023 08:37:03 GMT", "Content-Type": "application/json", "Content-Length": "647", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET,HEAD,PUT,PATCH,POST,DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "report-uri https://q.stripe.com/csp-report?p=v1%2Fcustomers; block-all-mixed-content; default-src 'none'; base-uri 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'", "Idempotency-Key": "2uia6pqhij461c90ao000a66", "Original-Request": "req_Jnsa4IVfgpONl7", "Request-Id": "req_Jnsa4IVfgpONl7", "Stripe-Should-Retry": "false", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "mode=subscription&allow_promotion_codes=true&customer=cus_PGPQldKXTgk4Ih&success_url=http%3A%2F%2Flocalhost%3A4002%2Fapp%2Fsubscribe%2Fsuccess%3Fcustomer_id%3D301&cancel_url=http%3A%2F%2Flocalhost%3A4002%2Fapp%2Fsubscribe&line_items%5B0%5D%5Bprice%5D=price_1OQj8TIWVkWpNCp7ZlUSOaI9&line_items%5B0%5D%5Bquantity%5D=1&client_reference_id=301&subscription_data%5Bmetadata%5D%5Bsource%5D=user&subscription_data%5Bmetadata%5D%5Bsource_id%5D=1942", "url": "https://api.stripe.com/v1/checkout/sessions", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Idempotency-Key": "2uia6pr5usjgpc90ao000a86", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "post", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"cs_test_b1UhKsRz3NG8TVmhc2VDVAScP4tLSd5J0s9jPT2G69Chenh7V1YZFXFlkK\",\n  \"object\": \"checkout.session\",\n  \"after_expiration\": null,\n  \"allow_promotion_codes\": true,\n  \"amount_subtotal\": 199,\n  \"amount_total\": 199,\n  \"automatic_tax\": {\n    \"enabled\": false,\n    \"status\": null\n  },\n  \"billing_address_collection\": null,\n  \"cancel_url\": \"http://localhost:4002/app/subscribe\",\n  \"client_reference_id\": \"301\",\n  \"client_secret\": null,\n  \"consent\": null,\n  \"consent_collection\": null,\n  \"created\": 1703666224,\n  \"currency\": \"aud\",\n  \"currency_conversion\": null,\n  \"custom_fields\": [],\n  \"custom_text\": {\n    \"after_submit\": null,\n    \"shipping_address\": null,\n    \"submit\": null,\n    \"terms_of_service_acceptance\": null\n  },\n  \"customer\": \"cus_PGPQldKXTgk4Ih\",\n  \"customer_creation\": null,\n  \"customer_details\": {\n    \"address\": null,\n    \"email\": \"<EMAIL>\",\n    \"name\": null,\n    \"phone\": null,\n    \"tax_exempt\": \"none\",\n    \"tax_ids\": null\n  },\n  \"customer_email\": null,\n  \"expires_at\": 1703752623,\n  \"invoice\": null,\n  \"invoice_creation\": null,\n  \"livemode\": false,\n  \"locale\": null,\n  \"metadata\": {},\n  \"mode\": \"subscription\",\n  \"payment_intent\": null,\n  \"payment_link\": null,\n  \"payment_method_collection\": \"always\",\n  \"payment_method_configuration_details\": {\n    \"id\": \"pmc_1KQkIYIWVkWpNCp7P1tL9RwN\",\n    \"parent\": null\n  },\n  \"payment_method_options\": null,\n  \"payment_method_types\": [\n    \"card\",\n    \"link\"\n  ],\n  \"payment_status\": \"unpaid\",\n  \"phone_number_collection\": {\n    \"enabled\": false\n  },\n  \"recovered_from\": null,\n  \"setup_intent\": null,\n  \"shipping_address_collection\": null,\n  \"shipping_cost\": null,\n  \"shipping_details\": null,\n  \"shipping_options\": [],\n  \"status\": \"open\",\n  \"submit_type\": null,\n  \"subscription\": null,\n  \"success_url\": \"http://localhost:4002/app/subscribe/success?customer_id=301\",\n  \"total_details\": {\n    \"amount_discount\": 0,\n    \"amount_shipping\": 0,\n    \"amount_tax\": 0\n  },\n  \"ui_mode\": \"hosted\",\n  \"url\": \"https://checkout.stripe.com/c/pay/cs_test_b1UhKsRz3NG8TVmhc2VDVAScP4tLSd5J0s9jPT2G69Chenh7V1YZFXFlkK#fid2cGd2ZndsdXFsamtQa2x0cGBrYHZ2QGtkZ2lgYSc%2FY2RpdmApJ2R1bE5gfCc%2FJ3VuWnFgdnFaMDROVEt8UExSU25SdUtGdTJSPDJJZ2FTdTdHZ0IyS2BIMGQ3XDRgPE1cSH1HbVFkSj1oXU5OQ01zUEN9V3VvRlM8ZzZ1fVVcQ1ZSTH9LYDNxN1FcVmsxQDM1NUdkYTEwYDM2JyknY3dqaFZgd3Ngdyc%2FcXdwYCknaWR8anBxUXx1YCc%2FJ2hwaXFsWmxxYGgnKSdga2RnaWBVaWRmYG1qaWFgd3YnP3F3cGB4JSUl\"\n}", "headers": {"Server": "nginx", "Date": "Wed, 27 Dec 2023 08:37:04 GMT", "Content-Type": "application/json", "Content-Length": "2406", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET,HEAD,PUT,PATCH,POST,DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "report-uri https://q.stripe.com/csp-report?p=v1%2Fcheckout%2Fsessions; block-all-mixed-content; default-src 'none'; base-uri 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'", "Idempotency-Key": "2uia6pr5usjgpc90ao000a86", "Original-Request": "req_1eoqJ3gZc09naU", "Request-Id": "req_1eoqJ3gZc09naU", "Stripe-Should-Retry": "false", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}]