defmodule FakeReq do
  @moduledoc "Used to stub out calls to <PERSON><PERSON> by default in tests"

  def request!(request, options) do
    {:ok, response} = request(request, options)
    response
  end

  def request(request, _options) do
    status = determine_status_code(request)
    {:ok, %Req.Response{status: status, body: []}}
  end

  # Determine appropriate status code based on request URL or other properties
  defp determine_status_code(request) do
    url = get_request_url(request)

    cond do
      # Apify API endpoints should return 201
      String.contains?(url, "apify") -> 201
      # Endato API endpoints should return 200
      String.contains?(url, "endato") -> 200
      # Google API endpoints should return 200
      String.contains?(url, "googleapis.com") -> 200
      # Default to 201 for backward compatibility (original FakeReq behavior)
      true -> 201
    end
  end

  # Extract URL from request (handles both string URLs and Req.Request structs)
  defp get_request_url(%Req.Request{url: url}), do: to_string(url)
  defp get_request_url(url) when is_binary(url), do: url
  defp get_request_url(_), do: ""
end
