# Git Hooks

This directory contains git hooks that are shared across the team to ensure consistent code quality and commit standards.

## Available Hooks

### pre-commit
- **Purpose**: Automatically runs `mix format` before each commit
- **Behavior**: 
  - Formats all Elixir files using `mix format`
  - If files are modified by formatting, automatically stages the changes
  - Prevents commit if formatting fails
  - Provides clear feedback about what's happening

### commit-msg
- **Purpose**: Validates commit messages follow Conventional Commits specification
- **Behavior**: Uses `mix git_ops.check_message` to ensure commit messages are properly formatted

## Installation

To install these hooks on your local machine, run the setup script from the project root:

```bash
./scripts/setup-hooks
```

This will:
1. Copy all hooks from `scripts/hooks/` to `.git/hooks/`
2. Make them executable
3. Show you which hooks were installed

## Updating Hooks

When hooks are updated in the repository, team members can run the setup script again to get the latest versions:

```bash
./scripts/setup-hooks
```

## For New Team Members

Add this to your onboarding checklist:
1. Clone the repository
2. Run `mix setup` (if not already done)
3. Run `./scripts/setup-hooks` to install git hooks

## Bypassing Hooks (Emergency Use Only)

If you need to bypass hooks in an emergency (not recommended), you can use:

```bash
git commit --no-verify -m "emergency commit message"
```

## Adding New Hooks

To add a new hook:
1. Create the hook file in `scripts/hooks/`
2. Make it executable: `chmod +x scripts/hooks/your-hook`
3. Test it locally
4. Update this README
5. Commit and push
6. Ask team members to run `./scripts/setup-hooks` to get the new hook

## Troubleshooting

### Hook not running
- Ensure you've run `./scripts/setup-hooks`
- Check that the hook file in `.git/hooks/` is executable: `ls -la .git/hooks/`

### Mix format fails
- Ensure Elixir is installed and `mix` is in your PATH
- Try running `mix format` manually to see the specific error

### Commit message validation fails
- Ensure your commit messages follow Conventional Commits format
- Examples: `feat: add new feature`, `fix: resolve bug`, `docs: update README`
