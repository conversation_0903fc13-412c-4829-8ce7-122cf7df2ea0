#!/bin/sh
#
# A hook script to verify what is about to be committed.
# Called by "git commit" with no arguments. The hook should
# exit with non-zero status after issuing an appropriate message if
# it wants to stop the commit.

# Check if mix is available
if ! command -v mix >/dev/null 2>&1; then
    echo "Error: mix command not found. Please ensure <PERSON>xir is installed and in your PATH."
    exit 1
fi

# Check if we're in an Elixir project
if [ ! -f "mix.exs" ]; then
    echo "Warning: mix.exs not found. Skipping mix format."
    exit 0
fi

echo "Running mix format..."

# Run mix format
if ! mix format; then
    echo "Error: mix format failed. Please fix the formatting issues and try again."
    exit 1
fi

# Check if any files were modified by mix format
# if ! git diff --quiet; then
#     echo "Files were formatted by mix format. Adding changes to commit..."
#     # Add all modified files to the staging area
#     git add -u
#     echo "Formatted files have been staged. Proceeding with commit."
# fi

echo "Pre-commit hook completed successfully."
exit 0
