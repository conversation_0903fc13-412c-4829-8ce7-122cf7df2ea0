#!/bin/bash
#
# Setup script to install git hooks from the repository
# Run this script from the project root: ./scripts/setup-hooks

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
HOOKS_DIR="$PROJECT_ROOT/.git/hooks"
SOURCE_HOOKS_DIR="$PROJECT_ROOT/scripts/hooks"

echo "Setting up git hooks..."

# Check if we're in a git repository
if [ ! -d "$PROJECT_ROOT/.git" ]; then
    echo "Error: Not in a git repository. Please run this script from the project root."
    exit 1
fi

# Check if source hooks directory exists
if [ ! -d "$SOURCE_HOOKS_DIR" ]; then
    echo "Error: Source hooks directory not found at $SOURCE_HOOKS_DIR"
    exit 1
fi

# Create hooks directory if it doesn't exist
mkdir -p "$HOOKS_DIR"

# Copy and install each hook
for hook_file in "$SOURCE_HOOKS_DIR"/*; do
    if [ -f "$hook_file" ]; then
        hook_name=$(basename "$hook_file")
        target_file="$HOOKS_DIR/$hook_name"
        
        echo "Installing $hook_name hook..."
        
        # Copy the hook
        cp "$hook_file" "$target_file"
        
        # Make it executable
        chmod +x "$target_file"
        
        echo "✓ $hook_name hook installed"
    fi
done

echo ""
echo "Git hooks setup complete! 🎉"
echo ""
echo "The following hooks are now active:"
for hook_file in "$SOURCE_HOOKS_DIR"/*; do
    if [ -f "$hook_file" ]; then
        hook_name=$(basename "$hook_file")
        echo "  - $hook_name"
    fi
done
echo ""
echo "To update hooks in the future, simply run this script again."
