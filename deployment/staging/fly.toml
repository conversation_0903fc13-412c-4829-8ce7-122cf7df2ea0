# fly.toml app configuration file generated for boring-staging on 2025-02-13T22:13:18-06:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'boring-staging'
primary_region = 'iad'
kill_signal = 'SIGTERM'

[deploy]
release_command = '/app/bin/migrate'
strategy = "immediate"
max_unavailable = 1

[env]
PHX_HOST = 'boring-staging.fly.dev'
PORT = '8080'
DNS_CLUSTER_QUERY = "boring-staging.internal"
MAPBOX_DOWNLOAD_IMAGE_PATH = ""
MAPBOX_STATIC_IMAGE_PATH = "https://boring-business-staging-mapbox-assets.fly.storage.tigris.dev/"
MAPBOX_IO_BACKEND = "S3_OPS"

[http_service]
internal_port = 8080
force_https = true
auto_stop_machines = 'suspend'
auto_start_machines = true
min_machines_running = 1
processes = ['app']

[[http_service.checks]]
processes = ["app"]

[http_service.concurrency]
type = 'connections'
hard_limit = 1000
soft_limit = 975

[[vm]]
memory = '2gb'
cpu_kind = 'shared'
cpus = 1
